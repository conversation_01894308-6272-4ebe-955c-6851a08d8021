package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.cache.CacheTableName;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.SplitTableConfig;
import org.jooq.Record;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;

/**
 * add by wdy
 * 1000条更新一次数据
 */
@Component
public class CourseProgressVersionUpdateListener extends AbstractMessageListener {
    private static Logger logger = LoggerFactory.getLogger(CourseProgressVersionUpdateListener.class);
    private CommonDao<CourseStudyProgress> progressCommonDao;
    private MessageSender messageSender;
    @Resource
    private CourseCacheService courseCacheService;

    @Autowired
    public void setProgressCommonDao(CommonDao<CourseStudyProgress> progressCommonDao) {
        this.progressCommonDao = progressCommonDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    protected void onMessage(Message message) {
        String progressIds = message.getHeader(MessageHeaderContent.IDS);
        String versionId = message.getHeader(MessageHeaderContent.ID);
        String paramsIds = message.getHeader(MessageHeaderContent.PARAMS);
        String[] ids = progressIds.split(",");
        String[] memberIds = paramsIds.split(",");

        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String memberId : memberIds) {
            TableImpl<?> cacheTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));
            tableMap.put(cacheTable.getName(), cacheTable);
        }
        Integer count = 0;
        Map<String, String> map = new HashMap<>();
        for (Map.Entry<String, TableImpl<?>> stringTableEntry : tableMap.entrySet()) {
            TableImpl<?> table = stringTableEntry.getValue();
            count += progressCommonDao.execute(x -> x.update(table).set(table.field("t_course_version_id",String.class), versionId)
                    .set(table.field("f_last_modify_time",Long.class), System.currentTimeMillis()).where(table.field("f_id",String.class).in(ids)).execute()
            );
            map = progressCommonDao.execute(dsl -> dsl
                    .select(table.field("f_id", String.class), table.field("f_member_id", String.class))
                    .from(table)
                    .where(table.field("f_id", String.class).in(ids))
                    .fetchMap(table.field("f_id", String.class), table.field("f_member_id", String.class)));
        }

        // add 2020-4-24 异步更新studyProgress分表数据
        for (Map.Entry<String, String> entry : map.entrySet()) {
            messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
                    MessageHeaderContent.ID, entry.getKey(),MessageHeaderContent.MEMBER_ID, entry.getValue()
            );
        }
        logger.info("修改条数， count={}， 修改结束时间time={}", count, System.currentTimeMillis());
    }

    @Override
    public int[] getTypes() {
        return new int[] { MessageTypeContent.COURSE_STUDY_PROGRESS_VERSION_CHANGE };
    }
}
