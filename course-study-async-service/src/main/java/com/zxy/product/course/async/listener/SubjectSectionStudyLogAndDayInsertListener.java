package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.cache.CacheTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.entity.SubjectSectionStudyLog;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

@Component
public class SubjectSectionStudyLogAndDayInsertListener extends AbstractMessageListener {
    private static Logger logger = LoggerFactory.getLogger(SubjectSectionStudyLogAndDayInsertListener.class);

    @Autowired
    private CommonDao<CourseInfo> courseInfoDao;

    @Autowired
    private CommonDao<SubjectSectionStudyLog> subjectLogDao;

    private CourseCacheService courseCacheService;

    @Autowired
    private CacheTableName tableName;

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Override
    protected void onMessage(Message message) {

        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        String subjectId = message.getHeader(MessageHeaderContent.COURSE_ID);
        String sectionId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        String status = message.getHeader(MessageHeaderContent.PARAMS);
        logger.info("人-专题-天表数据更新，memberId={}， sectionId={}, status={}", memberId, sectionId, status);
//        Integer finishStatus = Integer.valueOf(ObjectUtils.isEmpty(status) ? "0" : status);
        String studyTime = message.getHeader(MessageHeaderContent.STUDYTIME);
        Integer sectionStudyTime = Integer.valueOf(ObjectUtils.isEmpty(studyTime) ? "0" : studyTime);
        Optional<String> timeOptional = Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME));
        Long createTime = timeOptional.isPresent() ? Long.valueOf(timeOptional.get()) : System.currentTimeMillis();
        courseInfoDao.getOptional(subjectId)
                .filter(c -> CourseInfo.BUSINESS_TYPE_SUBJECT.equals(c.getBusinessType()))
                .ifPresent(c -> {
                    // update 2019-11-29将log更新去掉，专题log表已无用
//                    SubjectSectionStudyLog log = new SubjectSectionStudyLog();
//                    log.forInsert();
//                    log.setMemberId(memberId);
//                    log.setSubjectId(subjectId);
//                    log.setSectionId(sectionId);
//                    log.setFinishStatus(finishStatus);
//                    log.setStudyTime(sectionStudyTime);
//                    log.setCreateTime(createTime);
//                    subjectLogDao.insert(log);
//                    logger.info("本次专题流水新加的时长， studyTime={}, memberId={}, subjectId= {}", studyTime, memberId,
//                            subjectId);
//                    // add by Acong 发送subjectLog分表同步消息
//                    messageSender.send(MessageTypeContent.SPLIT_SUBJECT_SECTION_STUDY_LOG_INSERT,
//                            MessageHeaderContent.ID, log.getId());
                    insertOrUpdateSubjectStudyLogDay(memberId, subjectId, sectionStudyTime, createTime);
                });
    }

    @Override
    public int[] getTypes() {
        return new int[] {
                MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY
        };
    }

    private void insertOrUpdateSubjectStudyLogDay(String memberId, String subjectId, int studyTime,
                                                  Long createTime) {
        // 如果时长为0 不修改数据
        logger
                .info("本次专题更新人-专题-天时长为，studyTime={}, subjectId={}, memberId={}, currentTime={}", studyTime,
                        subjectId, memberId, System.currentTimeMillis());
//		if (studyTime == 0) return ;

        String path = courseCacheService.getPath(memberId);
        TableImpl<?> table = tableName
                .getCacheTable(path, SplitTableConfig.SUBJECT_STUDY_LOG_DAY);
        String date = this.changeTimeStyle(createTime);
        // 将date转换为年  月  日
        int year = Integer.parseInt(date.substring(0, 4));// 只有年
        int month = Integer.parseInt(date.substring(0, 6)); // 包括年月
        int day = Integer.parseInt(date);// 包括年月日
        SubjectSectionStudyLog subjectLog = subjectLogDao
                .execute(x -> x.select(table.field("f_id", String.class))
                        .from(table)
                        .where(table.field("f_subject_id", String.class).eq(subjectId)
                                .and(table.field("f_member_id", String.class).eq(memberId))
                                .and(table.field("f_day", Integer.class).eq(day)))
                        .limit(1))
                .fetchOptional(r -> {
                    SubjectSectionStudyLog log = new SubjectSectionStudyLog();
                    log.setId(r.getValue("f_id").toString());
                    return log;
                }).orElseGet(() -> {
                    SubjectSectionStudyLog log = new SubjectSectionStudyLog();
                    log.setMemberId(memberId);
                    log.setSubjectId(subjectId);

                    return log;
                });

        if(subjectLog.getId() == null) {
            // 赋值年月日
            subjectLog.setYear(year);
            subjectLog.setMonth(month);
            subjectLog.setDay(day);
            subjectLog.forInsert();
            subjectLogDao.execute(x ->
                    x.insertInto(table, table.field("f_id", String.class),
                            table.field("f_member_id", String.class),
                            table.field("f_subject_id", String.class),
                            table.field("f_study_time", Integer.class),
                            table.field("f_day", Integer.class),
                            table.field("f_month", Integer.class),
                            table.field("f_year", Integer.class),
                            table.field("f_create_time", Long.class))
                            .values(subjectLog.getId(),
                                    subjectLog.getMemberId(),
                                    subjectLog.getSubjectId(),
                                    studyTime,
                                    subjectLog.getDay(),
                                    subjectLog.getMonth(),
                                    subjectLog.getYear(),
                                    subjectLog.getCreateTime())
                            .execute()
            );
        } else {
            subjectLogDao.execute(x ->
                    x.update(table)
                            .set(table.field("f_study_time", Integer.class),
                                    DSL.nvl(table.field("f_study_time", Integer.class), 0).add(studyTime))
                            .where(table.field("f_id", String.class).eq(subjectLog.getId())).execute()
            );
        }

    }

    private String changeTimeStyle(Long createTime) {
        return new SimpleDateFormat("yyyyMMdd").format(new Date(createTime));
    }
}

