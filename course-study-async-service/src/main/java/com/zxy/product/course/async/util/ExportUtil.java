package com.zxy.product.course.async.util;

import com.zxy.common.fastdfs.support.pool.DefaultKeyTrackerServerPool;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.AbstractExcelWrite;
import com.zxy.product.human.api.FileService;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.poi.util.IOUtils;
import org.csource.fastdfs.StorageClient;
import org.csource.fastdfs.TrackerServer;
import org.csource.fastdfs.UploadCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/20 16:34
 * @description：导出文件流工具
 * @modified By：
 */
@Component
public class ExportUtil {
    private static final Logger logger = LoggerFactory.getLogger(ExportUtil.class);
    public static final String DEFAULT_GROUP = "default";
    private static final String FILE_SEPARATOR = "/";
    private GenericObjectPool<TrackerServer> trackerServerPool;
    private FileService fileService;

    @Autowired
    public void setTrackerServerPool(GenericObjectPool<TrackerServer> trackerServerPool) {
        this.trackerServerPool = trackerServerPool;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    /**
     * 文件存储
     *
     * @param is
     * @param fileName
     * @param companyId
     * @param saveFlag
     * @return
     * @throws IOException
     */
    public com.zxy.product.human.entity.Attachment store(InputStream is, String fileName, String companyId, Boolean saveFlag) throws IOException {
        com.zxy.product.human.entity.Attachment attachment = new com.zxy.product.human.entity.Attachment();
        Writer.FileInfo fileInfo = store(is, fileName);
        attachment.forInsert();
        attachment.setFilename(fileInfo.getFileName());
        attachment.setPath(fileInfo.getPath());
        attachment.setSize(Long.valueOf(fileInfo.getSize()));
        attachment.setContentType(AbstractExcelWrite.EXCEL_CONTENT_TYPE);
        if (saveFlag) {
            fileService.insert(attachment);
        }
        return attachment;
    }


    public Writer.FileInfo store(InputStream is, String fileName) throws IOException {
        int size = is.available();

        UploadCallback callback = out -> {
            IOUtils.copy(is, out);
            return 0;
        };
        String ext = null;

        int index = fileName.lastIndexOf('.');
        if (index != -1 && index < fileName.length() - 1) {
            ext = fileName.substring(index + 1);
        }

        TrackerServer trackerServer = null;
        String[] parts = null;
        try {
            trackerServer = trackerServerPool.borrowObject();
            parts = new StorageClient(trackerServer, null).upload_file(DEFAULT_GROUP, size, callback, ext, null);
        } catch (Exception e) {
            throw new UnsupportedOperationException("Can not upload file: " + fileName, e);
        } finally {
            trackerServerPool.returnObject(trackerServer);
        }
        if (parts == null) {
            throw new UnsupportedOperationException("Can not upload file to FastDFS, returns null");
        }

        String path = parts[0] + FILE_SEPARATOR + parts[1];
        Writer.FileInfo fileInfo = new Writer.FileInfo();
        fileInfo.setPath(path);
        fileInfo.setSize(size);
        fileInfo.setFileName(fileName);

        return fileInfo;
    }
}
