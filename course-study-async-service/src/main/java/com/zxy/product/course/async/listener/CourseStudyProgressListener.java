package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: TJ
 * @Date: 2016/11/21
 * @ModifyUser: TJ
 * @ModifyDate: 2016/11/21
 */
@Component
public class CourseStudyProgressListener extends AbstractMessageListener {

    private static Logger logger = LoggerFactory.getLogger(CourseStudyProgressListener.class);
    private CourseStudyProgressService courseStudyProgressService;

    @Autowired
    public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
        this.courseStudyProgressService = courseStudyProgressService;
    }

    @Override
    protected void onMessage(Message message) {
        String id = message.getHeader(MessageHeaderContent.ID);
        String studyTime = message.getHeader(MessageHeaderContent.STUDYTIME);
        String clientType = message.getHeader(MessageHeaderContent.STUDYCLIENTTYPE);
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        logger.info("course/CourseStudyProgressListener, {}", message.toString());
        String courseId = message.getHeader(MessageHeaderContent.COURSE_ID);
        Optional<String> timeOptional = Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME));
        Long finishTime = timeOptional.isPresent() ? Long.valueOf(timeOptional.get()) : System.currentTimeMillis();
        logger.info("开始更新学习日志和课程进度, sectionProgressId={}, studyTime= {}, clientType = {}, time",id, studyTime, clientType, finishTime);
        courseStudyProgressService.updateSectionProgressAsync(id, courseId,
                Optional.ofNullable(clientType).map(Integer::parseInt),
                Optional.ofNullable(studyTime).map(Integer::parseInt),
                finishTime,memberId
        );
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.COURSE_PROGRESS_UPDATE
        };
    }

}
