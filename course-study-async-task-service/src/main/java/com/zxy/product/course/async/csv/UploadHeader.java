package com.zxy.product.course.async.csv;

/**
 * <AUTHOR>
 * @create 2024/10/10 15:56
 */
public class UploadHeader {

    public static final String[] COURSE_STUDY_PROGRESS_HEADER = {"courseId", "courseName", "empName", "empNumber", "studyStatus",
        "studyTotalTime", "studyLastTime", "studyCompletionTime", "progressId", "provinceCode"};


    public static final String[] COURSE_STUDY_YEAR_HEADER = {"empNumber", "empName", "yearMonthRange", "monthTotalLearingHours", "learingcoursesNumberY",
            "provinceCode"};

    //专题id ,专题名称,受众类型  01代表组织类 02代表具体人员类,组织编码
    public static final String[] TOPIC_ORG_HEADER = {"topicId", "topicName", "type", "orgCode"};

    //专题id ,专题名称 ,受众类型  01代表组织类 02代表具体人员类,员工网大编号,员工ihr编号,员工姓名
    public static final String[] TOPIC_MEMBER_HEADER = {"topicId", "topicName", "type", "memberName","empNumber","empName"};

     //专题id ,专题名称 ,员工姓名(FullName),员工ihr编号(IHRCode),学习状态,
     // 首次学习时间,完成时间,学习学时(单位:时)
    public static final String[] TOPIC_STUDY_HEADER = {"topicId", "topicName", "empName", "empNumber","status",
             "beginTime","finishTime","learnHour"};

     //专题id ,专题名称 ,员工姓名(FullName),员工ihr编号(IHRCode),考试id,
     // 考试名称,最高考试成绩
    public static final String[] TOPIC_EXAM_HEADER = {"topicId", "topicName", "empName", "empNumber","examId",
             "examName","score"};

}
