package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.async.task.jiutian.CommunityPartyTask;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 触发党建数据同步的后门监听（用于同步全量或后期补偿性同步）
 * 触发会开启线程同步数据，暂没有做频次限制，人为控制其调用频率，短时间频繁调用可能会导致异常
 */
@Component
public class CommunityPartyPushCourseDataListener extends AbstractMessageListener {

    private static final Logger log = LoggerFactory.getLogger(CommunityPartyPushCourseDataListener.class);

    private CommunityPartyTask communityPartyTask;

    @Autowired
    public void setCommunityPartyTask(CommunityPartyTask communityPartyTask) {
        this.communityPartyTask = communityPartyTask;
    }

    @Override
    protected void onMessage(Message message) {
        int type = message.getType();
        String startTime = message.getHeader(MessageHeaderContent.STARTTIME);
        String endTime = message.getHeader(MessageHeaderContent.ENDTIME);
        log.error("type类型={}", type);
        switch (type) {
            case MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_INCREMENT_COMMUNITY:
                if (!StringUtils.isEmpty(startTime)) {
                    Long start = Long.valueOf(startTime);
                    communityPartyTask.uploadCourseLog(Optional.of(start), Optional.of(start));
                }
                break;
            case MessageTypeContent.FTP_UPLOAD_COURSE_INFO_DATA_ALL_COMMUNITY:
                communityPartyTask.uploadCourseInfo(Optional.empty(), Optional.empty());
                break;
            case MessageTypeContent.FTP_UPLOAD_USER_DATA_ALL_COMMUNITY:
                communityPartyTask.uploadMemberInfo(Optional.empty(), Optional.empty());
                break;
            case MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_ALL_COMMUNITY:
                log.info("开始同步数据");
                if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
                    Long start = Long.valueOf(startTime);
                    Long end = Long.valueOf(endTime);
                    log.error("ednTime={}, startTime={}", end, start);
                    communityPartyTask.uploadCourseLog(Optional.of(start), Optional.of(end));
                }
                break;
            default:
                break;
        }
    }


    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_INCREMENT_COMMUNITY,
                MessageTypeContent.FTP_UPLOAD_COURSE_INFO_DATA_ALL_COMMUNITY,
                MessageTypeContent.FTP_UPLOAD_USER_DATA_ALL_COMMUNITY,
                MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_ALL_COMMUNITY
        };
    }
}
