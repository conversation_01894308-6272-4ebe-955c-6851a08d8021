package com.zxy.product.course.async;

import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.course.async.config.*;
import com.zxy.product.course.jooq.CourseStudy;
import org.jooq.Schema;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 16/6/22
 */
@EnableAspectJAutoProxy(proxyTargetClass=true)
@EnableScheduling
@Configuration
@ComponentScan
@Import({
        RPCClientConfig.class,
        CommonDaoConfig.class,
        MessageConfig.class,
        RestTemplateConfig.class,
        GenseeConfig.class,
        DynamicDataSourceConfig.class,
        CacheConfig.class,
        JooqAutoConfiguration.class,
        TransactionConfig.class,
        RabbitAutoConfiguration.class,
        FastDFSConfig.class,
        RestfulConfig.class
})
public class CourseAsyncTaskServiceApplication {
    @Bean
    public Schema schema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    public static void main(String[] args) throws Exception {
    	new SpringApplicationBuilder(CourseAsyncTaskServiceApplication.class).web(false).run(args);

    }
}
