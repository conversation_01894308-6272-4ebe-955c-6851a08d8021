package com.zxy.product.course.async.task.ishow;

import com.zxy.product.course.api.ishow.IShowService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Classname StudyPlanTask
 * @Description ishow学习数据拉取
 * @Date 2024/11/13 10:53
 * @Created by cuiguangping
 */
@Component
public class IShowMemberStudyInfoTask {
    private static final Logger log = LoggerFactory.getLogger(IShowMemberStudyInfoTask.class);
    private IShowService iShowService;

    @Autowired
    public void setIShowService(IShowService iShowService) {
        this.iShowService = iShowService;
    }

    /**
     * 每日凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ? ")
    public void pull() {
        iShowService.invoke();
    }

}
