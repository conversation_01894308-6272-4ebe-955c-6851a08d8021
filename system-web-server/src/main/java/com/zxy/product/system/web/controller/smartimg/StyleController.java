package com.zxy.product.system.web.controller.smartimg;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.api.smartimg.StyleService;
import com.zxy.product.system.entity.CanvasSize;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.Style;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.zxy.product.system.entity.CanvasNewSize;
import com.zxy.product.system.content.SizeEnum;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
* @Description:    样式controller
* @Author:         liuc
* @CreateDate:     2021/6/24 17:04
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
@Controller
@RequestMapping("/style")
public class StyleController {
    protected static final Logger LOGGER = LoggerFactory.getLogger(StyleController.class);

    private StyleService styleService;

    @Autowired
    public void setStyleService(StyleService styleService) {
        this.styleService = styleService;
    }

    /**
     * 查询样式列表
     */
    @RequestMapping(method = RequestMethod.GET)
    @Permitted(perms = {"course-study/style-management","course-study/images-management"})
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "titleLength", type = Integer.class )
    @Param(name = "code")
    @Param(name = "name")
    @Param(name = "size")
    @JSON("recordCount")
    @JSON("items.(id,code,name,titleMin,titleMax,content,materialNum,creater,organizationName,createTime,defaultPath,size,scenarios,canvesType,organizationId)")
    public PagedResult<Style> findPage(RequestContext rc, Subject<Member> subject) {
        return styleService.find(rc.getInteger("page"), rc.getInteger("pageSize"),
                rc.getOptionalInteger("titleLength"),
                rc.getOptionalString("code"),
                rc.getOptionalString("name"),
                rc.getOptionalString("size"),
                subject.getCurrentUser().getOrganizationId());
    }

    /**
     * 新增样式
     */
    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "name", required = true, value = "名称")
    @Param(name = "sizeId", value = "分辨率id")
    @Param(name = "content", required = true, value = "样式内容json")
    @Param(name = "defaultAttachmentId", required = true, value = "默认附件id")
    @Param(name = "defaultPath", required = true, value = "默认附件路径")
    @JSON("id,code,name,titleMin,titleMax,content,materialNum,createTime,defaultAttachmentId,defaultPath")
    public Style insert(RequestContext rc, Subject<Member> subject) {
        return styleService.insert(
                rc.getString("name"),
                rc.getOptionalString("sizeId"),
                rc.getString("defaultAttachmentId"),
                rc.getString("defaultPath"),
                rc.getString("content"),
                subject.getCurrentUserId(),
                subject.getCurrentUser().getOrganizationId());
    }


    /**
     * 根据id获取样式详情
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("id,code,name,titleMin,titleMax,content,materialNum,createTime,defaultAttachmentId,defaultPath")
    public Style findById(RequestContext rc) {
        return styleService.get(rc.getString("id"));
    }

    /**
     * 根据id删除样式
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("result")
    public Map<String, Integer> delete(RequestContext rc) {
        return ImmutableMap.of("result", styleService.delete(rc.getString("id")));
    }

    /**
     * 保存样式
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true, value = "样式id")
    @Param(name = "name", required = true, value = "名称")
    @Param(name = "sizeId", value = "分辨率id")
    @Param(name = "defaultAttachmentId", required = true, value = "默认附件id")
    @Param(name = "defaultPath", required = true, value = "默认附件路径")
    @Param(name = "content", required = true, value = "样式内容json")
    @JSON("result")
    public Map<String, Integer> update(RequestContext rc) {
        return ImmutableMap.of("result", styleService.update(rc.getString("id"),
                rc.getString("name"),
                rc.getOptionalString("sizeId"),
                rc.getString("defaultAttachmentId"),
                rc.getString("defaultPath"),
                rc.getString("content")
        ));
    }
    /**
     * 获取尺寸列表
     */
    @RequestMapping(value = "/canvas-size", method = RequestMethod.GET)
    @Permitted
    @JSON("id,width,height")
    public List<CanvasSize> findBySize() {
        return styleService.getSize();
    }

    /**
     * 获取尺寸列表
     */
    @RequestMapping(value = "/new/canvas-size", method = RequestMethod.GET)
    @Permitted
    @JSON("id,key,value,selectStr")
    public List<CanvasNewSize> findNewBySize() {
        List<CanvasSize> size = styleService.getSize();
        List<CanvasNewSize> newSizeList = new ArrayList<>();
        size.forEach(item->{
            CanvasNewSize newSize = new CanvasNewSize();
            String modeSize = item.getWidth() + CanvasNewSize.SPLIT_LABEL+ item.getHeight();
            newSize.setId(item.getId());
            newSize.setKey(modeSize);
            newSize.setValue(modeSize);
            newSize.setSelectStr( SizeEnum.getNameByKey(item.getType()) + "("+ modeSize +")");
            newSizeList.add(newSize);
        });
        return newSizeList;
    }

}
