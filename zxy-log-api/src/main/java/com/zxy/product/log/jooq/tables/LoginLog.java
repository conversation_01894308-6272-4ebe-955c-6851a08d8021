/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.LoginLogRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 登录日至表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginLog extends TableImpl<LoginLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_login_log</code>
     */
    public static final LoginLog LOGIN_LOG = new LoginLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LoginLogRecord> getRecordType() {
        return LoginLogRecord.class;
    }

    /**
     * The column <code>zxy-log.t_login_log.f_id</code>. 主键
     */
    public final TableField<LoginLogRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>zxy-log.t_login_log.f_organization_id</code>. 组织机构id
     */
    public final TableField<LoginLogRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织机构id");

    /**
     * The column <code>zxy-log.t_login_log.f_member_id</code>. 人员id
     */
    public final TableField<LoginLogRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员id");

    /**
     * The column <code>zxy-log.t_login_log.f_ip_addr</code>. ip 地址
     */
    public final TableField<LoginLogRecord, String> IP_ADDR = createField("f_ip_addr", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "ip 地址");

    /**
     * The column <code>zxy-log.t_login_log.f_terminal_type</code>. 1.pc 2.app
     */
    public final TableField<LoginLogRecord, Integer> TERMINAL_TYPE = createField("f_terminal_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1.pc 2.app");

    /**
     * The column <code>zxy-log.t_login_log.f_terminal</code>. 终端名称 pc对应浏览器，app对应手机型号
     */
    public final TableField<LoginLogRecord, String> TERMINAL = createField("f_terminal", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "终端名称 pc对应浏览器，app对应手机型号");

    /**
     * The column <code>zxy-log.t_login_log.f_status</code>. 1.登录成功 2.登录失败
     */
    public final TableField<LoginLogRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1.登录成功 2.登录失败");

    /**
     * The column <code>zxy-log.t_login_log.f_system</code>. 登录系统 pc对应系统 app对应手机系统
     */
    public final TableField<LoginLogRecord, String> SYSTEM = createField("f_system", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "登录系统 pc对应系统 app对应手机系统");

    /**
     * The column <code>zxy-log.t_login_log.f_user_agent</code>. 登录UserAgent
     */
    public final TableField<LoginLogRecord, String> USER_AGENT = createField("f_user_agent", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "登录UserAgent");

    /**
     * The column <code>zxy-log.t_login_log.f_create_time</code>. 创建时间
     */
    public final TableField<LoginLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>zxy-log.t_login_log.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public final TableField<LoginLogRecord, Integer> PASSWORD_TYPE = createField("f_password_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "登录密码类型，0静态密码 1动态密码");

    /**
     * The column <code>zxy-log.t_login_log.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public final TableField<LoginLogRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "登录来源，0网大 1 OA单点 2党建单点");

    /**
     * The column <code>zxy-log.t_login_log.f_modify_date</code>. 修改时间
     */
    public final TableField<LoginLogRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_login_log</code> table reference
     */
    public LoginLog() {
        this("t_login_log", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_login_log</code> table reference
     */
    public LoginLog(String alias) {
        this(alias, LOGIN_LOG);
    }

    private LoginLog(String alias, Table<LoginLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private LoginLog(String alias, Table<LoginLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "登录日至表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LoginLogRecord> getPrimaryKey() {
        return Keys.KEY_T_LOGIN_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LoginLogRecord>> getKeys() {
        return Arrays.<UniqueKey<LoginLogRecord>>asList(Keys.KEY_T_LOGIN_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLog as(String alias) {
        return new LoginLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LoginLog rename(String name) {
        return new LoginLog(name, null);
    }
}
