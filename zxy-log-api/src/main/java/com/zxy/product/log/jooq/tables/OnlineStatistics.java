/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.OnlineStatisticsRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 在线人数统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OnlineStatistics extends TableImpl<OnlineStatisticsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_online_statistics</code>
     */
    public static final OnlineStatistics ONLINE_STATISTICS = new OnlineStatistics();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OnlineStatisticsRecord> getRecordType() {
        return OnlineStatisticsRecord.class;
    }

    /**
     * The column <code>zxy-log.t_online_statistics.f_id</code>.
     */
    public final TableField<OnlineStatisticsRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>zxy-log.t_online_statistics.f_organization_id</code>. 组织id
     */
    public final TableField<OnlineStatisticsRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>zxy-log.t_online_statistics.f_online_number</code>. 在线人数
     */
    public final TableField<OnlineStatisticsRecord, Integer> ONLINE_NUMBER = createField("f_online_number", org.jooq.impl.SQLDataType.INTEGER, this, "在线人数");

    /**
     * The column <code>zxy-log.t_online_statistics.f_pc_online_number</code>. pc在线人数
     */
    public final TableField<OnlineStatisticsRecord, Integer> PC_ONLINE_NUMBER = createField("f_pc_online_number", org.jooq.impl.SQLDataType.INTEGER, this, "pc在线人数");

    /**
     * The column <code>zxy-log.t_online_statistics.f_app_online_number</code>. app在线人数
     */
    public final TableField<OnlineStatisticsRecord, Integer> APP_ONLINE_NUMBER = createField("f_app_online_number", org.jooq.impl.SQLDataType.INTEGER, this, "app在线人数");

    /**
     * The column <code>zxy-log.t_online_statistics.f_hour</code>. 小时
     */
    public final TableField<OnlineStatisticsRecord, Integer> HOUR = createField("f_hour", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"), this, "小时");

    /**
     * The column <code>zxy-log.t_online_statistics.f_day</code>. 日
     */
    public final TableField<OnlineStatisticsRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "日");

    /**
     * The column <code>zxy-log.t_online_statistics.f_month</code>. 月
     */
    public final TableField<OnlineStatisticsRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月");

    /**
     * The column <code>zxy-log.t_online_statistics.f_create_time</code>. 创建时间
     */
    public final TableField<OnlineStatisticsRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>zxy-log.t_online_statistics</code> table reference
     */
    public OnlineStatistics() {
        this("t_online_statistics", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_online_statistics</code> table reference
     */
    public OnlineStatistics(String alias) {
        this(alias, ONLINE_STATISTICS);
    }

    private OnlineStatistics(String alias, Table<OnlineStatisticsRecord> aliased) {
        this(alias, aliased, null);
    }

    private OnlineStatistics(String alias, Table<OnlineStatisticsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "在线人数统计表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OnlineStatisticsRecord> getPrimaryKey() {
        return Keys.KEY_T_ONLINE_STATISTICS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OnlineStatisticsRecord>> getKeys() {
        return Arrays.<UniqueKey<OnlineStatisticsRecord>>asList(Keys.KEY_T_ONLINE_STATISTICS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatistics as(String alias) {
        return new OnlineStatistics(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OnlineStatistics rename(String name) {
        return new OnlineStatistics(name, null);
    }
}
