package com.zxy.product.askbar.service.support;

import static com.zxy.product.askbar.jooq.tables.Accuse.ACCUSE;
import static com.zxy.product.askbar.jooq.tables.Audit.AUDIT;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.askbar.api.AccuseService;
import com.zxy.product.askbar.content.ErrorCode;
import com.zxy.product.askbar.content.MessageHeaderContent;
import com.zxy.product.askbar.content.MessageTypeContent;
import com.zxy.product.askbar.entity.Accuse;
import com.zxy.product.askbar.entity.Audit;
import com.zxy.product.askbar.entity.Discuss;
import com.zxy.product.askbar.entity.Question;
import org.springframework.util.StringUtils;

/**
 * 举报
 */
@Service
public class AccuseServiceSupport implements AccuseService {

    private CommonDao<Accuse> accuseDao;
    private CommonDao<Audit> auditDao;
    private CommonDao<Question> questionDao;
    private CommonDao<Discuss> discussDao;

    private MessageSender sender;
    private Cache cache;

    @Autowired
    public void setSender(MessageSender sender) {
        this.sender = sender;
    }

    @Autowired
    public void setDiscussDao(CommonDao<Discuss> discussDao) {
        this.discussDao = discussDao;
    }

    @Autowired
    public void setQuestionDao(CommonDao<Question> questionDao) {
        this.questionDao = questionDao;
    }

    @Autowired
    public void setAccuseDao(CommonDao<Accuse> accuseDao) {
        this.accuseDao = accuseDao;
    }

    @Autowired
    public void setAuditDao(CommonDao<Audit> auditDao) {
        this.auditDao = auditDao;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("system", "speech-set-audit");
    }

    @Override
    public Accuse insert(Integer accuseType, Optional<String> accuseNote, String businessId,
                         Integer businessType, String memberId,
                         String organizationId) {
        //同一个人没有审核前不能重复举报相同东西
        Optional<Accuse> existAccuse = accuseDao.fetchOneWithOptional(Stream.of(
                Optional.of(ACCUSE.BUSINESS_ID.eq(businessId)),
                Optional.of(ACCUSE.BUSINESS_TYPE.eq(businessType)),
                Optional.of(ACCUSE.AUDIT_STATUS.eq(Accuse.AUDIT_STATUS_WAIT)),
                Optional.of(ACCUSE.CREATE_MEMBER_ID.eq(memberId))));
        if (existAccuse.isPresent()) {
            throw new UnprocessableException(ErrorCode.AccuseDuplicate);
        }

        //问题或文章处于编辑状态，无法被举报
//        if(businessType == Accuse.BUSINESS_TYPE_QUESTION || businessType == Accuse.BUSINESS_TYPE_ARTICLE){
//            Question question = questionDao.get(businessId);
//            List<Question> list = questionDao.fetch(QUESTION.PARENT_ID.eq(question.getId()).and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_WAIT)));
//            if(list != null && list.size() > 0){
//                throw new UnprocessableException(ErrorCode.QuestionHasBeenEdit);
//            }
//        }

        String accusedMemberId;
        switch (businessType) {
            case Accuse.BUSINESS_TYPE_QUESTION:
            case Accuse.BUSINESS_TYPE_ARTICLE:
            case Accuse.BUSINESS_TYPE_SHARE:
                Question question = questionDao.getOptional(businessId)
                        .orElseThrow(() -> new UnprocessableException(ErrorCode.AccuseNotExist));
                accusedMemberId = question.getCreateMemberId();
                break;
            case Accuse.BUSINESS_TYPE_DISCUSS:
            case Accuse.BUSINESS_TYPE_REPLY:
                Discuss discuss = discussDao.getOptional(businessId)
                        .orElseThrow(() -> new UnprocessableException(ErrorCode.AccuseNotExist));
                if (Objects.equals(discuss.getDeleteFlag(), Discuss.DELETE_FLAG_YES) ||
                        Objects.equals(discuss.getAccuseStatus(), Discuss.ACCUSE_STATUS_YES)) {
                    throw new UnprocessableException(ErrorCode.AccuseNotExist);
                }
                accusedMemberId = discuss.getCreateMemberId();
                break;
            default:
                throw new UnprocessableException(ErrorCode.AccuseNotExist);
        }

        Audit audit = handleAudit(businessId, businessType, memberId, accusedMemberId, organizationId);
        Accuse accuse = new Accuse();
        accuse.forInsert();
        accuseNote.ifPresent(accuse::setAccuseNote);
        accuse.setAccuseType(accuseType);
        accuse.setBusinessId(businessId);
        accuse.setBusinessType(businessType);
        accuse.setAuditId(audit.getId());
        accuse.setAuditStatus(Accuse.AUDIT_STATUS_WAIT);
        accuse.setCreateMemberId(memberId);
        Accuse insertAccuse = accuseDao.insert(accuse);
        return insertAccuse;
    }

    /**
     * 查找是否已存在审核记录,已存在更新举报次数和最新的时间，不存在则插入审核表
     */
    private Audit handleAudit(String businessId, Integer businessType, String memberId, String AccusedMemberId, String organizationId) {
//        String redStatus = cache.get("red_boat_audit" + "#" + "1", String.class); //查询红船审核是否开启
        Audit audit = auditDao.fetchOne(AUDIT.BUSINESS_TYPE.eq(getAuditType(businessType)),
                AUDIT.BUSINESS_ID.eq(businessId),
                AUDIT.AUDIT_STATUS.eq(Audit.AUDIT_STATUS_WAIT))
                .orElseGet(() -> {
                    Audit newAudit = new Audit();
                    newAudit.setBusinessId(businessId);
                    newAudit.setBusinessType(getAuditType(businessType));
                    newAudit.setAuditStatus(Audit.AUDIT_STATUS_WAIT);
                    newAudit.setOrganizationId(organizationId);
                    newAudit.setCreateMemberId(memberId);
                    newAudit.setAccusedMemberId(AccusedMemberId);
                    newAudit.setRedBoatAuditStatus(Audit.RED_BOAT_AUDIT_STATUS_D);
                    newAudit.setRedBoatBusinessType(Audit.RED_BOAT_BUSINESS_TYPE_ONE);
//                    if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
//                        newAudit.setRedBoatBusinessType(Audit.RED_BOAT_BUSINESS_TYPE_TWO);
//                    }
                    switch (businessType) {
                        case Accuse.BUSINESS_TYPE_QUESTION:
                        case Accuse.BUSINESS_TYPE_ARTICLE:
                        case Accuse.BUSINESS_TYPE_SHARE:
                            Optional<Question> question = questionDao.getOptional(businessId);
                            newAudit.setQuestionId(businessId);
                            newAudit.setContent(question.map(Question::getTitle).orElse(""));
                            break;
                        case Accuse.BUSINESS_TYPE_DISCUSS:
                        case Accuse.BUSINESS_TYPE_REPLY:
                            Optional<Discuss> discuss = discussDao.getOptional(businessId);
                            newAudit.setQuestionId(discuss.map(Discuss::getQuestionId).orElse("0"));
                            newAudit.setContent(discuss.map(Discuss::getContent).orElse(""));
                            break;
                    }
                    return newAudit;
                });

        int accuseNum = Optional.ofNullable(audit.getAccuseNum()).orElse(0);
        audit.setAccuseNum(accuseNum + 1);
        if (audit.getId() != null) {
        	audit.setCreateMemberId(memberId);
            audit.setCreateTime(System.currentTimeMillis());
            audit.setRedBoatBusinessType(Audit.RED_BOAT_BUSINESS_TYPE_ONE);
//            if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
//                audit.setRedBoatBusinessType(Audit.RED_BOAT_BUSINESS_TYPE_TWO);
//            }
            return auditDao.update(audit);
        } else {
            audit.forInsert();

//            if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
//                audit.setRedBoatBusinessType(Audit.RED_BOAT_AUDIT_STATUS);
//                sender.send(MessageTypeContent.RED_SHIP_MATERIAL_SPLIT,
//                        MessageHeaderContent.BUSINESS_TYPE, (businessType + ""),
//                        MessageHeaderContent.BUSINESS_ID, businessId,
//                        MessageHeaderContent.AUDIT_ID, audit.getId());
//            }

            return auditDao.insert(audit);
        }
    }

    /**
     * 根据举报的类型，返回对应的审核类型
     *
     * @param businessType 举报类型1问题 2文章 3分享 4讨论 5回复
     * @return 审核类型1问题 2文章 3分享 4讨论 5回复 6问题举报 7讨论举报 8回复举报
     */
    private Integer getAuditType(Integer businessType) {
        switch (businessType) {
            case Accuse.BUSINESS_TYPE_QUESTION:
            case Accuse.BUSINESS_TYPE_ARTICLE:
            case Accuse.BUSINESS_TYPE_SHARE:
                return Audit.BUSINESS_TYPE_QUESTION_ACCUSE;
            case Accuse.BUSINESS_TYPE_DISCUSS:
                return Audit.BUSINESS_TYPE_DISCUSS_ACCUSE;
            case Accuse.BUSINESS_TYPE_REPLY:
                return Audit.BUSINESS_TYPE_REPLY_ACCUSE;
            default:
                return 0;
        }
    }


}
