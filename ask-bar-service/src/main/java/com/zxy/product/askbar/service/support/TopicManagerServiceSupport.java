package com.zxy.product.askbar.service.support;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.askbar.api.TopicManagerService;
import com.zxy.product.askbar.entity.TopicManager;

import static com.zxy.product.askbar.jooq.Tables.TOPIC_MANAGER;

/**
 * 我是管理
 */
@Service
public class TopicManagerServiceSupport implements TopicManagerService {

    private CommonDao<TopicManager> topicManagerDao;

    @Autowired
    public void setTopicManagerDao(CommonDao<TopicManager> topicManagerDao) {
        this.topicManagerDao = topicManagerDao;
    }

    @Override
    public Integer findIsManager(String memberId) {
        return topicManagerDao.execute(d -> d.select(TOPIC_MANAGER.ID.count()).from(TOPIC_MANAGER)
                .where(TOPIC_MANAGER.MEMBER_ID.eq(memberId)).fetchOne(TOPIC_MANAGER.ID.count()));
    }
}
