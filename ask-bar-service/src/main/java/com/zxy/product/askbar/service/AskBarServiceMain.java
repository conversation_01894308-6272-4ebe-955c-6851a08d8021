package com.zxy.product.askbar.service;

import com.zxy.product.askbar.service.config.*;
import org.jooq.Schema;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.askbar.service.config.RPCServerConfig;
import com.zxy.product.askbar.jooq.AskBar;
import org.springframework.transaction.annotation.EnableTransactionManagement;

//@SpringBootApplication
@Configuration
@EnableTransactionManagement
@ComponentScan
@Import({
		RpcConfig.class,
		RPCServerConfig.class,
		CommonDaoConfig.class,
		MessageConfig.class,
		CacheConfig.class,
		TransactionConfig.class,
		RabbitAutoConfiguration.class,
		TransactionAutoConfiguration.class,
		DataSourceAutoConfiguration.class,
		JooqAutoConfiguration.class
})
public class AskBarServiceMain {
	@Bean
	public Schema schema() {
		return AskBar.ASK_BAR_SCHEMA; // jOOQ生成代码的根目录下与数据库同名的类
	}

	public static void main(String[] args) throws Exception {
		SpringApplication.run(AskBarServiceMain.class, args);
		synchronized (AskBarServiceMain.class) {
			AskBarServiceMain.class.wait();
		}
	}
}
