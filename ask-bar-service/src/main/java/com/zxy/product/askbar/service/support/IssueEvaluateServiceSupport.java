package com.zxy.product.askbar.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.askbar.api.IssueEvaluateService;
import com.zxy.product.askbar.entity.IssueEvaluate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 问题评价
 */
@Service
public class IssueEvaluateServiceSupport implements IssueEvaluateService {

    private CommonDao<IssueEvaluate> issueEvaluateCommonDao;

    @Autowired
    public void setIssueEvaluateCommonDao(CommonDao<IssueEvaluate> issueEvaluateCommonDao) {
        this.issueEvaluateCommonDao = issueEvaluateCommonDao;
    }

    @Override
    public Integer saveEvaluate(String memberId, Integer praiseOrTrample, String issue) {
        IssueEvaluate issueEvaluate=new IssueEvaluate();
        issueEvaluate.forInsert();
        issueEvaluate.setIssue(issue);
        issueEvaluate.setMemberId(memberId);
        issueEvaluate.setPraiseOrTrample(praiseOrTrample);
        issueEvaluate.setQuizDate(new Date().getTime());
        issueEvaluateCommonDao.insert(issueEvaluate);
        return 1 ;
    }
}
