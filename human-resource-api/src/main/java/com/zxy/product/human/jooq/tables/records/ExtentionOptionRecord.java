/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.records;


import com.zxy.product.human.jooq.tables.ExtentionOption;
import com.zxy.product.human.jooq.tables.interfaces.IExtentionOption;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExtentionOptionRecord extends UpdatableRecordImpl<ExtentionOptionRecord> implements Record5<String, String, String, String, Long>, IExtentionOption {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>human-resource.t_extention_option.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>human-resource.t_extention_option.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>human-resource.t_extention_option.f_extention_id</code>. 属性表ID
     */
    @Override
    public void setExtentionId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>human-resource.t_extention_option.f_extention_id</code>. 属性表ID
     */
    @Override
    public String getExtentionId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>human-resource.t_extention_option.f_key</code>. 属性对应key
     */
    @Override
    public void setKey(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>human-resource.t_extention_option.f_key</code>. 属性对应key
     */
    @Override
    public String getKey() {
        return (String) get(2);
    }

    /**
     * Setter for <code>human-resource.t_extention_option.f_value</code>. 属性值
     */
    @Override
    public void setValue(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>human-resource.t_extention_option.f_value</code>. 属性值
     */
    @Override
    public String getValue() {
        return (String) get(3);
    }

    /**
     * Setter for <code>human-resource.t_extention_option.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>human-resource.t_extention_option.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ExtentionOption.EXTENTION_OPTION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ExtentionOption.EXTENTION_OPTION.EXTENTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ExtentionOption.EXTENTION_OPTION.KEY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ExtentionOption.EXTENTION_OPTION.VALUE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return ExtentionOption.EXTENTION_OPTION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getExtentionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getKey();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getValue();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord value2(String value) {
        setExtentionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord value3(String value) {
        setKey(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord value4(String value) {
        setValue(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExtentionOptionRecord values(String value1, String value2, String value3, String value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IExtentionOption from) {
        setId(from.getId());
        setExtentionId(from.getExtentionId());
        setKey(from.getKey());
        setValue(from.getValue());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IExtentionOption> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ExtentionOptionRecord
     */
    public ExtentionOptionRecord() {
        super(ExtentionOption.EXTENTION_OPTION);
    }

    /**
     * Create a detached, initialised ExtentionOptionRecord
     */
    public ExtentionOptionRecord(String id, String extentionId, String key, String value, Long createTime) {
        super(ExtentionOption.EXTENTION_OPTION);

        set(0, id);
        set(1, extentionId);
        set(2, key);
        set(3, value);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.human.jooq.tables.pojos.ExtentionOptionEntity)) {
            return false;
        }
        com.zxy.product.human.jooq.tables.pojos.ExtentionOptionEntity pojo = (com.zxy.product.human.jooq.tables.pojos.ExtentionOptionEntity)source;
        pojo.into(this);
        return true;
    }
}
