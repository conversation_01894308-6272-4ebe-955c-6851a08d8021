/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.human.jooq.tables.interfaces.ISyncItemMember;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SyncItemMemberEntity extends BaseEntity implements ISyncItemMember {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  memberName;
    private String  memberFullName;
    private Integer memberSex;
    private String  memberCredentialValue;
    private Long    memberBornDate;
    private String  memberNationalityValue;
    private String  memberEthnicityValue;
    private Long    memberEntryDate;
    private Long    memberJoinDate;
    private String  memberCustomerTypeValue;
    private String  memberIncumbencyStatusValue;
    private String  memberPhoneNumber;
    private String  memberEmail;
    private String  memberPoliticalizationValue;
    private Long    memberExpiryDate;
    private String  memberSchool;
    private Long    memberGraduateDate;
    private String  memberEducationValue;
    private Integer memberIsLeader;
    private Integer memberSequence;
    private String  memberOrganizationCode;
    private String  memberPositionCode;
    private String  rootOrganizationId;
    private String  type;
    private String  before;
    private String  after;
    private Integer status;
    private String  content;
    private String  remark;
    private String  version;

    public SyncItemMemberEntity() {}

    public SyncItemMemberEntity(SyncItemMemberEntity value) {
        this.memberId = value.memberId;
        this.memberName = value.memberName;
        this.memberFullName = value.memberFullName;
        this.memberSex = value.memberSex;
        this.memberCredentialValue = value.memberCredentialValue;
        this.memberBornDate = value.memberBornDate;
        this.memberNationalityValue = value.memberNationalityValue;
        this.memberEthnicityValue = value.memberEthnicityValue;
        this.memberEntryDate = value.memberEntryDate;
        this.memberJoinDate = value.memberJoinDate;
        this.memberCustomerTypeValue = value.memberCustomerTypeValue;
        this.memberIncumbencyStatusValue = value.memberIncumbencyStatusValue;
        this.memberPhoneNumber = value.memberPhoneNumber;
        this.memberEmail = value.memberEmail;
        this.memberPoliticalizationValue = value.memberPoliticalizationValue;
        this.memberExpiryDate = value.memberExpiryDate;
        this.memberSchool = value.memberSchool;
        this.memberGraduateDate = value.memberGraduateDate;
        this.memberEducationValue = value.memberEducationValue;
        this.memberIsLeader = value.memberIsLeader;
        this.memberSequence = value.memberSequence;
        this.memberOrganizationCode = value.memberOrganizationCode;
        this.memberPositionCode = value.memberPositionCode;
        this.rootOrganizationId = value.rootOrganizationId;
        this.type = value.type;
        this.before = value.before;
        this.after = value.after;
        this.status = value.status;
        this.content = value.content;
        this.remark = value.remark;
        this.version = value.version;
    }

    public SyncItemMemberEntity(
        String  id,
        Long    createTime,
        String  memberId,
        String  memberName,
        String  memberFullName,
        Integer memberSex,
        String  memberCredentialValue,
        Long    memberBornDate,
        String  memberNationalityValue,
        String  memberEthnicityValue,
        Long    memberEntryDate,
        Long    memberJoinDate,
        String  memberCustomerTypeValue,
        String  memberIncumbencyStatusValue,
        String  memberPhoneNumber,
        String  memberEmail,
        String  memberPoliticalizationValue,
        Long    memberExpiryDate,
        String  memberSchool,
        Long    memberGraduateDate,
        String  memberEducationValue,
        Integer memberIsLeader,
        Integer memberSequence,
        String  memberOrganizationCode,
        String  memberPositionCode,
        String  rootOrganizationId,
        String  type,
        String  before,
        String  after,
        Integer status,
        String  content,
        String  remark,
        String  version
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.memberId = memberId;
        this.memberName = memberName;
        this.memberFullName = memberFullName;
        this.memberSex = memberSex;
        this.memberCredentialValue = memberCredentialValue;
        this.memberBornDate = memberBornDate;
        this.memberNationalityValue = memberNationalityValue;
        this.memberEthnicityValue = memberEthnicityValue;
        this.memberEntryDate = memberEntryDate;
        this.memberJoinDate = memberJoinDate;
        this.memberCustomerTypeValue = memberCustomerTypeValue;
        this.memberIncumbencyStatusValue = memberIncumbencyStatusValue;
        this.memberPhoneNumber = memberPhoneNumber;
        this.memberEmail = memberEmail;
        this.memberPoliticalizationValue = memberPoliticalizationValue;
        this.memberExpiryDate = memberExpiryDate;
        this.memberSchool = memberSchool;
        this.memberGraduateDate = memberGraduateDate;
        this.memberEducationValue = memberEducationValue;
        this.memberIsLeader = memberIsLeader;
        this.memberSequence = memberSequence;
        this.memberOrganizationCode = memberOrganizationCode;
        this.memberPositionCode = memberPositionCode;
        this.rootOrganizationId = rootOrganizationId;
        this.type = type;
        this.before = before;
        this.after = after;
        this.status = status;
        this.content = content;
        this.remark = remark;
        this.version = version;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getMemberName() {
        return this.memberName;
    }

    @Override
    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @Override
    public String getMemberFullName() {
        return this.memberFullName;
    }

    @Override
    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    @Override
    public Integer getMemberSex() {
        return this.memberSex;
    }

    @Override
    public void setMemberSex(Integer memberSex) {
        this.memberSex = memberSex;
    }

    @Override
    public String getMemberCredentialValue() {
        return this.memberCredentialValue;
    }

    @Override
    public void setMemberCredentialValue(String memberCredentialValue) {
        this.memberCredentialValue = memberCredentialValue;
    }

    @Override
    public Long getMemberBornDate() {
        return this.memberBornDate;
    }

    @Override
    public void setMemberBornDate(Long memberBornDate) {
        this.memberBornDate = memberBornDate;
    }

    @Override
    public String getMemberNationalityValue() {
        return this.memberNationalityValue;
    }

    @Override
    public void setMemberNationalityValue(String memberNationalityValue) {
        this.memberNationalityValue = memberNationalityValue;
    }

    @Override
    public String getMemberEthnicityValue() {
        return this.memberEthnicityValue;
    }

    @Override
    public void setMemberEthnicityValue(String memberEthnicityValue) {
        this.memberEthnicityValue = memberEthnicityValue;
    }

    @Override
    public Long getMemberEntryDate() {
        return this.memberEntryDate;
    }

    @Override
    public void setMemberEntryDate(Long memberEntryDate) {
        this.memberEntryDate = memberEntryDate;
    }

    @Override
    public Long getMemberJoinDate() {
        return this.memberJoinDate;
    }

    @Override
    public void setMemberJoinDate(Long memberJoinDate) {
        this.memberJoinDate = memberJoinDate;
    }

    @Override
    public String getMemberCustomerTypeValue() {
        return this.memberCustomerTypeValue;
    }

    @Override
    public void setMemberCustomerTypeValue(String memberCustomerTypeValue) {
        this.memberCustomerTypeValue = memberCustomerTypeValue;
    }

    @Override
    public String getMemberIncumbencyStatusValue() {
        return this.memberIncumbencyStatusValue;
    }

    @Override
    public void setMemberIncumbencyStatusValue(String memberIncumbencyStatusValue) {
        this.memberIncumbencyStatusValue = memberIncumbencyStatusValue;
    }

    @Override
    public String getMemberPhoneNumber() {
        return this.memberPhoneNumber;
    }

    @Override
    public void setMemberPhoneNumber(String memberPhoneNumber) {
        this.memberPhoneNumber = memberPhoneNumber;
    }

    @Override
    public String getMemberEmail() {
        return this.memberEmail;
    }

    @Override
    public void setMemberEmail(String memberEmail) {
        this.memberEmail = memberEmail;
    }

    @Override
    public String getMemberPoliticalizationValue() {
        return this.memberPoliticalizationValue;
    }

    @Override
    public void setMemberPoliticalizationValue(String memberPoliticalizationValue) {
        this.memberPoliticalizationValue = memberPoliticalizationValue;
    }

    @Override
    public Long getMemberExpiryDate() {
        return this.memberExpiryDate;
    }

    @Override
    public void setMemberExpiryDate(Long memberExpiryDate) {
        this.memberExpiryDate = memberExpiryDate;
    }

    @Override
    public String getMemberSchool() {
        return this.memberSchool;
    }

    @Override
    public void setMemberSchool(String memberSchool) {
        this.memberSchool = memberSchool;
    }

    @Override
    public Long getMemberGraduateDate() {
        return this.memberGraduateDate;
    }

    @Override
    public void setMemberGraduateDate(Long memberGraduateDate) {
        this.memberGraduateDate = memberGraduateDate;
    }

    @Override
    public String getMemberEducationValue() {
        return this.memberEducationValue;
    }

    @Override
    public void setMemberEducationValue(String memberEducationValue) {
        this.memberEducationValue = memberEducationValue;
    }

    @Override
    public Integer getMemberIsLeader() {
        return this.memberIsLeader;
    }

    @Override
    public void setMemberIsLeader(Integer memberIsLeader) {
        this.memberIsLeader = memberIsLeader;
    }

    @Override
    public Integer getMemberSequence() {
        return this.memberSequence;
    }

    @Override
    public void setMemberSequence(Integer memberSequence) {
        this.memberSequence = memberSequence;
    }

    @Override
    public String getMemberOrganizationCode() {
        return this.memberOrganizationCode;
    }

    @Override
    public void setMemberOrganizationCode(String memberOrganizationCode) {
        this.memberOrganizationCode = memberOrganizationCode;
    }

    @Override
    public String getMemberPositionCode() {
        return this.memberPositionCode;
    }

    @Override
    public void setMemberPositionCode(String memberPositionCode) {
        this.memberPositionCode = memberPositionCode;
    }

    @Override
    public String getRootOrganizationId() {
        return this.rootOrganizationId;
    }

    @Override
    public void setRootOrganizationId(String rootOrganizationId) {
        this.rootOrganizationId = rootOrganizationId;
    }

    @Override
    public String getType() {
        return this.type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String getBefore() {
        return this.before;
    }

    @Override
    public void setBefore(String before) {
        this.before = before;
    }

    @Override
    public String getAfter() {
        return this.after;
    }

    @Override
    public void setAfter(String after) {
        this.after = after;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getContent() {
        return this.content;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String getRemark() {
        return this.remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getVersion() {
        return this.version;
    }

    @Override
    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SyncItemMemberEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(memberId);
        sb.append(", ").append(memberName);
        sb.append(", ").append(memberFullName);
        sb.append(", ").append(memberSex);
        sb.append(", ").append(memberCredentialValue);
        sb.append(", ").append(memberBornDate);
        sb.append(", ").append(memberNationalityValue);
        sb.append(", ").append(memberEthnicityValue);
        sb.append(", ").append(memberEntryDate);
        sb.append(", ").append(memberJoinDate);
        sb.append(", ").append(memberCustomerTypeValue);
        sb.append(", ").append(memberIncumbencyStatusValue);
        sb.append(", ").append(memberPhoneNumber);
        sb.append(", ").append(memberEmail);
        sb.append(", ").append(memberPoliticalizationValue);
        sb.append(", ").append(memberExpiryDate);
        sb.append(", ").append(memberSchool);
        sb.append(", ").append(memberGraduateDate);
        sb.append(", ").append(memberEducationValue);
        sb.append(", ").append(memberIsLeader);
        sb.append(", ").append(memberSequence);
        sb.append(", ").append(memberOrganizationCode);
        sb.append(", ").append(memberPositionCode);
        sb.append(", ").append(rootOrganizationId);
        sb.append(", ").append(type);
        sb.append(", ").append(before);
        sb.append(", ").append(after);
        sb.append(", ").append(status);
        sb.append(", ").append(content);
        sb.append(", ").append(remark);
        sb.append(", ").append(version);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISyncItemMember from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setMemberName(from.getMemberName());
        setMemberFullName(from.getMemberFullName());
        setMemberSex(from.getMemberSex());
        setMemberCredentialValue(from.getMemberCredentialValue());
        setMemberBornDate(from.getMemberBornDate());
        setMemberNationalityValue(from.getMemberNationalityValue());
        setMemberEthnicityValue(from.getMemberEthnicityValue());
        setMemberEntryDate(from.getMemberEntryDate());
        setMemberJoinDate(from.getMemberJoinDate());
        setMemberCustomerTypeValue(from.getMemberCustomerTypeValue());
        setMemberIncumbencyStatusValue(from.getMemberIncumbencyStatusValue());
        setMemberPhoneNumber(from.getMemberPhoneNumber());
        setMemberEmail(from.getMemberEmail());
        setMemberPoliticalizationValue(from.getMemberPoliticalizationValue());
        setMemberExpiryDate(from.getMemberExpiryDate());
        setMemberSchool(from.getMemberSchool());
        setMemberGraduateDate(from.getMemberGraduateDate());
        setMemberEducationValue(from.getMemberEducationValue());
        setMemberIsLeader(from.getMemberIsLeader());
        setMemberSequence(from.getMemberSequence());
        setMemberOrganizationCode(from.getMemberOrganizationCode());
        setMemberPositionCode(from.getMemberPositionCode());
        setRootOrganizationId(from.getRootOrganizationId());
        setType(from.getType());
        setBefore(from.getBefore());
        setAfter(from.getAfter());
        setStatus(from.getStatus());
        setContent(from.getContent());
        setRemark(from.getRemark());
        setVersion(from.getVersion());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISyncItemMember> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SyncItemMemberEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.human.jooq.tables.records.SyncItemMemberRecord r = new com.zxy.product.human.jooq.tables.records.SyncItemMemberRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ID, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CREATE_TIME, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ID, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NAME, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_FULL_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_FULL_NAME, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_FULL_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEX.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEX, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEX));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CREDENTIAL_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CREDENTIAL_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CREDENTIAL_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_BORN_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_BORN_DATE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_BORN_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NATIONALITY_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NATIONALITY_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_NATIONALITY_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ETHNICITY_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ETHNICITY_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ETHNICITY_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ENTRY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ENTRY_DATE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ENTRY_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_JOIN_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_JOIN_DATE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_JOIN_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CUSTOMER_TYPE_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CUSTOMER_TYPE_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_CUSTOMER_TYPE_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_INCUMBENCY_STATUS_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_INCUMBENCY_STATUS_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_INCUMBENCY_STATUS_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_PHONE_NUMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_PHONE_NUMBER, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_PHONE_NUMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EMAIL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EMAIL, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EMAIL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POLITICALIZATION_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POLITICALIZATION_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POLITICALIZATION_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EXPIRY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EXPIRY_DATE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EXPIRY_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SCHOOL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SCHOOL, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SCHOOL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_GRADUATE_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_GRADUATE_DATE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_GRADUATE_DATE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EDUCATION_VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EDUCATION_VALUE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_EDUCATION_VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_IS_LEADER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_IS_LEADER, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_IS_LEADER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEQUENCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEQUENCE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_SEQUENCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ORGANIZATION_CODE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ORGANIZATION_CODE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_ORGANIZATION_CODE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POSITION_CODE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POSITION_CODE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.MEMBER_POSITION_CODE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ROOT_ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ROOT_ORGANIZATION_ID, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.ROOT_ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.TYPE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.BEFORE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.BEFORE, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.BEFORE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.AFTER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.AFTER, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.AFTER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.STATUS, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CONTENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CONTENT, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.CONTENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.REMARK.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.REMARK, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.REMARK));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.VERSION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.VERSION, record.getValue(com.zxy.product.human.jooq.tables.SyncItemMember.SYNC_ITEM_MEMBER.VERSION));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
