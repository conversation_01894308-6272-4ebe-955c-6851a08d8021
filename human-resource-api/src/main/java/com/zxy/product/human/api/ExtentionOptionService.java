package com.zxy.product.human.api;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.human.entity.ExtentionOption;

/**
 * <AUTHOR>
 *
 */
@RemoteService
public interface ExtentionOptionService {

    /**
     *新增
     * @param extentionId
     * @param value
     * @return
     */
    @Transactional
    ExtentionOption insert(String extentionId, String value);

    /**
     * 根据id查询属性
     * @param id 节点id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExtentionOption get(String id);

    /**
     * 根据id删除属性
     *
     * @param id
     *            节点id
     * @return
     */
    @Transactional
    int delete(String id);

}
