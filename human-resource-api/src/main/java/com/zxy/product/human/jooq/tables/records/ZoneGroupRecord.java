/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.records;


import com.zxy.product.human.jooq.tables.ZoneGroup;
import com.zxy.product.human.jooq.tables.interfaces.IZoneGroup;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ZoneGroupRecord extends UpdatableRecordImpl<ZoneGroupRecord> implements Record7<String, Long, String, String, Integer, Integer, String>, IZoneGroup {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>human-resource.t_zone_group.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_name</code>. 类别名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_name</code>. 类别名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_code</code>. 目录编码
     */
    @Override
    public void setCode(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_code</code>. 目录编码
     */
    @Override
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_sequence</code>. 排序
     */
    @Override
    public void setSequence(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_sequence</code>. 排序
     */
    @Override
    public Integer getSequence() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_status</code>. 类别状态(0=禁用,1=启用)
     */
    @Override
    public void setStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_status</code>. 类别状态(0=禁用,1=启用)
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>human-resource.t_zone_group.f_organization_id</code>. 所属组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>human-resource.t_zone_group.f_organization_id</code>. 所属组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, Long, String, String, Integer, Integer, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, Long, String, String, Integer, Integer, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ZoneGroup.ZONE_GROUP.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return ZoneGroup.ZONE_GROUP.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ZoneGroup.ZONE_GROUP.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ZoneGroup.ZONE_GROUP.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return ZoneGroup.ZONE_GROUP.SEQUENCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ZoneGroup.ZONE_GROUP.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return ZoneGroup.ZONE_GROUP.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getSequence();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value4(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value5(Integer value) {
        setSequence(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value6(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord value7(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ZoneGroupRecord values(String value1, Long value2, String value3, String value4, Integer value5, Integer value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IZoneGroup from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setCode(from.getCode());
        setSequence(from.getSequence());
        setStatus(from.getStatus());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IZoneGroup> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ZoneGroupRecord
     */
    public ZoneGroupRecord() {
        super(ZoneGroup.ZONE_GROUP);
    }

    /**
     * Create a detached, initialised ZoneGroupRecord
     */
    public ZoneGroupRecord(String id, Long createTime, String name, String code, Integer sequence, Integer status, String organizationId) {
        super(ZoneGroup.ZONE_GROUP);

        set(0, id);
        set(1, createTime);
        set(2, name);
        set(3, code);
        set(4, sequence);
        set(5, status);
        set(6, organizationId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.human.jooq.tables.pojos.ZoneGroupEntity)) {
            return false;
        }
        com.zxy.product.human.jooq.tables.pojos.ZoneGroupEntity pojo = (com.zxy.product.human.jooq.tables.pojos.ZoneGroupEntity)source;
        pojo.into(this);
        return true;
    }
}
