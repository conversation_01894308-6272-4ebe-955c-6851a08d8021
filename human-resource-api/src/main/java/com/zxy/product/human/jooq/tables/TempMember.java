/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables;


import com.zxy.product.human.jooq.HumanResource;
import com.zxy.product.human.jooq.Keys;
import com.zxy.product.human.jooq.tables.records.TempMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 临时表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TempMember extends TableImpl<TempMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>human-resource.t_temp_member</code>
     */
    public static final TempMember TEMP_MEMBER = new TempMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TempMemberRecord> getRecordType() {
        return TempMemberRecord.class;
    }

    /**
     * The column <code>human-resource.t_temp_member.f_id</code>. ID
     */
    public final TableField<TempMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>human-resource.t_temp_member.f_create_time</code>. 创建时间
     */
    public final TableField<TempMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>human-resource.t_temp_member.f_name</code>. 员工编号
     */
    public final TableField<TempMemberRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工编号");

    /**
     * The column <code>human-resource.t_temp_member.f_full_name</code>. 账户
     */
    public final TableField<TempMemberRecord, String> FULL_NAME = createField("f_full_name", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "账户");

    /**
     * The column <code>human-resource.t_temp_member.f_password</code>. 密码
     */
    public final TableField<TempMemberRecord, String> PASSWORD = createField("f_password", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "密码");

    /**
     * The column <code>human-resource.t_temp_member.f_salt</code>. 密码盐
     */
    public final TableField<TempMemberRecord, String> SALT = createField("f_salt", org.jooq.impl.SQLDataType.VARCHAR.length(30).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "密码盐");

    /**
     * The column <code>human-resource.t_temp_member.f_sex</code>. 性别
     */
    public final TableField<TempMemberRecord, Integer> SEX = createField("f_sex", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "性别");

    /**
     * The column <code>human-resource.t_temp_member.f_organization_id</code>. 部门Id
     */
    public final TableField<TempMemberRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门Id");

    /**
     * The column <code>human-resource.t_temp_member.f_organization_name</code>. 部门名称
     */
    public final TableField<TempMemberRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门名称");

    /**
     * The column <code>human-resource.t_temp_member.f_credential_type</code>. 证件类型
     */
    public final TableField<TempMemberRecord, String> CREDENTIAL_TYPE = createField("f_credential_type", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证件类型");

    /**
     * The column <code>human-resource.t_temp_member.f_credential_value</code>. 证件值
     */
    public final TableField<TempMemberRecord, String> CREDENTIAL_VALUE = createField("f_credential_value", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证件值");

    /**
     * The column <code>human-resource.t_temp_member.f_email</code>. 邮箱
     */
    public final TableField<TempMemberRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "邮箱");

    /**
     * The column <code>human-resource.t_temp_member.f_phone_number</code>. 手机号码
     */
    public final TableField<TempMemberRecord, String> PHONE_NUMBER = createField("f_phone_number", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "手机号码");

    /**
     * The column <code>human-resource.t_temp_member.f_born_date</code>. 出生日期
     */
    public final TableField<TempMemberRecord, Long> BORN_DATE = createField("f_born_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "出生日期");

    /**
     * The column <code>human-resource.t_temp_member.f_expiry_date</code>. 有效日期
     */
    public final TableField<TempMemberRecord, Long> EXPIRY_DATE = createField("f_expiry_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "有效日期");

    /**
     * The column <code>human-resource.t_temp_member.f_politicalization_id</code>. 政治面貌Id
     */
    public final TableField<TempMemberRecord, String> POLITICALIZATION_ID = createField("f_politicalization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "政治面貌Id");

    /**
     * The column <code>human-resource.t_temp_member.f_politicalization_value</code>. 政治面貌
     */
    public final TableField<TempMemberRecord, String> POLITICALIZATION_VALUE = createField("f_politicalization_value", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "政治面貌");

    /**
     * The column <code>human-resource.t_temp_member.f_major_position_id</code>. 岗位id
     */
    public final TableField<TempMemberRecord, String> MAJOR_POSITION_ID = createField("f_major_position_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "岗位id");

    /**
     * The column <code>human-resource.t_temp_member.f_major_position_value</code>. 岗位名称
     */
    public final TableField<TempMemberRecord, String> MAJOR_POSITION_VALUE = createField("f_major_position_value", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "岗位名称");

    /**
     * The column <code>human-resource.t_temp_member.f_entry_date</code>. 入职日期
     */
    public final TableField<TempMemberRecord, Long> ENTRY_DATE = createField("f_entry_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "入职日期");

    /**
     * The column <code>human-resource.t_temp_member.f_join_date</code>. 参加本单位时间
     */
    public final TableField<TempMemberRecord, Long> JOIN_DATE = createField("f_join_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "参加本单位时间");

    /**
     * The column <code>human-resource.t_temp_member.f_customer_type</code>. 员工类型
     */
    public final TableField<TempMemberRecord, String> CUSTOMER_TYPE = createField("f_customer_type", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工类型");

    /**
     * The column <code>human-resource.t_temp_member.f_sequence</code>. 排序号
     */
    public final TableField<TempMemberRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序号");

    /**
     * The column <code>human-resource.t_temp_member.f_is_leader</code>. 是否是领导
     */
    public final TableField<TempMemberRecord, Integer> IS_LEADER = createField("f_is_leader", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否是领导");

    /**
     * The column <code>human-resource.t_temp_member.f_user_population</code>. 用户人群
     */
    public final TableField<TempMemberRecord, String> USER_POPULATION = createField("f_user_population", org.jooq.impl.SQLDataType.VARCHAR.length(800).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户人群");

    /**
     * The column <code>human-resource.t_temp_member.f_account_type</code>. 账号类型 0表示系统同步 1表示 新增注册 2表示自主注册
     */
    public final TableField<TempMemberRecord, Integer> ACCOUNT_TYPE = createField("f_account_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "账号类型 0表示系统同步 1表示 新增注册 2表示自主注册");

    /**
     * The column <code>human-resource.t_temp_member.f_account_category</code>. 账号类别 关联t_regist_reason id 
     */
    public final TableField<TempMemberRecord, String> ACCOUNT_CATEGORY = createField("f_account_category", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "账号类别 关联t_regist_reason id ");

    /**
     * Create a <code>human-resource.t_temp_member</code> table reference
     */
    public TempMember() {
        this("t_temp_member", null);
    }

    /**
     * Create an aliased <code>human-resource.t_temp_member</code> table reference
     */
    public TempMember(String alias) {
        this(alias, TEMP_MEMBER);
    }

    private TempMember(String alias, Table<TempMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private TempMember(String alias, Table<TempMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "临时表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return HumanResource.HUMAN_RESOURCE_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TempMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_TEMP_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TempMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<TempMemberRecord>>asList(Keys.KEY_T_TEMP_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TempMember as(String alias) {
        return new TempMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TempMember rename(String name) {
        return new TempMember(name, null);
    }
}
