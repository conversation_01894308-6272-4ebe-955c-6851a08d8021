package com.zxy.product.human.entity;

import com.zxy.product.human.jooq.tables.pojos.MemberTopicEntity;

/**
 * Created by ch<PERSON><PERSON> on 17/4/22.
 */
public class MemberTopic extends MemberTopicEntity {

    private static final long serialVersionUID = -406929456580892060L;

    private String organizationId;
    private Integer watchSum;
    private String topicName;
    private String topicParentId;
    private Integer topicLevel;

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getWatchSum() {
        return watchSum;
    }

    public void setWatchSum(Integer watchSum) {
        this.watchSum = watchSum;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public String getTopicParentId() {
        return topicParentId;
    }

    public void setTopicParentId(String topicParentId) {
        this.topicParentId = topicParentId;
    }

    public Integer getTopicLevel() {
        return topicLevel;
    }

    public void setTopicLevel(Integer topicLevel) {
        this.topicLevel = topicLevel;
    }
}
