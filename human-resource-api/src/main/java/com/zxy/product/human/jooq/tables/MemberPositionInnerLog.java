/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables;


import com.zxy.product.human.jooq.HumanResource;
import com.zxy.product.human.jooq.Keys;
import com.zxy.product.human.jooq.tables.records.MemberPositionInnerLogRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 用户内部工作信息
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberPositionInnerLog extends TableImpl<MemberPositionInnerLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>human-resource.t_member_position_inner_log</code>
     */
    public static final MemberPositionInnerLog MEMBER_POSITION_INNER_LOG = new MemberPositionInnerLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MemberPositionInnerLogRecord> getRecordType() {
        return MemberPositionInnerLogRecord.class;
    }

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_id</code>.
     */
    public final TableField<MemberPositionInnerLogRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_create_time</code>. 创建时间
     */
    public final TableField<MemberPositionInnerLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_member_id</code>. 用户id
     */
    public final TableField<MemberPositionInnerLogRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户id");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_position_id</code>. 职位id
     */
    public final TableField<MemberPositionInnerLogRecord, String> POSITION_ID = createField("f_position_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "职位id");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_position_name</code>. 职位名称
     */
    public final TableField<MemberPositionInnerLogRecord, String> POSITION_NAME = createField("f_position_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "职位名称");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_organization_id</code>. 所属部门id
     */
    public final TableField<MemberPositionInnerLogRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属部门id");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_organization_name</code>. 所属部门名称
     */
    public final TableField<MemberPositionInnerLogRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "所属部门名称");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_start_time</code>. 开始时间
     */
    public final TableField<MemberPositionInnerLogRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_end_time</code>. 结束时间
     */
    public final TableField<MemberPositionInnerLogRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>human-resource.t_member_position_inner_log.f_sequence</code>. 排序号
     */
    public final TableField<MemberPositionInnerLogRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER, this, "排序号");

    /**
     * Create a <code>human-resource.t_member_position_inner_log</code> table reference
     */
    public MemberPositionInnerLog() {
        this("t_member_position_inner_log", null);
    }

    /**
     * Create an aliased <code>human-resource.t_member_position_inner_log</code> table reference
     */
    public MemberPositionInnerLog(String alias) {
        this(alias, MEMBER_POSITION_INNER_LOG);
    }

    private MemberPositionInnerLog(String alias, Table<MemberPositionInnerLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private MemberPositionInnerLog(String alias, Table<MemberPositionInnerLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "用户内部工作信息");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return HumanResource.HUMAN_RESOURCE_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MemberPositionInnerLogRecord> getPrimaryKey() {
        return Keys.KEY_T_MEMBER_POSITION_INNER_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MemberPositionInnerLogRecord>> getKeys() {
        return Arrays.<UniqueKey<MemberPositionInnerLogRecord>>asList(Keys.KEY_T_MEMBER_POSITION_INNER_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberPositionInnerLog as(String alias) {
        return new MemberPositionInnerLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MemberPositionInnerLog rename(String name) {
        return new MemberPositionInnerLog(name, null);
    }
}
