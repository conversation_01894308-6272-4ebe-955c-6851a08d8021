/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.records;


import com.zxy.product.human.jooq.tables.RegisterExpeditingRocord;
import com.zxy.product.human.jooq.tables.interfaces.IRegisterExpeditingRocord;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 催办记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RegisterExpeditingRocordRecord extends UpdatableRecordImpl<RegisterExpeditingRocordRecord> implements Record7<String, String, Long, String, String, Timestamp, String>, IRegisterExpeditingRocord {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_id</code>. 催办记录id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_id</code>. 催办记录id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_regist_record_id</code>. 需要催办的注册记录id
     */
    @Override
    public void setRegistRecordId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_regist_record_id</code>. 需要催办的注册记录id
     */
    @Override
    public String getRegistRecordId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_create_id</code>. 创建人id
     */
    @Override
    public void setCreateId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_create_id</code>. 创建人id
     */
    @Override
    public String getCreateId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_modify_id</code>. 修改人id
     */
    @Override
    public void setModifyId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_modify_id</code>. 修改人id
     */
    @Override
    public String getModifyId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(5);
    }

    /**
     * Setter for <code>human-resource.t_register_expediting_rocord.f_is_del</code>. 0 正常 1删除
     */
    @Override
    public void setIsDel(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>human-resource.t_register_expediting_rocord.f_is_del</code>. 0 正常 1删除
     */
    @Override
    public String getIsDel() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Long, String, String, Timestamp, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Long, String, String, Timestamp, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.REGIST_RECORD_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.CREATE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.MODIFY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field6() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD.IS_DEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getRegistRecordId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCreateId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getModifyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value6() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getIsDel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value2(String value) {
        setRegistRecordId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value3(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value4(String value) {
        setCreateId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value5(String value) {
        setModifyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value6(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord value7(String value) {
        setIsDel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterExpeditingRocordRecord values(String value1, String value2, Long value3, String value4, String value5, Timestamp value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IRegisterExpeditingRocord from) {
        setId(from.getId());
        setRegistRecordId(from.getRegistRecordId());
        setCreateTime(from.getCreateTime());
        setCreateId(from.getCreateId());
        setModifyId(from.getModifyId());
        setModifyDate(from.getModifyDate());
        setIsDel(from.getIsDel());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IRegisterExpeditingRocord> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RegisterExpeditingRocordRecord
     */
    public RegisterExpeditingRocordRecord() {
        super(RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD);
    }

    /**
     * Create a detached, initialised RegisterExpeditingRocordRecord
     */
    public RegisterExpeditingRocordRecord(String id, String registRecordId, Long createTime, String createId, String modifyId, Timestamp modifyDate, String isDel) {
        super(RegisterExpeditingRocord.REGISTER_EXPEDITING_ROCORD);

        set(0, id);
        set(1, registRecordId);
        set(2, createTime);
        set(3, createId);
        set(4, modifyId);
        set(5, modifyDate);
        set(6, isDel);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.human.jooq.tables.pojos.RegisterExpeditingRocordEntity)) {
            return false;
        }
        com.zxy.product.human.jooq.tables.pojos.RegisterExpeditingRocordEntity pojo = (com.zxy.product.human.jooq.tables.pojos.RegisterExpeditingRocordEntity)source;
        pojo.into(this);
        return true;
    }
}
