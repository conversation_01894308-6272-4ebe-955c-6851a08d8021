/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IJobType extends Serializable {

    /**
     * Setter for <code>human-resource.t_job_type.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>human-resource.t_job_type.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>human-resource.t_job_type.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>human-resource.t_job_type.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>human-resource.t_job_type.f_code</code>. 类别编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>human-resource.t_job_type.f_code</code>. 类别编码
     */
    public String getCode();

    /**
     * Setter for <code>human-resource.t_job_type.f_organization_id</code>. 所属机构
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>human-resource.t_job_type.f_organization_id</code>. 所属机构
     */
    public String getOrganizationId();

    /**
     * Setter for <code>human-resource.t_job_type.f_status</code>. 类别状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>human-resource.t_job_type.f_status</code>. 类别状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>human-resource.t_job_type.f_job_count</code>. 关联职务数
     */
    public void setJobCount(Integer value);

    /**
     * Getter for <code>human-resource.t_job_type.f_job_count</code>. 关联职务数
     */
    public Integer getJobCount();

    /**
     * Setter for <code>human-resource.t_job_type.f_member_count</code>. 类别人数
     */
    public void setMemberCount(Integer value);

    /**
     * Getter for <code>human-resource.t_job_type.f_member_count</code>. 类别人数
     */
    public Integer getMemberCount();

    /**
     * Setter for <code>human-resource.t_job_type.f_name</code>.
     */
    public void setName(String value);

    /**
     * Getter for <code>human-resource.t_job_type.f_name</code>.
     */
    public String getName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IJobType
     */
    public void from(com.zxy.product.human.jooq.tables.interfaces.IJobType from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IJobType
     */
    public <E extends com.zxy.product.human.jooq.tables.interfaces.IJobType> E into(E into);
}
