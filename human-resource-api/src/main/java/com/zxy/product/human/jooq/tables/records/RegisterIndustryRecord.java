/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.records;


import com.zxy.product.human.jooq.tables.RegisterIndustry;
import com.zxy.product.human.jooq.tables.interfaces.IRegisterIndustry;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 注册报名从事行业
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RegisterIndustryRecord extends UpdatableRecordImpl<RegisterIndustryRecord> implements Record9<String, String, Integer, String, Long, String, Timestamp, String, Integer>, IRegisterIndustry {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>human-resource.t_register_industry.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_name</code>. 从事行业
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_name</code>. 从事行业
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_status</code>. 0 启用 1禁用
     */
    @Override
    public void setStatus(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_status</code>. 0 启用 1禁用
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_code</code>. 编码
     */
    @Override
    public void setCode(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_code</code>. 编码
     */
    @Override
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_create_id</code>. 创建人id
     */
    @Override
    public void setCreateId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_create_id</code>. 创建人id
     */
    @Override
    public String getCreateId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(6);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_modify_id</code>. 修改人id
     */
    @Override
    public void setModifyId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_modify_id</code>. 修改人id
     */
    @Override
    public String getModifyId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>human-resource.t_register_industry.f_is_del</code>. 0 删除 1正常
     */
    @Override
    public void setIsDel(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>human-resource.t_register_industry.f_is_del</code>. 0 删除 1正常
     */
    @Override
    public Integer getIsDel() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, String, Long, String, Timestamp, String, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, String, Long, String, Timestamp, String, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return RegisterIndustry.REGISTER_INDUSTRY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return RegisterIndustry.REGISTER_INDUSTRY.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return RegisterIndustry.REGISTER_INDUSTRY.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return RegisterIndustry.REGISTER_INDUSTRY.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return RegisterIndustry.REGISTER_INDUSTRY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return RegisterIndustry.REGISTER_INDUSTRY.CREATE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field7() {
        return RegisterIndustry.REGISTER_INDUSTRY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return RegisterIndustry.REGISTER_INDUSTRY.MODIFY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return RegisterIndustry.REGISTER_INDUSTRY.IS_DEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value7() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getModifyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getIsDel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value3(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value4(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value6(String value) {
        setCreateId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value7(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value8(String value) {
        setModifyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord value9(Integer value) {
        setIsDel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterIndustryRecord values(String value1, String value2, Integer value3, String value4, Long value5, String value6, Timestamp value7, String value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IRegisterIndustry from) {
        setId(from.getId());
        setName(from.getName());
        setStatus(from.getStatus());
        setCode(from.getCode());
        setCreateTime(from.getCreateTime());
        setCreateId(from.getCreateId());
        setModifyDate(from.getModifyDate());
        setModifyId(from.getModifyId());
        setIsDel(from.getIsDel());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IRegisterIndustry> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RegisterIndustryRecord
     */
    public RegisterIndustryRecord() {
        super(RegisterIndustry.REGISTER_INDUSTRY);
    }

    /**
     * Create a detached, initialised RegisterIndustryRecord
     */
    public RegisterIndustryRecord(String id, String name, Integer status, String code, Long createTime, String createId, Timestamp modifyDate, String modifyId, Integer isDel) {
        super(RegisterIndustry.REGISTER_INDUSTRY);

        set(0, id);
        set(1, name);
        set(2, status);
        set(3, code);
        set(4, createTime);
        set(5, createId);
        set(6, modifyDate);
        set(7, modifyId);
        set(8, isDel);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.human.jooq.tables.pojos.RegisterIndustryEntity)) {
            return false;
        }
        com.zxy.product.human.jooq.tables.pojos.RegisterIndustryEntity pojo = (com.zxy.product.human.jooq.tables.pojos.RegisterIndustryEntity)source;
        pojo.into(this);
        return true;
    }
}
