/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.human.jooq.tables.interfaces.IThirdPartyConfig;

import javax.annotation.Generated;


/**
 * 第三方系统单点登录配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ThirdPartyConfigEntity extends BaseEntity implements IThirdPartyConfig {

    private static final long serialVersionUID = 1L;

    private String  appId;
    private String  appSecret;
    private Long    sourceType;
    private String  sourceName;
    private Short   status;
    private String  domain;
    private Integer businessCode;
    private String  authorizeUrl;
    private String  modelId;
    private String  secretId;
    private Integer appidMultiple;

    public ThirdPartyConfigEntity() {}

    public ThirdPartyConfigEntity(ThirdPartyConfigEntity value) {
        this.appId = value.appId;
        this.appSecret = value.appSecret;
        this.sourceType = value.sourceType;
        this.sourceName = value.sourceName;
        this.status = value.status;
        this.domain = value.domain;
        this.businessCode = value.businessCode;
        this.authorizeUrl = value.authorizeUrl;
        this.modelId = value.modelId;
        this.secretId = value.secretId;
        this.appidMultiple = value.appidMultiple;
    }

    public ThirdPartyConfigEntity(
        String  id,
        String  appId,
        String  appSecret,
        Long    sourceType,
        String  sourceName,
        Short   status,
        String  domain,
        Integer businessCode,
        Long    createTime,
        String  authorizeUrl,
        String  modelId,
        String  secretId,
        Integer appidMultiple
    ) {
        super.setId(id);
        this.appId = appId;
        this.appSecret = appSecret;
        this.sourceType = sourceType;
        this.sourceName = sourceName;
        this.status = status;
        this.domain = domain;
        this.businessCode = businessCode;
        super.setCreateTime(createTime);
        this.authorizeUrl = authorizeUrl;
        this.modelId = modelId;
        this.secretId = secretId;
        this.appidMultiple = appidMultiple;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getAppId() {
        return this.appId;
    }

    @Override
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getAppSecret() {
        return this.appSecret;
    }

    @Override
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    @Override
    public Long getSourceType() {
        return this.sourceType;
    }

    @Override
    public void setSourceType(Long sourceType) {
        this.sourceType = sourceType;
    }

    @Override
    public String getSourceName() {
        return this.sourceName;
    }

    @Override
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    @Override
    public Short getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Short status) {
        this.status = status;
    }

    @Override
    public String getDomain() {
        return this.domain;
    }

    @Override
    public void setDomain(String domain) {
        this.domain = domain;
    }

    @Override
    public Integer getBusinessCode() {
        return this.businessCode;
    }

    @Override
    public void setBusinessCode(Integer businessCode) {
        this.businessCode = businessCode;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getAuthorizeUrl() {
        return this.authorizeUrl;
    }

    @Override
    public void setAuthorizeUrl(String authorizeUrl) {
        this.authorizeUrl = authorizeUrl;
    }

    @Override
    public String getModelId() {
        return this.modelId;
    }

    @Override
    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    @Override
    public String getSecretId() {
        return this.secretId;
    }

    @Override
    public void setSecretId(String secretId) {
        this.secretId = secretId;
    }

    @Override
    public Integer getAppidMultiple() {
        return this.appidMultiple;
    }

    @Override
    public void setAppidMultiple(Integer appidMultiple) {
        this.appidMultiple = appidMultiple;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ThirdPartyConfigEntity (");

        sb.append(getId());
        sb.append(", ").append(appId);
        sb.append(", ").append(appSecret);
        sb.append(", ").append(sourceType);
        sb.append(", ").append(sourceName);
        sb.append(", ").append(status);
        sb.append(", ").append(domain);
        sb.append(", ").append(businessCode);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(authorizeUrl);
        sb.append(", ").append(modelId);
        sb.append(", ").append(secretId);
        sb.append(", ").append(appidMultiple);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IThirdPartyConfig from) {
        setId(from.getId());
        setAppId(from.getAppId());
        setAppSecret(from.getAppSecret());
        setSourceType(from.getSourceType());
        setSourceName(from.getSourceName());
        setStatus(from.getStatus());
        setDomain(from.getDomain());
        setBusinessCode(from.getBusinessCode());
        setCreateTime(from.getCreateTime());
        setAuthorizeUrl(from.getAuthorizeUrl());
        setModelId(from.getModelId());
        setSecretId(from.getSecretId());
        setAppidMultiple(from.getAppidMultiple());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IThirdPartyConfig> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ThirdPartyConfigEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.human.jooq.tables.records.ThirdPartyConfigRecord r = new com.zxy.product.human.jooq.tables.records.ThirdPartyConfigRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.ID) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.ID, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.ID));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_ID) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_ID, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_ID));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_SECRET) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_SECRET, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APP_SECRET));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_TYPE) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_TYPE, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_NAME) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_NAME, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SOURCE_NAME));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.STATUS) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.STATUS, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.STATUS));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.DOMAIN) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.DOMAIN, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.DOMAIN));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.BUSINESS_CODE) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.BUSINESS_CODE, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.BUSINESS_CODE));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.CREATE_TIME, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.AUTHORIZE_URL) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.AUTHORIZE_URL, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.AUTHORIZE_URL));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.MODEL_ID) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.MODEL_ID, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.MODEL_ID));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SECRET_ID) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SECRET_ID, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.SECRET_ID));
                    }
                    if(row.indexOf(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APPID_MULTIPLE) > -1){
                        r.setValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APPID_MULTIPLE, record.getValue(com.zxy.product.human.jooq.tables.ThirdPartyConfig.THIRD_PARTY_CONFIG.APPID_MULTIPLE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
