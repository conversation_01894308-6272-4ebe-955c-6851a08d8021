package com.zxy.product.datacenter.util;
import javax.crypto.Cipher;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.Map.Entry;

public class SignatureUtil {


    /**
     * 创建SHA1签名
     * @param params
     * @return SHA1签名
     */
    public static String createSignature(SortedMap<String, String> params) {
        String s = sha1Encrypt(sortParams(params));
        return s;
//		return sha1Encrypt(sortParams(params));
    }

    /**
     * 创建SHA1签名
     * @param timeStamp 毫秒级时间戳
     * @param nonce 随机字符串取6位
     * @param secretId 密钥id
     * @param secretKey 密钥字符串
     * @return
     */
    public static String createSignature(String timeStamp, String nonce, String secretId,String secretKey) {
        SortedMap<String, String> signParams = new TreeMap<String, String>();
        signParams.put("secretKey", secretKey);
        signParams.put("timeStamp", timeStamp);
        signParams.put("nonce", nonce);
        signParams.put("secretId", secretId);
        return createSignature(signParams);
    }



    /**
     * 使用SHA1算法对字符串进行加密
     * @param str
     * @return
     */
    public static String sha1Encrypt(String str) {

        if (str == null || str.length() == 0) {
            return null;
        }

        char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f' };

        try {

            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));

            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;

            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }

            return new String(buf);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 生成时间戳
     * @return
     */
    public static String getTimeStamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 生成随机字符串
     * @return
     */
    public static String getRandomStr(int length) {
        String base = "0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        int number = 0;
        for (int i = 0; i < length; i++) {
            number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 根据参数名称对参数进行字典排序
     * @param params
     * @return
     */
    public static String sortParams(SortedMap<String, String> params) {
        StringBuffer sb = new StringBuffer();
        Set<Entry<String, String>> es = params.entrySet();
        Iterator<Entry<String, String>> it = es.iterator();
        while (it.hasNext()) {
            Entry<String, String> entry = it.next();
            String k = entry.getKey();
            String v = entry.getValue();
            sb.append(k + "=" + v + "&");
        }
        return sb.substring(0, sb.lastIndexOf("&"));
    }

    //将参数body用rawurlencode方式进行编码处理
    public static String rawurlencode(String body) {
        try {
            String encodedUrl = URLEncoder.encode(body, "UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("%21", "!")
                    .replaceAll("%27", "'")
                    .replaceAll("%28", "(")
                    .replaceAll("%29", ")")
                    .replaceAll("%7E", "~");
            return encodedUrl;
        } catch (UnsupportedEncodingException ex) {
            throw new RuntimeException("UTF-8 encoding not supported", ex);
        }
    }

    public static String encodeRequestBody(String body) {
        try {
            return URLEncoder.encode(body,"UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\*", "%2A")
                    .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String formatUrlMap(Map<String, String> paraMap, boolean urlEncode, boolean keyToLower) {
        String buff = "";
        Map<String, String> tmpMap = paraMap;
        try {
            List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(tmpMap.entrySet());
            // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
            Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
                @Override
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                    return (o1.getKey()).toString().compareTo(o2.getKey());
                }
            });
            // 构造URL 键值对的格式
            StringBuilder buf = new StringBuilder();
            for (Map.Entry<String, String> item : infoIds) {
                if (!org.springframework.util.StringUtils.isEmpty(item.getKey())) {
                    String key = item.getKey();
                    String val = item.getValue();
                    if (urlEncode) {
                        val = URLEncoder.encode(val, "utf-8");
                    }
                    if (keyToLower) {
                        buf.append(key.toLowerCase() + "=" + val);
                    } else {
                        buf.append(key + "=" + val);
                    }
                    buf.append("&");
                }

            }
            buff = buf.toString();
            if (buff.isEmpty() == false) {
                buff = buff.substring(0, buff.length() - 1);
            }
        } catch (Exception e) {
            return null;
        }
        return buff;
    }

    public static String encrypt(String text, String publicKeyStr) {
        byte[] tempBytes = new byte[0];
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            byte[] publicKeyByte = Base64.getDecoder().decode(publicKeyStr);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyByte);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            tempBytes = cipher.doFinal(text.getBytes("UTF-8"));
        } catch (Exception e) {
        }
        String s = Base64.getEncoder().encodeToString(tempBytes);
        return trans(s);
    }


    public static String trans(String s) {
        return s.replaceAll(" ", "%20")
                .replaceAll("!", "%21")
                .replaceAll("\"", "%22")
                .replaceAll("#", "%23")
                .replaceAll("\\$", "%24")
                .replaceAll("%", "%25")
                .replaceAll("&", "%26")
                .replaceAll("’", "%27")
                .replaceAll("\\(", "%28")
                .replaceAll("\\)", "%29")
                .replaceAll("\\*", "%2A")
                .replaceAll("\\+", "%2B")
                .replaceAll(",", "%2C")
                .replaceAll("-", "%2D")
                .replaceAll("/", "%2F")
                .replaceAll(":", "%3A")
                .replaceAll(";", "%3B")
                .replaceAll("<", "%3C")
                .replaceAll("=", "%3D")
                .replaceAll(">", "%3E")
                .replaceAll("\\?", "%3F")
                .replaceAll("@", "%40")
                .replaceAll("\\[", "%5B")
                .replaceAll("\\\\", "%5C")
                .replaceAll("]", "%5D")
                .replaceAll("\\^", "%5E")
                .replaceAll("_", "%5F")
                .replaceAll("\\{", "%7B")
                .replaceAll("\\|", "%7C")
                .replaceAll("}", "%7D")
                .replaceAll("~", "%7E");
    }


}
