package com.zxy.product.log.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.log.api.DisplayService;
import com.zxy.product.log.api.HotResourceVisitService;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.ErrorCode;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.entity.DailyActiveUser;
import com.zxy.product.log.entity.HotResourceVisit;
import com.zxy.product.log.entity.LoginCountDay;
import com.zxy.product.log.entity.LoginRecord;
import com.zxy.product.log.entity.Member;
import com.zxy.product.log.entity.MemberLoginDistribution;
import com.zxy.product.log.entity.ResourceVisit;
import com.zxy.product.log.web.util.DateUtil;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.ParamConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;

/**
 * <AUTHOR> zhouyong
 */
@Controller
@RequestMapping("/display")
public class DisplayController {

    private Logger LOGGER = LoggerFactory.getLogger(DisplayController.class);

    private static final String TASK_SCHEDULER = String.join("#", ZXY_LOG, DISPLAY_MODULE, "task-scheduler");


    private static final String CACHE_KEY_PREFIX = "data-screen-permitted";

    private HotResourceVisitService hotResourceVisitService;

    private Cache cache;

    private DisplayService displayService;

    private MessageSender messageSender;

    private Redis redis;

    @Value("#{'${smart.campus.ip}'.split(',')}")
    private Set<String> smartCampusIP;

    @Autowired
    public void setHotResourceVisitService(HotResourceVisitService hotResourceVisitService) {
        this.hotResourceVisitService = hotResourceVisitService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(CacheKeyConstant.ZXY_LOG,CacheKeyConstant.DISPLAY_MODULE);
    }

    @Autowired
    public void setDisplayService(DisplayService displayService) {
        this.displayService = displayService;
    }
    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/login-counts", method = RequestMethod.GET)
    public LoginRecord loginCounts() {
        return cache.get(CacheKeyConstant.REAL_TIME_LOGIN_COUNT, () -> displayService.loginCount(),60 * 5);
    }

    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/login-activity", method = RequestMethod.GET)
    public Map<String,List<LoginCountDay>> getLoginActivityView(Subject<Member> subject) {
        checkPermission(subject.getCurrentUserId());
        return displayService.getLoginActivityView();
    }


    /**
     * 获取当天登录人数、登录人次
     * */
    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/login-member-counts", method = RequestMethod.GET)
    public LoginRecord getLoginMemberCounts(Subject<Member> subject) {
        checkPermission(subject.getCurrentUserId());
        return displayService.getLoginMemberCounts();
    }

    /**
     * 获取前一天的登录情况分布
     * */
    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/member-login-distribution", method = RequestMethod.GET)
    public Map<String,MemberLoginDistribution> getMemberLoginDistribution(Subject<Member> subject) {
        checkPermission(subject.getCurrentUserId());
        return displayService.getMemberLoginDistribution();
    }
    

    @RequestMapping(value = "/smart-campus/login-counts", method = RequestMethod.GET)
    @JSON("*.*")
    public LoginRecord smartCampusLoginCounts() {
        return cache.get(CacheKeyConstant.REAL_TIME_LOGIN_COUNT, () -> displayService.loginCount(),60 * 5);
    }

    @RequestMapping(value = "/smart-campus/today-login-info", method = RequestMethod.GET)
    @Param()
    @JSON("totalVisit")
    public LoginRecord smartCampusTodayLoginInfo(RequestContext requestContext) {
        String ipAddr = getIpAddr(requestContext.getRequest());

        LOGGER.error("数据大屏同步智慧校园 -》 获取真实IP : {} ",ipAddr);
        if (!smartCampusIP.contains(ipAddr)){
            return null;
        }
        return displayService.getLoginMemberCounts();
    }

    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/dau-trends", method = RequestMethod.GET)
    public List<DailyActiveUser> dauTrends() {
        return displayService.dauTrends();
    }

    @Permitted
    @JSON("*.*")
    @RequestMapping(value = "/hot-resources-top3", method = RequestMethod.GET)
    public Map<String,List<ResourceVisit>> resourceTop3() {
        return displayService.resourceTop3();
    }

    /**
     * type:大屏定时任务类型
     * 0 -> 所有(8个), 1 -> 登录数据统计, 2 -> 学员日活趋势, 3 -> 热门主题, 4 -> 30日学习数据
     * 5 -> 学习时长走势, 6 -> 重点学习活动, 7 -> 在库资源, 8 -> 热门资源排行
     */
    @JSON("*.*")
    @Param(name = "type", required = true)
    @RequestMapping(value = "/task-scheduler", method = RequestMethod.GET)
    public Map<String,Object> taskScheduler(RequestContext context) {
        cache.get(TASK_SCHEDULER, () -> displayService.taskScheduler(context.getOptionalString("type").orElse("0")), 60);
        return ImmutableMap.of("result",200);
    }


    /**
     * @param rc
     * @return
     */
    @Permitted
    @JSON("*")
    @Param(name = "date",required = true)
    @RequestMapping(value = "/resource-visit-count",method = RequestMethod.GET)
    public Map<String,String> sendGenerateResourceVisitMessage(RequestContext rc){

        long dateTimestamp = DateUtil.date2Timestamp(rc.getString("date"));
        long time = Instant.ofEpochMilli(dateTimestamp).atZone(ZoneOffset.systemDefault()).plusDays(1).toLocalDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        messageSender.send(MessageTypeContent.GENERATE_RV_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(time));
        return ImmutableMap.of("status","ok");
    }


    private void checkPermission(String userId) {
        // if (!redis.process(jedis -> jedis.sismember(CACHE_KEY_PREFIX, userId))) {
        //     throw new UnprocessableException(com.zxy.product.system.content.ErrorCode.UserIsNotPermitted);
        // }
    }

    private String getIpAddr(HttpServletRequest request) {
        //此方式用于nginx服务器参数设置
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        if (request.getHeader("X-Real-IP") != null) {
            return request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @Permitted
    @JSON("*")
    @Param(name = "businessType", type = Integer.class)
    @RequestMapping(value = "/manage/hot-resource-visits",method = RequestMethod.GET)
    public List<HotResourceVisit> findHotResources(RequestContext ctx) {
        return hotResourceVisitService.queryByType(ctx.getOptionalInteger("businessType").orElse(HotResourceVisit.BUSINESS_TYPE_COURSE));
    }

    @Permitted
    @JSON("*")
    @Param(name = ParamConstant.ID, required = true)
    @Param(name = ParamConstant.STATUS, required = true, type = Integer.class)
    @RequestMapping(value = "/manage/hot-resource-visit/{id}",method = RequestMethod.PUT)
    public Map<String, Integer> updateStatus(RequestContext ctx) {
        Integer status = ctx.getInteger(ParamConstant.STATUS);
        HotResourceVisit resourceVisit = hotResourceVisitService.findById(ctx.getString(ParamConstant.ID));
        ErrorCode.RESOURCE_STATUS_NOT_UPDATED.throwIf(status.equals(resourceVisit.getStatus()));
        ErrorCode.RESOURCE_SHOW_COUNT_MORE_THEN_3.throwIf(status == HotResourceVisit.STATUS_SHOW && hotResourceVisitService.findShowCount(resourceVisit.getBusinessType()) == 3);
        return ImmutableMap.of(ParamConstant.SUCCESS, hotResourceVisitService.updateStatus(resourceVisit.getId(), resourceVisit.getBusinessType(), status));
    }
}
