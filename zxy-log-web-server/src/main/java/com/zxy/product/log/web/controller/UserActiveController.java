package com.zxy.product.log.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.entity.Member;
import com.zxy.product.log.api.UserActiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;
import java.util.Optional;


@Controller
@RequestMapping("/user-active")
public class UserActiveController {

    private UserActiveService userActiveService;

    @Autowired
    public void setUserActiveService(UserActiveService userActiveService) {
        this.userActiveService = userActiveService;
    }

    /**
     * 空接口-用于运维健康检测
     */
    @RequestMapping("/health-check")
    @JSON("*")
    public Map<String, String> healthCheck() {
        return ImmutableMap.of("status", "ok");
    }

    /**
     * 活跃墙数据
     *
     * @param subject
     */
    @RequestMapping(value = "/activite-wall", method = RequestMethod.GET)
    @Param(name = "userId")
    @Permitted
    @JSON("*.*")
    public List<List<Object>> activiteWall(RequestContext requestContext,Subject<Member> subject) {
        Optional<String> optionalUserId = requestContext.getOptionalString("userId");
        String userId = optionalUserId.orElse(subject.getCurrentUserId());
        return userActiveService.getUserActiveData(userId);
    }
}
