package com.zxy.product.human.async;

import com.zxy.product.human.async.config.*;
import org.jooq.Schema;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.human.jooq.HumanResource;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 *
 */
@EnableScheduling
@Configuration
@ComponentScan
//@SpringBootApplication
//@EnableAutoConfiguration
@Import({
        CommonDaoConfig.class,
        RPCClientConfig.class,
        MessageConfig.class,
        MongoConfig.class,
        RestTemplateConfig.class,
        DataSourceAutoConfiguration.class,
        JooqAutoConfiguration.class,
        TransactionConfig.class,
        RabbitAutoConfiguration.class,
        RedissonAutoConfiguration.class
})
public class HumanResourceAsyncServiceMain {

    @Bean
    public Schema schema() {
        return HumanResource.HUMAN_RESOURCE_SCHEMA; // jOOQ生成代码的根目录下与数据库同名的类
    }

    public static void main(String[] args) {
        new SpringApplicationBuilder(HumanResourceAsyncServiceMain.class).web(false).run(args);
    }
}
