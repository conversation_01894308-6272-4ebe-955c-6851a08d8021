package com.zxy.product.exam.async.task.szfn.constants;

/**
 * 数智赋能考试档案相关常量定义
 * 
 * <AUTHOR>
 * @since 2024-07-30
 */
public final class SzfnExamProfileConstants {
    
    /**
     * 私有构造函数，防止实例化
     */
    private SzfnExamProfileConstants() {
        throw new UnsupportedOperationException("常量类不允许实例化");
    }
    
    // ==================== 批量处理相关常量 ====================
    
    /**
     * 默认批量查询用户数量
     */
    public static final int DEFAULT_BATCH_SIZE = 15;
    
    /**
     * 最小批量大小
     */
    public static final int MIN_BATCH_SIZE = 1;
    
    /**
     * 最大批量大小
     */
    public static final int MAX_BATCH_SIZE = 100;
    
    // ==================== 重试机制相关常量 ====================
    
    /**
     * 默认最大重试次数
     */
    public static final int DEFAULT_MAX_RETRY_ATTEMPTS = 3;
    
    /**
     * 最大重试次数上限
     */
    public static final int MAX_RETRY_ATTEMPTS_LIMIT = 10;
    
    /**
     * 默认重试延迟时间（毫秒）
     */
    public static final long DEFAULT_RETRY_DELAY_MS = 1000L;
    
    /**
     * 最大重试延迟时间（毫秒）
     */
    public static final long MAX_RETRY_DELAY_MS = 30000L;
    
    /**
     * 指数退避因子
     */
    public static final double EXPONENTIAL_BACKOFF_FACTOR = 2.0;
    
    // ==================== 性能监控相关常量 ====================
    
    /**
     * 性能日志记录间隔（处理多少个批次后记录一次）
     */
    public static final int PERFORMANCE_LOG_INTERVAL = 50;
    
    /**
     * 详细进度日志记录间隔
     */
    public static final int PROGRESS_LOG_INTERVAL = 10;
    
    /**
     * 慢查询阈值（毫秒）
     */
    public static final long SLOW_QUERY_THRESHOLD_MS = 5000L;
    
    // ==================== 队列相关常量 ====================
    
    /**
     * 队列满时的等待时间（毫秒）
     */
    public static final long QUEUE_OFFER_TIMEOUT_MS = 5000L;
    
    /**
     * 队列使用率警告阈值（百分比）
     */
    public static final double QUEUE_USAGE_WARNING_THRESHOLD = 0.8;
    
    // ==================== 配置属性键名 ====================
    
    /**
     * 批量大小配置键
     */
    public static final String CONFIG_BATCH_SIZE = "szfn.exam.profile.batch.size";
    
    /**
     * 最大重试次数配置键
     */
    public static final String CONFIG_MAX_RETRY_ATTEMPTS = "szfn.exam.profile.max.retry.attempts";
    
    /**
     * 重试延迟配置键
     */
    public static final String CONFIG_RETRY_DELAY_MS = "szfn.exam.profile.retry.delay.ms";
    
    /**
     * 是否启用性能监控配置键
     */
    public static final String CONFIG_ENABLE_METRICS = "szfn.exam.profile.enable.metrics";
    
    /**
     * 是否启用详细日志配置键
     */
    public static final String CONFIG_ENABLE_VERBOSE_LOGGING = "szfn.exam.profile.enable.verbose.logging";
    
    // ==================== 异常分类相关常量 ====================
    
    /**
     * 可重试异常类型
     */
    public static final String[] RETRYABLE_EXCEPTION_PATTERNS = {
        "java.sql.SQLException",
        "java.net.SocketTimeoutException", 
        "java.net.ConnectException",
        "org.springframework.dao.DataAccessException",
        "java.util.concurrent.TimeoutException"
    };
    
    /**
     * 不可重试异常类型
     */
    public static final String[] NON_RETRYABLE_EXCEPTION_PATTERNS = {
        "java.lang.IllegalArgumentException",
        "java.lang.NullPointerException",
        "java.lang.SecurityException",
        "com.zxy.product.exam.exception.DataValidationException"
    };
    
    // ==================== 日志相关常量 ====================
    
    /**
     * 生产者线程名前缀
     */
    public static final String PRODUCER_THREAD_NAME_PREFIX = "SzfnExamProfileProducer";
    
    /**
     * 性能统计日志标记
     */
    public static final String PERFORMANCE_LOG_MARKER = "[PERFORMANCE]";
    
    /**
     * 重试日志标记
     */
    public static final String RETRY_LOG_MARKER = "[RETRY]";
    
    /**
     * 错误日志标记
     */
    public static final String ERROR_LOG_MARKER = "[ERROR]";
    
    /**
     * 警告日志标记
     */
    public static final String WARNING_LOG_MARKER = "[WARNING]";
}
