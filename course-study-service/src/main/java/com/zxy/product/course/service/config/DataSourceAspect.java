package com.zxy.product.course.service.config;


import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

@Aspect
@Component
public class DataSourceAspect implements Ordered {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Pointcut("@within(com.zxy.product.course.content.DataSource)")
    public void dataSourcePointCut() {}

    @Pointcut("@annotation(com.zxy.product.course.content.DataSource)")
    public void dataSourcePointCutMethod() {}

    @Around("dataSourcePointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        aroundProcess(point);
        try {
            return point.proceed();
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }

    @Around("dataSourcePointCutMethod()")
    public Object aroundMethod(ProceedingJoinPoint point) throws Throwable {
        aroundProcess(point);
        try {
            return point.proceed();
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }
    //具体数据源动态切换执行
    private void aroundProcess(ProceedingJoinPoint point){
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        DataSource ds = method.getAnnotation(DataSource.class);
        if(ds == null){     // 方法上没有注解则取类上的
            Class clazz = point.getTarget().getClass();
            ds = (DataSource) clazz.getAnnotation(DataSource.class);
        }
        if(ds == null){
            DynamicDataSource.setDataSource(DataSourceEnum.MASTER.getName());
            logger.debug("set default datasource is " + DataSourceEnum.MASTER.getName());
        }else {
            if(StringUtils.hasText(ds.name())){
                DynamicDataSource.setDataSource(ds.name());
                logger.debug("set datasource is " + ds.name());
            }else if(ds.type() != null){
                DynamicDataSource.setDataSource(ds.type().getName());
                logger.debug("set datasource is " + ds.type().getName());
            }else {
                DynamicDataSource.setDataSource(DataSourceEnum.MASTER.getName());
                logger.debug("set datasource is " + DataSourceEnum.MASTER.getName());
            }
        }
    }


    @Override
    public int getOrder() {
        return 1;
    }

}
