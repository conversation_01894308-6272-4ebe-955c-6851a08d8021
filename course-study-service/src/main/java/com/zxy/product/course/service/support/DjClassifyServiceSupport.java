package com.zxy.product.course.service.support;

import static com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO;
import static com.zxy.product.course.jooq.tables.DjClassify.DJ_CLASSIFY;
import static com.zxy.product.course.jooq.tables.DjResource.DJ_RESOURCE;

import java.util.ArrayList;
import java.util.List;

import org.jooq.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.CourseCategoryService;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.DjClassifyService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.DjClassify;
import com.zxy.product.course.entity.DjResource;
import com.zxy.product.course.entity.Organization;

/**
 * liushunan
 *
 */
@Service
public class DjClassifyServiceSupport implements DjClassifyService{

    private CommonDao<DjClassify> dao;

    private CommonDao<DjResource> resourceDao;

    private CommonDao<CourseInfo> courseDao;

    private CommonDao<Organization> orgDao;

    private CourseStudyProgressService courseStudyProgressService;

    private CourseCacheService courseCacheService;

    private CourseCategoryService courseCategoryService;

    @Autowired
	public void setCourseCategoryService(CourseCategoryService courseCategoryService) {
		this.courseCategoryService = courseCategoryService;
	}

	@Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
	public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
		this.courseStudyProgressService = courseStudyProgressService;
	}

	@Autowired
	public void setDao(CommonDao<DjClassify> dao) {
		this.dao = dao;
	}

    @Autowired
	public void setResourceDao(CommonDao<DjResource> resourceDao) {
		this.resourceDao = resourceDao;
	}

    @Autowired
	public void setCourseDao(CommonDao<CourseInfo> courseDao) {
		this.courseDao = courseDao;
	}

    @Autowired
    public void setOrgDao(CommonDao<Organization> orgDao) {
        this.orgDao = orgDao;
    }

	@Override
	public List<DjClassify> findList() {

		Field<String> courseId = COURSE_INFO.ID.as("courseId");
		Field<String> resourceId = DJ_RESOURCE.ID.as("resourceId");

		List<DjClassify> classifyList = new ArrayList<>();
		DjClassify classify = new DjClassify();
		List<DjResource> resourceList = new ArrayList<>();
		classify.setResourceList(resourceList);
		classify.setId("0");
		classify.setName("全部");
		classify.setSort(0);
		classifyList.add(classify);

		 dao.execute(x -> x
				.selectDistinct(Fields.start()
						.add(DJ_CLASSIFY.ID, DJ_CLASSIFY.NAME,DJ_CLASSIFY.SORT).end())
						.from(DJ_CLASSIFY))
						.orderBy(DJ_CLASSIFY.SORT.asc())
						.fetch(r -> {
							DjClassify cl = new DjClassify();
							cl.setId(r.getValue(DJ_CLASSIFY.ID));
							cl.setName(r.getValue(DJ_CLASSIFY.NAME));
							cl.setSort(r.getValue(DJ_CLASSIFY.SORT));
							List<DjResource> rl = new ArrayList<>();
							cl.setResourceList(rl);
							classifyList.add(cl);
							return classifyList;
						});
		 resourceDao.execute(r -> r
				.selectDistinct(Fields.start()
						.add(resourceId, DJ_RESOURCE.CLASSIFY_ID
								,courseId,COURSE_INFO.NAME,COURSE_INFO.DESCRIPTION_TEXT, COURSE_INFO.COVER_PATH)
						.end())
							.from(DJ_RESOURCE))
							.innerJoin(COURSE_INFO).on(DJ_RESOURCE.COURSE_ID.eq(COURSE_INFO.ID))
							.where(COURSE_INFO.DELETE_FLAG.ne(1).or(COURSE_INFO.DELETE_FLAG.isNull()))
						.fetch(r -> {
						String classifyId = r.getValue(DJ_RESOURCE.CLASSIFY_ID);
						for (DjClassify classify1 : classifyList) {
							if (classify1.getId().equals(classifyId)) {
								DjResource re = new DjResource();
								re.setId(r.getValue(resourceId));
								re.setClassifyId(classifyId);
								re.setCourseId(r.getValue(courseId));
								re.setCourseName(r.getValue(COURSE_INFO.NAME));
								re.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
								re.setDescription(r.getValue(COURSE_INFO.DESCRIPTION_TEXT));
								classify1.getResourceList().add(re);
								classify.getResourceList().add(re);
							}}
						return null;
				});
		 return classifyList;
	}

	@Override
	public CourseInfo detail(String id, String memberId) {

		CourseInfo course = courseDao.get(id);

		course.setDescription(course.getDescriptionText());

        String courseVersion = courseStudyProgressService.findByMemberIdAndCourseId(memberId, id)
                .map(r -> r.getCourseVersionId()).orElse(course.getVersionId());
        // 查询章节和章节详情
        course.setCourseChapters(courseCacheService.getCourseChapter(id, courseVersion));
        // 查询附件
        // course.setCourseAttachments(this.findCourseAttachmentByCourseId(id));
        course.setCategory(courseCategoryService.get(course.getCategoryId()));
        // 查询发布部门
        if(course.getReleaseOrgId()!=null)
            course.setReleaseOrg(orgDao.get(course.getReleaseOrgId()));

		return course;
	}

}
