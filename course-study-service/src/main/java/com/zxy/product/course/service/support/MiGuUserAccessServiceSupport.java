package com.zxy.product.course.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.MiGuUserAccessService;
import com.zxy.product.course.entity.MiGuUserAccess;
import com.zxy.product.course.util.DesensitizationUtil;
import com.zxy.product.course.util.EncryptUtil;
import org.jooq.Condition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;

@Service
public class MiGuUserAccessServiceSupport implements MiGuUserAccessService {

    private CommonDao<MiGuUserAccess> miGuUserAccessCommonDao;


    @Autowired
    public void setMiGuUserAccessCommonDao(CommonDao<MiGuUserAccess> miGuUserAccessCommonDao) {
        this.miGuUserAccessCommonDao = miGuUserAccessCommonDao;
    }

    @Override
    public void insertList(List<MiGuUserAccess> list) {
        miGuUserAccessCommonDao.insert(list);
    }

    @Override
    public void insert(MiGuUserAccess miGuUserAccess) {
        miGuUserAccessCommonDao.insert(miGuUserAccess);
    }

    @Override
    public PagedResult<MiGuUserAccess> findList(Optional<String> fullName, Optional<String> name, Optional<Integer> memberType, Optional<Long> startTime, Optional<Long> endTime, String code, Integer page, Integer pageSize, Optional<Integer> type) {
        List<Condition> conditionList = Stream.of(
                        fullName.map(MIGU_USER_ACCESS.USER_NAME::contains),
                        name.map(MEMBER.NAME::contains),
                        startTime.map(MIGU_USER_ACCESS.VISIT_TIME::ge),
                        endTime.map(MIGU_USER_ACCESS.VISIT_TIME::le),
                        Optional.of(code).map(MIGU_USER_ACCESS.CODE::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organizationUnit"); // 所属机构

        if (memberType.isPresent()){
            Integer integer = memberType.get();
            if (integer.equals(MiGuUserAccess.MEMBER_TYPE_INTERNAL)){
                conditionList.add(MEMBER.ID.isNotNull());
            }else {
                conditionList.add(MEMBER.ID.isNull());
            }
        }
        return miGuUserAccessCommonDao.fetchPage(page, pageSize, r ->
                        r.select(MIGU_USER_ACCESS.USER_NAME,
                                        MEMBER.NAME.as("code"),
                                        organizationTable.NAME.as("orgName"),
                                        ORGANIZATION.NAME,
                                        MIGU_USER_ACCESS.VISIT_TIME,
                                        MIGU_USER_ACCESS.LEAVE_TIME,
                                        MIGU_USER_ACCESS.WATCH_TIME,
                                        MIGU_USER_ACCESS.SOURCE)
                                .from(MIGU_USER_ACCESS)
                                .leftJoin(MEMBER).on(MIGU_USER_ACCESS.MEMBER_ID.eq(MEMBER.ID))
                                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                .leftJoin(organizationTable).on(organizationTable.COMPANY_ID.eq(ORGANIZATION.ID)).and(MEMBER.ORGANIZATION_ID.eq(organizationTable.ID))
                                .where(conditionList)
                , o -> {
                    MiGuUserAccess miGuUserAccess = new MiGuUserAccess();
                    miGuUserAccess.setUserName(EncryptUtil.aesEncrypt(o.getValue(MIGU_USER_ACCESS.USER_NAME), null));
                    miGuUserAccess.setName(DesensitizationUtil.desensitizeEmployeeId(o.getValue(MEMBER.NAME.as("code"))));
                    miGuUserAccess.setMechanismName(o.getValue(organizationTable.NAME.as("orgName")));
                    miGuUserAccess.setOrganizationName(o.getValue(ORGANIZATION.NAME));
                    miGuUserAccess.setVisitTime(o.getValue(MIGU_USER_ACCESS.VISIT_TIME));
                    miGuUserAccess.setLeaveTime(o.getValue(MIGU_USER_ACCESS.LEAVE_TIME));
                    miGuUserAccess.setWatchTime(o.getValue(MIGU_USER_ACCESS.WATCH_TIME));
                    miGuUserAccess.setSource(o.getValue(MIGU_USER_ACCESS.SOURCE));
                    miGuUserAccess.setMemberType(StringUtils.isEmpty(o.getValue(MEMBER.NAME.as("code"))) ? MiGuUserAccess.MEMBER_TYPE_EXTERNAL : MiGuUserAccess.MEMBER_TYPE_INTERNAL);
                    return miGuUserAccess;
                });
    }

    @Override
    public Integer getAttendNumber(String shortLink) {
        return miGuUserAccessCommonDao.execute(r->
                r.select(MIGU_USER_ACCESS.MEMBER_ID.countDistinct()).from(MIGU_USER_ACCESS).where(MIGU_USER_ACCESS.CODE.eq(shortLink)).fetchOne(MIGU_USER_ACCESS.MEMBER_ID.countDistinct(), Integer.class)
        );
    }

    @Override
    public PagedResult<MiGuUserAccess> findMergeList(Optional<String> fullName, Optional<String> name, Optional<Integer> memberType, Optional<Long> startTime, Optional<Long> endTime, String code, Integer page, Integer pageSize, Optional<Integer> type) {
        List<Condition> conditionList = Stream.of(
                        fullName.map(MIGU_USER_ACCESS.USER_NAME::contains),
                        name.map(MEMBER.NAME::contains),
                        startTime.map(MIGU_USER_ACCESS.VISIT_TIME::ge),
                        endTime.map(MIGU_USER_ACCESS.VISIT_TIME::le),
                        Optional.of(code).map(MIGU_USER_ACCESS.CODE::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organizationUnit"); // 所属机构

        if (memberType.isPresent()){
            Integer integer = memberType.get();
            if (integer.equals(MiGuUserAccess.MEMBER_TYPE_INTERNAL)){
                conditionList.add(MEMBER.ID.isNotNull());
            }else {
                conditionList.add(MEMBER.ID.isNull());
            }
        }
        return miGuUserAccessCommonDao.fetchPage(page, pageSize, r ->
                        r.select(MIGU_USER_ACCESS.USER_NAME,
                                        MEMBER.NAME.as("code"),
                                        organizationTable.NAME.as("orgName"),
                                        ORGANIZATION.NAME,
                                        MIGU_USER_ACCESS.VISIT_TIME.min(),
                                        MIGU_USER_ACCESS.WATCH_TIME.sum(),
                                        MIGU_USER_ACCESS.MEMBER_ID.count(),
                                        organizationTable.ORDER,
                                        organizationTable.CREATE_TIME)
                                .from(MIGU_USER_ACCESS)
                                .leftJoin(MEMBER).on(MIGU_USER_ACCESS.MEMBER_ID.eq(MEMBER.ID))
                                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                .leftJoin(organizationTable).on(organizationTable.COMPANY_ID.eq(ORGANIZATION.ID))
                                .where(conditionList)
                                .groupBy(MIGU_USER_ACCESS.MEMBER_ID)
                , o -> {
                    MiGuUserAccess miGuUserAccess = new MiGuUserAccess();
                    miGuUserAccess.setUserName(EncryptUtil.aesEncrypt(o.getValue(MIGU_USER_ACCESS.USER_NAME), null));
                    miGuUserAccess.setName(DesensitizationUtil.desensitizeEmployeeId(o.getValue(MEMBER.NAME.as("code"))));
                    miGuUserAccess.setMechanismName(o.getValue(organizationTable.NAME.as("orgName")));
                    miGuUserAccess.setOrganizationName(o.getValue(ORGANIZATION.NAME));
                    miGuUserAccess.setVisitTime(o.getValue(MIGU_USER_ACCESS.VISIT_TIME.min()));
                    miGuUserAccess.setWatchTime(o.getValue(MIGU_USER_ACCESS.WATCH_TIME.sum()).intValue());
                    miGuUserAccess.setVisits(o.getValue(MIGU_USER_ACCESS.MEMBER_ID.count()));
                    miGuUserAccess.setMemberType(StringUtils.isEmpty(o.getValue(MEMBER.NAME.as("code"))) ? MiGuUserAccess.MEMBER_TYPE_EXTERNAL : MiGuUserAccess.MEMBER_TYPE_INTERNAL);
                    miGuUserAccess.setOrgOrder(o.getValue(organizationTable.ORDER));
                    miGuUserAccess.setOrgCreateTime(ObjectUtils.isEmpty(o.getValue(organizationTable.CREATE_TIME))? System.currentTimeMillis() : o.getValue(organizationTable.CREATE_TIME));
                    return miGuUserAccess;
                });
    }

    @Override
    public Integer findNumber(String code) {
        return miGuUserAccessCommonDao.execute(r->r.select(MIGU_USER_ACCESS.USER_NAME.count()).from(MIGU_USER_ACCESS).where(MIGU_USER_ACCESS.CODE.eq(code)).fetchOne(MIGU_USER_ACCESS.USER_NAME.count()));
    }

    @Override
    public Integer findNumberDistinct(String code) {
        return miGuUserAccessCommonDao.execute(r->r.select(MIGU_USER_ACCESS.USER_NAME.countDistinct()).from(MIGU_USER_ACCESS).where(MIGU_USER_ACCESS.CODE.eq(code)).fetchOne(MIGU_USER_ACCESS.USER_NAME.countDistinct()));
    }
}
