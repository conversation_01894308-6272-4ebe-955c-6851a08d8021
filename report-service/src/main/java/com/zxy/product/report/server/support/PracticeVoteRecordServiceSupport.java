package com.zxy.product.report.server.support;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.PracticeVoteRecordService;
import com.zxy.product.report.entity.PracticeVoteRecord;
import static com.zxy.product.report.jooq.tables.PracticeVoteRecord.PRACTICE_VOTE_RECORD;

@Service
public class PracticeVoteRecordServiceSupport implements PracticeVoteRecordService {

	private CommonDao<PracticeVoteRecord> dao;
	
	@Autowired
	public void setDao(CommonDao<PracticeVoteRecord> dao) {
		this.dao = dao;
	}

	@Override
	public List<String> findVoted(String memberId) {
		return dao.execute(x->x.select(PRACTICE_VOTE_RECORD.PRACTICE_ID).from(PRACTICE_VOTE_RECORD)
				.where(PRACTICE_VOTE_RECORD.MEMBER_ID.eq(memberId))).fetch(PRACTICE_VOTE_RECORD.PRACTICE_ID);
	}

}
