package com.zxy.product.report.server.support;

import java.util.List;
import java.util.Optional;

import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.PracticeDateConfigService;
import com.zxy.product.report.entity.PracticeDateConfig;
import static com.zxy.product.report.jooq.tables.PracticeDateConfig.PRACTICE_DATE_CONFIG;

@Service
public class PracticeDateConfigServiceSupport implements PracticeDateConfigService {

	private CommonDao<PracticeDateConfig> dao;
	
	@Autowired
	public void setDao(CommonDao<PracticeDateConfig> dao) {
		this.dao = dao;
	}

	@Override
	public List<String> find(Optional<String> date) {
		Condition condition=date.map(PRACTICE_DATE_CONFIG.DATE::eq).orElse(DSL.trueCondition());
		return dao.execute(x->x.select(PRACTICE_DATE_CONFIG.DATE).from(PRACTICE_DATE_CONFIG).where(condition).orderBy(PRACTICE_DATE_CONFIG.ORDER.asc())).fetch(PRACTICE_DATE_CONFIG.DATE);
	}

}
