package com.zxy.product.report.server.support.anniversary;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.anniversary.AnniversaryService;
import com.zxy.product.report.content.CacheKeyConstant;
import com.zxy.product.report.content.ErrorCode;
import com.zxy.product.report.entity.*;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import static com.zxy.product.report.jooq.Tables.*;
import static java.util.stream.Collectors.toMap;

@Service
public class AnniversaryServiceSupport implements AnniversaryService {
    private CommonDao<Anniversary> anniversaryCommonDao;
    private CommonDao<AnniversaryChapter> chapterCommonDao;
    private CommonDao<AnniversaryVisitLog> logCommonDao;
    private CommonDao<AnniversaryChapterSection> sectionCommonDao;
    private CommonDao<AnniversaryExpress> expressCommonDao;
    private CommonDao<AnniversaryExpressItem> expressItemCommonDao;
    private Cache cache;

    @Autowired
    public void setAnniversaryCommonDao(CommonDao<Anniversary> anniversaryCommonDao) {
        this.anniversaryCommonDao = anniversaryCommonDao;
    }

    @Autowired
    public void setChapterCommonDao(CommonDao<AnniversaryChapter> chapterCommonDao) {
        this.chapterCommonDao = chapterCommonDao;
    }

    @Autowired
    public void setLogCommonDao(CommonDao<AnniversaryVisitLog> logCommonDao) {
        this.logCommonDao = logCommonDao;
    }

    @Autowired
    public void setSectionCommonDao(CommonDao<AnniversaryChapterSection> sectionCommonDao) {
        this.sectionCommonDao = sectionCommonDao;
    }

    @Autowired
    public void setExpressCommonDao(CommonDao<AnniversaryExpress> expressCommonDao) {
        this.expressCommonDao = expressCommonDao;
    }

    @Autowired
    public void setExpressItemCommonDao(CommonDao<AnniversaryExpressItem> expressItemCommonDao) {
        this.expressItemCommonDao = expressItemCommonDao;
    }

    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    @Override
    public List<Anniversary> findAnniversarys() {
        return cache.get(CacheKeyConstant.ANNIVERSARY_ITEM_LIST_APP, () -> {
            return anniversaryCommonDao.execute(x -> x.select(Fields.start().add(ANNIVERSARY).end())
                    .from(ANNIVERSARY)
                    .orderBy(ANNIVERSARY.SEQUENCE)
            ).fetch(r -> {
                Anniversary anniversary = new Anniversary();
                anniversary.setId(r.getValue(ANNIVERSARY.ID));
                anniversary.setCoverId(r.getValue(ANNIVERSARY.COVER_ID));
                anniversary.setCoverPath(r.getValue(ANNIVERSARY.COVER_PATH));
                anniversary.setName(r.getValue(ANNIVERSARY.NAME));
                anniversary.setVisits(r.getValue(ANNIVERSARY.VISITS));
                anniversary.setCreateTime(r.getValue(ANNIVERSARY.CREATE_TIME));
                anniversary.setTitle(r.getValue(ANNIVERSARY.TITLE));
                anniversary.setIsChapter(r.getValue(ANNIVERSARY.IS_CHAPTER));
                return anniversary;
            });
        }, 24 * 60 * 60);

    }

    @Override
    public List<AnniversaryChapter> findAnniversaryChapters(String anniversaryId) {
        return cache.get(CacheKeyConstant.ANNIVERSARY_CHAPTER_LIST_BY_ANNIVERSARY_ID + anniversaryId, () -> {
            Result<Record> records = chapterCommonDao.execute(x -> x.select(Fields.start()
                    .add(ANNIVERSARY_CHAPTER)
                    .add(ANNIVERSARY_CHAPTER_SECTION)
                    .end())
                    .from(ANNIVERSARY_CHAPTER)
                    .leftJoin(ANNIVERSARY_CHAPTER_SECTION).on(ANNIVERSARY_CHAPTER_SECTION.CHAPTER_ID.eq(ANNIVERSARY_CHAPTER.ID).and(ANNIVERSARY_CHAPTER_SECTION.CLIENT_TYPE.eq(AnniversaryChapterSection.CLIENT_TYPE_PC)))
                    .where(ANNIVERSARY_CHAPTER.ANNIVERSARY_ID.eq(anniversaryId))
                    .orderBy(ANNIVERSARY_CHAPTER.SEQUENCE.asc(), ANNIVERSARY_CHAPTER_SECTION.SEQUENCE.asc())
                    .fetch()
            );

            Map<String, List<AnniversaryChapterSection>> sectionMap = records.into(ANNIVERSARY_CHAPTER_SECTION).into(AnniversaryChapterSection.class)
                    .stream().filter(x -> x.getId() != null).collect(Collectors.groupingBy(AnniversaryChapterSection::getChapterId));
            List<AnniversaryChapter> chapterList = new ArrayList<>(records.into(ANNIVERSARY_CHAPTER).into(AnniversaryChapter.class)
                    .stream().collect(toMap(AnniversaryChapter::getId, p -> p, (p, q) -> q)).values());
            // 组装数据
            chapterList.forEach(chapter -> chapter.setAnniversaryChapterSections(sectionMap.get(chapter.getId())));

            // 排序
            chapterList.sort(Comparator.comparingInt(AnniversaryChapter::getSequence));

            return chapterList;
        }, 24 * 60 * 60);
    }

    @Override
    public int addVisits(String anniversaryId) {
        return anniversaryCommonDao.execute(x -> x.update(ANNIVERSARY)
                .set(ANNIVERSARY.VISITS, ANNIVERSARY.VISITS.add(1))
                .where(ANNIVERSARY.ID.eq(anniversaryId))
                .execute()
        );
    }

    @Override
    public AnniversaryVisitLog insertAnniversaryVisitLog(String anniversaryId, String memberId, String userAgent, String ipAddr, Optional<String> terminal) {
        AnniversaryVisitLog log = new AnniversaryVisitLog();
        log.forInsert();
        log.setAccessTime(log.getCreateTime());
        log.setMemberId(memberId);
        log.setIpAddr(ipAddr);
        log.setUserAgent(userAgent);
        terminal.ifPresent(log::setTerminal);
        log.setAnniversaryId(anniversaryId);
        logCommonDao.insert(log);
        return log;
    }

    @Override
    public String findOneAnniversaryId() {
        return anniversaryCommonDao.execute(x -> x.select(Fields.start().add(ANNIVERSARY.ID).end())
                .from(ANNIVERSARY)
                .limit(1)
        ).fetchOne(ANNIVERSARY.ID);
    }

    @Override
    public Anniversary findAnniversaryChaptersApp(String anniversaryId) {
        return cache.get(CacheKeyConstant.ANNIVERSARY_CHAPTER_LIST_BY_ANNIVERSARY_ID_APP + anniversaryId, () -> {
            Anniversary anniversary = anniversaryCommonDao.get(anniversaryId);
            List<AnniversaryChapter> anniversaryChapters = chapterCommonDao.execute(x -> x.select(Fields.start()
                    .add(ANNIVERSARY_CHAPTER)
                    .add(ANNIVERSARY_CHAPTER_SECTION)
                    .end())
                    .from(ANNIVERSARY_CHAPTER)
                    .leftJoin(ANNIVERSARY_CHAPTER_SECTION).on(ANNIVERSARY_CHAPTER_SECTION.CHAPTER_ID.eq(ANNIVERSARY_CHAPTER.ID).and(ANNIVERSARY_CHAPTER_SECTION.CLIENT_TYPE.eq(AnniversaryChapterSection.CLIENT_TYPE_APP)))
                    .where(ANNIVERSARY_CHAPTER.ANNIVERSARY_ID.eq(anniversaryId))
                    .orderBy(ANNIVERSARY_CHAPTER.SEQUENCE.asc(), ANNIVERSARY_CHAPTER_SECTION.SEQUENCE.asc())
                    .fetch(r -> {
                        AnniversaryChapter chapter = new AnniversaryChapter();
                        chapter.setId(r.getValue(ANNIVERSARY_CHAPTER.ID));
                        chapter.setTitle(r.getValue(ANNIVERSARY_CHAPTER.TITLE));
                        chapter.setName(r.getValue(ANNIVERSARY_CHAPTER.NAME));
                        chapter.setSequence(r.getValue(ANNIVERSARY_CHAPTER.SEQUENCE));
                        chapter.setAnniversaryId(r.getValue(ANNIVERSARY_CHAPTER.ANNIVERSARY_ID));
                        AnniversaryChapterSection section = new AnniversaryChapterSection();
                        section.setId(r.getValue(ANNIVERSARY_CHAPTER_SECTION.ID));
                        section.setAnniversaryId(r.getValue(ANNIVERSARY_CHAPTER_SECTION.ANNIVERSARY_ID));
                        section.setAudioId(r.getValue(ANNIVERSARY_CHAPTER_SECTION.AUDIO_ID));
                        section.setAudioPath(r.getValue(ANNIVERSARY_CHAPTER_SECTION.AUDIO_PATH));
                        section.setChapterId(r.getValue(ANNIVERSARY_CHAPTER_SECTION.CHAPTER_ID));
                        section.setClientType(r.getValue(ANNIVERSARY_CHAPTER_SECTION.CLIENT_TYPE));
                        section.setDescription(r.getValue(ANNIVERSARY_CHAPTER_SECTION.DESCRIPTION));
                        section.setImageId(r.getValue(ANNIVERSARY_CHAPTER_SECTION.IMAGE_ID));
                        section.setImagePath(r.getValue(ANNIVERSARY_CHAPTER_SECTION.IMAGE_PATH));
                        section.setLargeImagePath(r.getValue(ANNIVERSARY_CHAPTER_SECTION.LARGE_IMAGE_PATH));
                        chapter.setAnniversaryChapterSection(section);

                        return chapter;
                    })
            );
            anniversary.setAnniversaryChapters(anniversaryChapters);
            return anniversary;
        }, 24 * 60 * 60);
    }

    @Override
    public List<Anniversary> findAnniversarysPc() {
        return cache.get(CacheKeyConstant.ANNIVERSARY_CHAPTER_LIST_ALL_PC, () -> {
            Result<Record> records = chapterCommonDao.execute(x -> x.select(Fields.start()
                    .add(ANNIVERSARY)
                    .add(ANNIVERSARY_CHAPTER)
                    .add(ANNIVERSARY_CHAPTER_SECTION)
                    .end())
                    .from(ANNIVERSARY)
                    .leftJoin(ANNIVERSARY_CHAPTER).on(ANNIVERSARY_CHAPTER.ANNIVERSARY_ID.eq(ANNIVERSARY.ID))
                    .leftJoin(ANNIVERSARY_CHAPTER_SECTION).on(ANNIVERSARY_CHAPTER_SECTION.CHAPTER_ID.eq(ANNIVERSARY_CHAPTER.ID).and(ANNIVERSARY_CHAPTER_SECTION.CLIENT_TYPE.eq(AnniversaryChapterSection.CLIENT_TYPE_PC)))
                    .orderBy(ANNIVERSARY.SEQUENCE.asc(), ANNIVERSARY_CHAPTER.SEQUENCE.asc(), ANNIVERSARY_CHAPTER_SECTION.SEQUENCE.asc())
                    .fetch()
            );

            Map<String, List<AnniversaryChapterSection>> sectionMap = records.into(ANNIVERSARY_CHAPTER_SECTION).into(AnniversaryChapterSection.class)
                    .stream().filter(x -> x.getId() != null).collect(Collectors.groupingBy(AnniversaryChapterSection::getChapterId));

            List<AnniversaryChapter> chapterList = new ArrayList<>(records.into(ANNIVERSARY_CHAPTER).into(AnniversaryChapter.class)
                    .stream().collect(toMap(AnniversaryChapter::getId, p -> p, (p, q) -> q)).values());
            // 组装数据
            chapterList.forEach(chapter -> chapter.setAnniversaryChapterSections(sectionMap.get(chapter.getId())));

            List<Anniversary> list = new ArrayList<>(records.into(ANNIVERSARY).into(Anniversary.class)
                    .stream().collect(toMap(Anniversary::getId, p -> p, (p, q) -> q)).values());


            Map<String, List<AnniversaryChapter>> chapterMap = chapterList.stream().filter(c -> !StringUtils.isEmpty(c.getAnniversaryId())).collect(Collectors.groupingBy(AnniversaryChapter::getAnniversaryId));

            list.forEach(anniversary -> {
                List<AnniversaryChapter> anniversaryChapters = chapterMap.get(anniversary.getId());
                // 排序处理一下
                anniversaryChapters.sort(Comparator.comparingInt(AnniversaryChapter::getSequence));
                anniversary.setAnniversaryChapters(anniversaryChapters);
            });
            // 排序
            list.sort(Comparator.comparingInt(Anniversary::getSequence));
            return list;
        }, 24 * 60 * 60);
    }

    @Override
    public Map<String, String> insertAnniversaryData(List<AnniversaryChapterSection> sectionList, List<Anniversary> anniversaryList){
            if (!anniversaryList.isEmpty()) {
                anniversaryList.forEach(r -> {
                    anniversaryCommonDao.execute(x -> x.update(ANNIVERSARY)
                            .set(ANNIVERSARY.COVER_ID, r.getCoverId())
                            .set(ANNIVERSARY.COVER_PATH, r.getCoverPath())
                            .where(ANNIVERSARY.ID.eq(r.getSequence().toString()))
                            .execute()
                    );
                });
            }

            if (!sectionList.isEmpty()) {
                sectionCommonDao.insert(sectionList.stream().map(s -> {
                    s.forInsert();
                    String fileName = s.getName();
                    if (!StringUtils.isEmpty(fileName)) {
                        String[] nameArr = fileName.split("-");
                        s.setSequence(Integer.valueOf(nameArr[2]));
                        String chapterId = chapterCommonDao.execute(x -> x.select(ANNIVERSARY_CHAPTER.ID)
                                .from(ANNIVERSARY_CHAPTER)
                                .where(ANNIVERSARY_CHAPTER.ANNIVERSARY_ID.eq(s.getAnniversaryId())
                                        .and(ANNIVERSARY_CHAPTER.SEQUENCE.eq(Integer.valueOf(nameArr[1]))))
                                .limit(1)
                                .fetchOne(ANNIVERSARY_CHAPTER.ID)
                        );
                        s.setChapterId(chapterId);
                    }
                    if (AnniversaryChapterSection.CLIENT_TYPE_APP == s.getClientType()) {
                        String path = s.getImagePath();
                        s.setImagePath("/" + path);
                    }
                    return s;
                }).collect(Collectors.toList()));
            }
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("success", "true");
            return resultMap;
    }

    @Override
    public PagedResult<AnniversaryExpress> findExpressPage(int start, int pageSize, Optional<Integer> subject) {
        return cache.get(CacheKeyConstant.ANNIVERSARY_EXPRESS_PAGE + "#" + start + "#" + pageSize + "#" + subject.orElse(0), ()->{
            Integer count = expressCommonDao.execute(e->e.select(Fields.start().add(ANNIVERSARY_EXPRESS.ID.count()).end())
                    .from(ANNIVERSARY_EXPRESS).where(subject.map(ANNIVERSARY_EXPRESS.SUBJECT::eq).orElse(DSL.trueCondition())))
                    .fetchOne().getValue(0, Integer.class);

            List<AnniversaryExpress> list = expressCommonDao.execute(e->e.select(ANNIVERSARY_EXPRESS.fields()).from(ANNIVERSARY_EXPRESS)
                    .where(subject.map(ANNIVERSARY_EXPRESS.SUBJECT::eq).orElse(DSL.trueCondition())))
                    .orderBy(ANNIVERSARY_EXPRESS.SEQUENCE).limit(start, pageSize)
                    .fetchInto(ANNIVERSARY_EXPRESS).into(AnniversaryExpress.class);

            return PagedResult.create(count, list);
        }, 24 * 60 * 60);
    }

    @Override
    public AnniversaryExpress findExpressDetail(String expressId) {
        return cache.get(CacheKeyConstant.ANNIVERSARY_EXPRESS_DETAIL + "#" + expressId, ()-> {
            AnniversaryExpress express = expressCommonDao.getOptional(expressId)
                    .orElseThrow(() -> new UnprocessableException(ErrorCode.AnniversaryExpressNotExists));

            List<AnniversaryExpressItem> items = expressItemCommonDao.fetch(ANNIVERSARY_EXPRESS_ITEM.EXPRESS_ID.eq(expressId))
                    .stream().sorted(Comparator.comparing(AnniversaryExpressItem::getSequence)).collect(Collectors.toList());
            express.setItems(items);
            return express;
        }, 24 * 60 * 60);
    }
}
