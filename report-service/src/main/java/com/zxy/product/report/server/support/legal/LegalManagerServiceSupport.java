package com.zxy.product.report.server.support.legal;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.legal.LegalManagerService;
import com.zxy.product.report.entity.LegalManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.product.report.content.ErrorCode;
import com.zxy.product.report.entity.*;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.report.jooq.Tables.*;

import static com.zxy.product.report.jooq.Tables.LEGAL_MANAGER;

/**
 * <AUTHOR>
 * @date 2019/12/26
 */
@Service
public class LegalManagerServiceSupport implements LegalManagerService {

    private CommonDao<LegalQuestion> questionDao;
    private CommonDao<LegalManager> managerDao;
    private CommonDao<LegalQuestionAttr> attrDao;
    private CommonDao<LegalQuestionRecord> recordDao;
    private CommonDao<LegalQuestionRecordScore> scoreDao;
    private CommonDao<Organization> organizationDao;

    @Autowired
    public void setQuestionDao(CommonDao<LegalQuestion> questionDao) {
        this.questionDao = questionDao;
    }

    @Autowired
    public void setManagerDao(CommonDao<LegalManager> managerDao) {
        this.managerDao = managerDao;
    }

    @Autowired
    public void setAttrDao(CommonDao<LegalQuestionAttr> attrDao) {
        this.attrDao = attrDao;
    }

    @Autowired
    public void setRecordDao(CommonDao<LegalQuestionRecord> recordDao) {
        this.recordDao = recordDao;
    }

    @Autowired
    public void setScoreDao(CommonDao<LegalQuestionRecordScore> scoreDao) {
        this.scoreDao = scoreDao;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }

    @Override
    public LegalManager get(String memberId) {
        LegalManager legalManager = managerDao.fetchOne(LEGAL_MANAGER.MEMBER_ID.eq(memberId),
                LEGAL_MANAGER.COMPANY_TYPE.in(LegalManager.COMPANY_TYPE_MAJOR,LegalManager.COMPANY_TYPE_PROVINCE))
                .orElseThrow(()->new UnprocessableException(ErrorCode.LegalManagerNotExists));
        Organization organization = organizationDao.fetchOne(ORGANIZATION.ID.eq(legalManager.getCompanyId())).orElse(new Organization());
        legalManager.setCompanyName(organization.getName());
        return legalManager;
    }

    @Override
    public LegalManager getAll(String memberId) {

        // 获取管理员信息,总管理无法答题
        LegalManager legalManager = managerDao.fetchOne(LEGAL_MANAGER.MEMBER_ID.eq(memberId),
                LEGAL_MANAGER.COMPANY_TYPE.in(LegalManager.COMPANY_TYPE_MAJOR,LegalManager.COMPANY_TYPE_PROVINCE))
                .orElseThrow(()->new UnprocessableException(ErrorCode.LegalManagerNotExists));
        legalManager.setCompanyName(organizationDao.getOptional(legalManager.getCompanyId()).orElse(new Organization()).getName());

        // 获取问题信息
        List<LegalQuestion> questions = questionDao.fetch(LEGAL_QUESTION.COMPANY_TYPE.eq(legalManager.getCompanyType()));
        if (CollectionUtils.isEmpty(questions)) {
            throw new UnprocessableException(ErrorCode.LegalQuestionNotExists);
        }

        // 拿到问题ids条件
        Set<String> questionIds = questions.stream().map(LegalQuestion::getId).collect(Collectors.toSet());

        // 获取问题选项并按order排序，然后转为map，key为questionId
        Map<String,List<LegalQuestionAttr>> attrMap = attrDao.fetch(LEGAL_QUESTION_ATTR.QUESTION_ID.in(questionIds))
                .stream().sorted(Comparator.comparing(LegalQuestionAttr::getOrder))
                .collect(Collectors.groupingBy(LegalQuestionAttr::getQuestionId, LinkedHashMap::new, Collectors.toList()));

        // 获取当前用户的答题记录
        List<LegalQuestionRecord> records = recordDao.fetch(LEGAL_QUESTION_RECORD.QUESTION_ID.in(questionIds),
                LEGAL_QUESTION_RECORD.MEMBER_ID.eq(memberId));

        // 获取分数记录并按时间排序，然后转为map，key为recordId
        Set<String> recordIds = records.stream().map(LegalQuestionRecord::getId).collect(Collectors.toSet());
        Map<String,List<LegalQuestionRecordScore>> scoreMap = scoreDao.fetch(LEGAL_QUESTION_RECORD_SCORE.RECORD_ID.in(recordIds))
                .stream().sorted(Comparator.comparing(LegalQuestionRecordScore::getCreateTime))
                .collect(Collectors.groupingBy(LegalQuestionRecordScore::getRecordId, LinkedHashMap::new, Collectors.toList()));

        // 分数记录放入答题记录中
        records.forEach(r-> r.setScores(scoreMap.get(r.getId())));

        // 答题记录转为map，key为questionId
        Map<String,LegalQuestionRecord> recordMap = records.stream().collect(Collectors.toMap(LegalQuestionRecord::getQuestionId, r->r, (r1, r2) -> r2));

        // 问题选项，答题记录放入问题中
        questions.forEach(q->{
            q.setAttrs(attrMap.get(q.getId()));
            q.setRecord(recordMap.get(q.getId()));
            q.setMaxScore(q.getAttrs() == null ? null : q.getAttrs().stream().map(LegalQuestionAttr::getScore).max(Comparator.naturalOrder()).orElse(null));
            q.setMinScore(q.getAttrs() == null ? null : q.getAttrs().stream().map(LegalQuestionAttr::getScore).min(Comparator.naturalOrder()).orElse(null));
        });

        // 问卷及答题记录放入管理员中
        legalManager.setQuestions(questions.stream().sorted(Comparator.comparing(LegalQuestion::getOrder)).collect(Collectors.toList()));
        return legalManager;
    }

    @Override
    public List<LegalManager> getManageList(String currentUserId) {

        this.checkLeaderManager(currentUserId);

        // 获取管理列表
        return managerDao.execute(e->e.select(Fields.start().add(LEGAL_MANAGER).add(ORGANIZATION.NAME).end()).from(LEGAL_MANAGER)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(LEGAL_MANAGER.COMPANY_ID))
                .where(LEGAL_MANAGER.COMPANY_TYPE.in(LegalManager.COMPANY_TYPE_MAJOR,LegalManager.COMPANY_TYPE_PROVINCE),
                        LEGAL_MANAGER.COMPANY_ID.notEqual(LegalManager.TEST_COMPANY_ID))
                .orderBy(LEGAL_MANAGER.SCORE.desc(),LEGAL_MANAGER.STATUS.desc(),ORGANIZATION.ORDER.asc())).fetch(r->{
                    LegalManager lm = new LegalManager();
                    lm.setId(r.get(LEGAL_MANAGER.ID));
                    lm.setMemberId(r.get(LEGAL_MANAGER.MEMBER_ID));
                    lm.setCompanyId(r.get(LEGAL_MANAGER.COMPANY_ID));
                    lm.setCompanyType(r.get(LEGAL_MANAGER.COMPANY_TYPE));
                    lm.setCompanyName(r.get(ORGANIZATION.NAME));
                    lm.setStartTime(r.get(LEGAL_MANAGER.START_TIME));
                    lm.setSubmitTime(r.get(LEGAL_MANAGER.SUBMIT_TIME));
                    lm.setStatus(r.get(LEGAL_MANAGER.STATUS));
                    lm.setScore(r.get(LEGAL_MANAGER.SCORE));
                    return lm;
        });
    }

    @Override
    public LegalManager getDetail(String memberId, String currentUserId) {

        // 校验管理员权限
        this.checkLeaderManager(currentUserId);

        // 获取详情
        return this.getAll(memberId);
    }

    @Override
    public void saveScore(String memberId, String currentUserId, List<LegalQuestionRecordScore> scores) {

        // 校验管理员权限
        this.checkLeaderManager(currentUserId);

        // 获取分公司管理员答题记录
        List<LegalQuestionRecord> records = recordDao.fetch(LEGAL_QUESTION_RECORD.MEMBER_ID.eq(memberId));
        Set<String> recordIds = records.stream().map(LegalQuestionRecord::getId).collect(Collectors.toSet());

        // 根据答题记录ids获取改分记录，for update避免并发场景超出5次
        List<LegalQuestionRecordScore> scoreList = scoreDao.execute(e->e.select(LEGAL_QUESTION_RECORD_SCORE.fields())
                .from(LEGAL_QUESTION_RECORD_SCORE).where(LEGAL_QUESTION_RECORD_SCORE.RECORD_ID.in(recordIds))
                .forUpdate()).fetchInto(LegalQuestionRecordScore.class);

        // 合并新增改分记录
        scores.forEach(s->{
            s.forInsert();
            // 存储修改人ID
            s.setMemberId(currentUserId);
        });
        scoreList.addAll(scores);

        // 改分记录按创建时间倒序排列后分组，分组内第一个值为最后修改分数，计入总分计算
        Map<String,List<LegalQuestionRecordScore>> scoreMap = scoreList.stream().sorted(Comparator.comparing(LegalQuestionRecordScore::getCreateTime).reversed())
                .collect(Collectors.groupingBy(LegalQuestionRecordScore::getRecordId));

        // 验证每条答题记录的分数调整次数是否超出限制
        scoreMap.values().forEach(list->{
            if (list.size() > LegalQuestionRecordScore.MAX_SCORE_COUNT) {
                throw new UnprocessableException(ErrorCode.LegalScoreChangeOverflow);
            }
        });

        // 插入新增改分记录
        scoreDao.insert(scores);

        // 获取修改用户的所有答题记录，重新计算并更新总分
        double totalScore = 0;
        for (LegalQuestionRecord record : records) {
            // 改分记录如果为空，取原始得分，如果非空，取最新一条分数
            Double score = CollectionUtils.isEmpty(scoreMap.get(record.getId())) ? record.getScore() : scoreMap.get(record.getId()).get(0).getScore();
            if (score != null) {
                totalScore += score;
            }
        }
        double finalTotalScore = totalScore;
        managerDao.execute(e->e.update(LEGAL_MANAGER).set(LEGAL_MANAGER.SCORE, finalTotalScore).where(LEGAL_MANAGER.MEMBER_ID.eq(memberId))).execute();
    }

    private void checkLeaderManager(String currentUserId) {
        // 验证总管理员权限
        managerDao.fetchOne(LEGAL_MANAGER.MEMBER_ID.eq(currentUserId),
                LEGAL_MANAGER.COMPANY_TYPE.eq(LegalManager.COMPANY_TYPE_INNER))
                .orElseThrow(()->new UnprocessableException(ErrorCode.LegalLeaderManagerNotExists));
    }

}
