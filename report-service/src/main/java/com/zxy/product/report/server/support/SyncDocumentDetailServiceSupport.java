package com.zxy.product.report.server.support;

import static com.zxy.product.report.jooq.tables.SyncDocumentDetail.SYNC_DOCUMENT_DETAIL;
import static com.zxy.product.report.jooq.tables.SyncLog.SYNC_LOG;

import java.util.Optional;
import java.util.stream.Stream;

import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.SyncDocumentDetailService;
import com.zxy.product.report.entity.SyncDocumentDetail;

/**
 * mis数据同步文件详情
 * 
 * <AUTHOR>
 *
 */
@Service
public class SyncDocumentDetailServiceSupport implements SyncDocumentDetailService {
    
	private CommonDao<SyncDocumentDetail> dao;
	
	@Autowired
	public void setDao(CommonDao<SyncDocumentDetail> dao) {
		this.dao = dao;
	}
	
	/**
	 * 保存文件详情
	 */
	@Override
	public SyncDocumentDetail insert(Optional<String> code, Optional<Integer> type, Optional<String> syncTime,
			Optional<String> documentId, Optional<Integer> status, Optional<String> version) {
		
		SyncDocumentDetail syncDocumentDetail = new SyncDocumentDetail();
		syncDocumentDetail.forInsert();
		code.ifPresent(syncDocumentDetail::setCode);
		type.ifPresent(syncDocumentDetail::setType);
		syncTime.ifPresent(syncDocumentDetail::setSyncTime);
		status.ifPresent(syncDocumentDetail::setStatus);
		version.ifPresent(syncDocumentDetail::setVertion);
		documentId.ifPresent(syncDocumentDetail::setDocumentId);
		dao.insert(syncDocumentDetail);
		return syncDocumentDetail;
	}

    /**
     * 详情文件
     */
	@Override
	public Optional<SyncDocumentDetail> get(String version) {
		return dao.fetchOne(SYNC_DOCUMENT_DETAIL.VERTION.eq(version));
	}

    /**
     * 文件详情总数
     */
	@Override
	public Integer getCount(Optional<Integer> type, Optional<String> syncTime, Optional<Integer> status, Optional<String> version) {
		Condition conditions = Stream.of(
					               type.map(SYNC_DOCUMENT_DETAIL.TYPE::eq), 
					               syncTime.map(SYNC_DOCUMENT_DETAIL.SYNC_TIME::eq),
					               status.map(SYNC_DOCUMENT_DETAIL.STATUS::eq),
				                   version.map(SYNC_DOCUMENT_DETAIL.VERTION::eq))
				               .filter(Optional::isPresent)
				               .map(Optional::get)
				               .reduce((acc, item) -> acc.and(item))
				               .orElse(DSL.trueCondition());
		return dao.execute(d -> 
                         d.select(SYNC_DOCUMENT_DETAIL.ID.count()).from(SYNC_DOCUMENT_DETAIL).where(conditions).fetchOne(SYNC_DOCUMENT_DETAIL.ID.count()));
	}

	@Override
	public Optional<SyncDocumentDetail> getDocumentDetailByFileName(String fileName) {
		return dao.fetchOne(SYNC_DOCUMENT_DETAIL.VERTION.eq(fileName));
	}

	@Override
	public Integer checkCount(String id) {
		return dao.execute(d -> 
        d.select(SYNC_DOCUMENT_DETAIL.ID.count()).from(SYNC_DOCUMENT_DETAIL).where(SYNC_DOCUMENT_DETAIL.DOCUMENT_ID.eq(id), SYNC_DOCUMENT_DETAIL.STATUS.eq(1)).fetchOne(SYNC_DOCUMENT_DETAIL.ID.count()));
	}

	@Override
	public void delete(String documentId) {
		dao.delete(SYNC_DOCUMENT_DETAIL.DOCUMENT_ID.eq(documentId));
	}
}


