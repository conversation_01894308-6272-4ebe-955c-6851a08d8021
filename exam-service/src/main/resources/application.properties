spring.application.name=exam-service 

spring.datasource.url=*******************************************************
spring.datasource.username=root
spring.datasource.password=dreamtech%9
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.validation-query=SELECT 1
spring.datasource.initial-size=5
spring.datasource.max-active=10
spring.datasource.max-idle=5
spring.datasource.min-idle=1
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.time-between-eviction-runs-millis=5000
spring.datasource.min-evictable-idle-time-millis=60000

app.secretKey.sm4=e83d7a1c9b046f25d2c5e789a0b4f67d

spring.datasource.exam.url=*******************************************************
spring.datasource.exam.username=root
spring.datasource.exam.password=dreamtech%9
spring.datasource.exam.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.exam.test-while-idle=true
spring.datasource.exam.test-on-borrow=true
spring.datasource.exam.time-between-eviction-runs-millis=5000
spring.datasource.exam.min-evictable-idle-time-millis=60000
spring.datasource.exam.validation-query=SELECT 1


spring.datasource.juhe.exam.url=*******************************************************
spring.datasource.juhe.exam.username=root
spring.datasource.juhe.exam.password=dreamtech%9
spring.datasource.juhe.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.juhe.test-while-idle=true
spring.datasource.juhe.test-on-borrow=true
spring.datasource.juhe.time-between-eviction-runs-millis=5000
spring.datasource.juhe.min-evictable-idle-time-millis=60000
spring.datasource.juhe.validation-query=SELECT 1




spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=exam-server
dubbo.registry.address=zookeeper://127.0.0.1:2181
#dubbo.registry.address=zookeeper://localhost:2181
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
dubbo.application.timeout=700000
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

spring.redis.cluster = false
spring.redis.cluster.nodes = *************:30016
#spring.redis.cluster.nodes= ***************:30006
spring.redis.timeout=10000

spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1


dubbo.application.version=1

spring.rabbitmq.host=*************
spring.rabbitmq.port=30419
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

graphite.server=**************
graphite.port=30004

org.releated.tables=t_exam,f_organization_id,false,30901|t_paper_class,f_organization_id,false,30902|\
t_question,f_organization_id,false,30903|t_question_depot,f_organization_id,false,30904|\
t_research_questionary,f_organization_id,false,30905

dubbo.protocol.port=20883
#\u7F51\u7EDC\u90E8id
network.organizationId=96531

 #\u5E02\u573A\u90E8id
market.organizationId=96532

zyx.desensitize.accounts=admin,lihua