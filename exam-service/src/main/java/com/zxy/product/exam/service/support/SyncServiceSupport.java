package com.zxy.product.exam.service.support;

import static com.zxy.product.exam.jooq.Tables.EQUIPMENT_TYPE;
import static com.zxy.product.exam.jooq.Tables.ERROR_REASON;
import static com.zxy.product.exam.jooq.Tables.EXAM;
import static com.zxy.product.exam.jooq.Tables.PAPER_CLASS;
import static com.zxy.product.exam.jooq.Tables.PAPER_CLASS_QUESTION;
import static com.zxy.product.exam.jooq.Tables.PROFESSION;
import static com.zxy.product.exam.jooq.Tables.PROFESSION_LEVEL;
import static com.zxy.product.exam.jooq.Tables.QUESTION;
import static com.zxy.product.exam.jooq.Tables.QUESTION_ATTR;
import static com.zxy.product.exam.jooq.Tables.QUESTION_DEPOT;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.zxy.product.exam.entity.DeleteDataExam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.exam.api.PaperClassService;
import com.zxy.product.exam.api.QuestionService;
import com.zxy.product.exam.api.SyncService;
import com.zxy.product.exam.content.MessageCode;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.content.MsgData;
import com.zxy.product.exam.entity.ErrorReason;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.PaperClass;
import com.zxy.product.exam.entity.PaperClassQuestion;
import com.zxy.product.exam.entity.Question;
import com.zxy.product.exam.entity.QuestionAttr;
import com.zxy.product.exam.entity.QuestionDepot;
import com.zxy.product.exam.entity.SyncInfo;
import com.zxy.product.system.util.CodeUtils;

import javax.annotation.Resource;

@Service
public class SyncServiceSupport implements SyncService{

    private CommonDao<PaperClass> paperClassCommonDao;

    private CommonDao<PaperClassQuestion> paperClassQuestionCommonDao;

    private CommonDao<Exam> examCommonDao;

    private CommonDao<Question> questionCommonDao;

    private CommonDao<QuestionAttr> questionAttrCommonDao;

    private CommonDao<SyncInfo> syncInfoCommonDao;

    private CommonDao<QuestionDepot> questionDepotCommonDao;

    private CommonDao<ErrorReason> reasonCommonDao;

    private QuestionService questionService;

    private PaperClassService paperClassService;

    private MessageSender messageSender;
    @Resource
    private CommonDao<DeleteDataExam> deleteDataExamCommonDao;


    @Value("${network.organizationId}")
    private String orgId;

    @Override
    public MessageCode syncData(String professionName, String subProfessionName, String levelName, String equipMentName,String examBatch, PaperClass paper, List<Question> questions, SyncInfo info) {
        MessageCode code = new MessageCode();
        paper.setOrganizationId(orgId);
        com.zxy.product.exam.jooq.tables.Profession subProfession = PROFESSION.as("subProfession");
        List<Exam> exams = examCommonDao.execute(e ->
                e.select(EXAM.fields()).from(EXAM)
                        .leftJoin(PROFESSION).on(EXAM.PROFESSION_ID.eq(PROFESSION.ID))
                        .leftJoin(subProfession).on(EXAM.SUB_PROFESSION_ID.eq(subProfession.ID))
                        .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(EXAM.EQUIPMENT_TYPE_ID))
                        .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(EQUIPMENT_TYPE.NAME.eq(equipMentName),PROFESSION.NAME.eq(professionName)
                        ,subProfession.NAME.eq(subProfessionName),EXAM.EXAM_BATCH.eq(examBatch),
                        PROFESSION_LEVEL.LEVEL_NAME.eq(levelName),
                                EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE)).fetch().map(r ->{
                            Exam exam = r.into(EXAM).into(Exam.class);
                            return exam;
                }));
        if (exams.isEmpty()){
            code.setMessage(MessageCode.MSG_ERROR_NO_EXAM);
            code.setCode(MessageCode.CODE_ERROR_NO_EXAM);
        }else{
            List<MsgData> datas = new ArrayList<>();
            String examIds = "";
            for (Exam exam:exams){
                if (Exam.STATUS_NOT_PUBLISH == exam.getStatus() || Exam.STATUS_NOT_START == exam.getStatus() || Exam.STATUS_SIGNUPING == exam.getStatus() || Exam.STATUS_UPDATING == exam.getStatus()){ // 未发布，已开始，报名中，更新中
                    if (StringUtils.isNotEmpty(exam.getPaperClassId())){ // 修改的时候，需要先删掉试卷，试题，试题属性

                        PaperClass paperClass = paperClassCommonDao.get(exam.getPaperClassId());

                        List<PaperClassQuestion> paperClassQuestions = paperClassQuestionCommonDao.fetch(PAPER_CLASS_QUESTION.PAPER_CLASS_ID.eq(exam.getPaperClassId()));

                        List<String> ids = new ArrayList<String>();
                        for (PaperClassQuestion pcq:paperClassQuestions){
                            ids.add(pcq.getQuestionId());
                        }
                        questionCommonDao.delete(ids);
                        List<DeleteDataExam> collect = ids.stream().map(id -> DeleteDataExam.getDeleteData(QUESTION.getName(), id, "")).collect(Collectors.toList());
                        deleteDataExamCommonDao.insert(collect);

                        questionAttrCommonDao.delete(QUESTION_ATTR.QUESTION_ID.in(paperClassQuestions.stream().map(PaperClassQuestion::getQuestionId).collect(Collectors.toList())));
                        paperClassCommonDao.delete(paperClass.getId());

                    }

                    examIds += (exam.getId()+",");

                    List<Question> insertQuestions = new ArrayList<>();
                    List<PaperClassQuestion> paperClassQuestions = new ArrayList<>();
                    paper.forInsert();
                    paper.setStatus(PaperClass.IS_PUBLISH);

                    this.paperClassCommonDao.insert(paper);

                    this.parsingQuestionsRemoveDuplicate(questions,insertQuestions,paperClassQuestions,paper);

                    this.questionCommonDao.insert(insertQuestions);

                    insertPaperClassQuestion(paper.getId(), paperClassQuestions);
                    updateQuestionsToPublished(paper.getId(), paperClassQuestions);

                    List<QuestionAttr> questionAttrs = new ArrayList<>();
                    insertQuestions.forEach(t -> {
                        if (t.getQuestionAttrs()!=null){
                            t.getQuestionAttrs().forEach(qa -> {
                                qa.forInsert();
                                qa.setQuestionId(t.getId());
                                questionAttrs.add(qa);
                            });
                        }
                    });
                    questionAttrCommonDao.insert(questionAttrs);

                    if (Exam.STATUS_NOT_PUBLISH != exam.getStatus()){  // 未发布的时候，不改变考试状态
                        messageSender.send(
                                MessageTypeContent.EXAM_PAPERCLASS_INSERT,
                                MessageHeaderContent.ID,
                                exam.getId()
                        );
                        // 更新exam表状态和状态
                        exam.setStatus(Exam.STATUS_UPDATING);
                    }
                    exam.setPaperClassId(paper.getId());
                    exam.setModifyDate(null);
                    examCommonDao.update(exam);
                    MsgData data = new MsgData();
                    data.setPaperClass(paper.getName());
                    data.setExamId(exam.getId());
                    datas.add(data);
                }
            }
            info.setExamIds(examIds);
            this.saveSyncInfo(info);
            if (!datas.isEmpty()){
                code.setCode(MessageCode.CODE_SUCESS_SYNC);
                code.setMessage(MessageCode.MSG_SUCESS_SYNC);
                code.setDatas(datas);
            }else{
                code.setCode(MessageCode.CODE_ERROR_NO_EXAM);
                code.setMessage(MessageCode.MSG_ERROR_NO_EXAM);
            }
        }
        return code;

    }

    /**
     * 是用查询数据库的方式去重的，目前未使用
     * @param questions
     * @param insertQuestions
     * @param paperClassQuestions
     * @param paper
     */
    private void parsingQuestions(List<Question> questions, List<Question> insertQuestions, List<PaperClassQuestion> paperClassQuestions,PaperClass paper) {
        List<QuestionDepot> depots = new ArrayList<QuestionDepot>();
        String knowledgeId = null;
        for (Question q:questions){
            q.forInsert();
            PaperClassQuestion pcq = new PaperClassQuestion();
            pcq.forInsert();
            pcq.setQuestionId(q.getId());
            pcq.setPaperClassId(paper.getId());
            pcq.setParentId(q.getParentId());
            pcq.setScore(q.getScore());
            pcq.setSequence(q.getSequence());
            pcq.setIsFromSelected(1);
            paperClassQuestions.add(pcq);
            if (StringUtils.isNotEmpty(q.getSkillsElment())){
                List<QuestionDepot> skillsDepots = this.findByName(q.getSkillsElment());
                QuestionDepot skilldepot = new QuestionDepot();
                if (skillsDepots.isEmpty()){
                    skilldepot=this.getQuestionDepot(q,skilldepot);
                    depots.add(skilldepot);
                    this.questionDepotCommonDao.insert(skilldepot);
                    if (StringUtils.isNotEmpty(q.getKnowledge())){
                        QuestionDepot knowDepot = this.getQuestionDepot(q,skilldepot);
                        this.questionDepotCommonDao.insert(knowDepot);
                        knowledgeId = knowDepot.getId();
                    }else {
                        knowledgeId = skilldepot.getId();
                    }
                }else if (StringUtils.isNotEmpty(q.getKnowledge())){
                    List<QuestionDepot> knowDepots = this.findBySubName(q.getKnowledge());
                    if (knowDepots.isEmpty()){
                        QuestionDepot knowDepot = this.getQuestionDepot(q,skilldepot);
                        this.questionDepotCommonDao.insert(knowDepot);
                        knowledgeId = knowDepot.getId();
                    }else {
                        knowledgeId = knowDepots.get(0).getId();
                    }
                }
            }
            if (StringUtils.isNotEmpty(knowledgeId)){
                q.setQuestionDepotId(knowledgeId);
            }
            insertQuestions.add(q);
        }

    }

    /**
     * 解压传过来的试题，并去重questionsDepot和questionAttr
     * @param questions
     * @param insertQuestions
     * @param paperClassQuestions
     * @param paper
     */
    private void parsingQuestionsRemoveDuplicate(List<Question> questions, List<Question> insertQuestions, List<PaperClassQuestion> paperClassQuestions,PaperClass paper) {
        Map<String,Question> depotMap = this.removeSyncDuplicateMap(questions);

        List<QuestionDepot> depots = new ArrayList<>();
        Map<String,String> knowDepotId = new HashMap<>();

        if (!depotMap.isEmpty()){
            depots = this.removeSyncDuplicateList(depotMap,knowDepotId);
        }


        for (Question q:questions){

            String key = q.getSkillsElment()+ q.getKnowledge();
            if (!knowDepotId.isEmpty() && knowDepotId.get(key) != null && StringUtils.isNotEmpty(knowDepotId.get(key))){
                q.setQuestionDepotId(knowDepotId.get(key).toString());
                q.forInsert();
                insertQuestions.add(q);
            }else{
                continue; // 若知识点不存在，则不同步该试题
            }

            PaperClassQuestion pcq = new PaperClassQuestion();
            pcq.forInsert();
            pcq.setQuestionId(q.getId());
            pcq.setPaperClassId(paper.getId());
            pcq.setParentId(q.getParentId());
            pcq.setScore(q.getScore());
            pcq.setSequence(q.getSequence());
            pcq.setIsFromSelected(1);
            paperClassQuestions.add(pcq);
        }

        if (!depots.isEmpty()){

//            List<QuestionDepot> existedDepots = this.getByType(new Integer("3"));
//            List<QuestionDepot> insertDepots = existedDepots.isEmpty() ? depots : this.removeDuplicate(depots,existedDepots,insertQuestions);
//            this.questionDepotCommonDao.insert(insertDepots);
            this.questionDepotCommonDao.insert(depots);
        }

    }

    /**
     * 封装并返回去重后的试题目录List
     * @param depotMap
     * @param insertQuestions
     * @return
     */
    private List<QuestionDepot> removeSyncDuplicateList(Map<String, Question> depotMap,Map<String,String> knowDepotId) {
        List<QuestionDepot> depots = new ArrayList<>();
        Map<String,Question> skillMap = new HashMap<>();
        Map<String,QuestionDepot> skillDepotMap = new HashMap<>();
        for (Map.Entry<String,Question> entry : depotMap.entrySet()) { // 技能元素再去重
            Question question =entry.getValue();
            skillMap.put(question.getSkillsElment(),question);

        }
        for (Map.Entry<String,Question> entry : skillMap.entrySet()){  // 新增技能元素
            Question question =entry.getValue();
            QuestionDepot skillDepot = this.getQuestionDepot(question,null);
            depots.add(skillDepot);
            skillDepotMap.put(question.getSkillsElment(),skillDepot);
        }

        for (Map.Entry<String,Question> entry : depotMap.entrySet()) { // 新增知识点

            Question question =entry.getValue();
            String skillKey = question.getSkillsElment();
            QuestionDepot skillDepot = skillDepotMap.get(skillKey);
            QuestionDepot knowDepot = this.getQuestionDepot(question,skillDepot);


            depots.add(knowDepot);
            if (StringUtils.isNotEmpty(knowDepot.getId())){
                String key = question.getSkillsElment()+question.getKnowledge();
                knowDepotId.put(key,knowDepot.getId());
            }
        }
        return depots;
    }

    /**
     * 将同步过来的 知识点+技能元素作为map的key值做去重
     * @param questions
     * @return
     */
    private Map<String,Question> removeSyncDuplicateMap(List<Question> questions) {
        Map<String,Question> map =new HashMap<String,Question>();
//        map = questions.stream().filter( e -> e.getId() !=null).collect(Collectors.toMap(e -> e.getSkillsElment()+e.getKnowledge(),e->e,(t1,t2) -> t2));
        for (Question question:questions){
            if (StringUtils.isNotEmpty(question.getKnowledge()) && StringUtils.isNotEmpty(question.getSkillsElment())){  // 经业务确认，技能元素和知识点都是必填项
                map.put(question.getSkillsElment()+question.getKnowledge(),question);
            }

        }
        return map;
    }

    /**
     * 封装试题目录，要区分知识点和技能元素；技能元素是一级目录，知识点是二级目录
     * @param q
     * @param skilldepot
     * @return
     */
    private QuestionDepot getQuestionDepot(Question q,QuestionDepot skilldepot) {
        QuestionDepot depot = new QuestionDepot();
        depot.forInsert();
        depot.setType(3);
        depot.setName(q.getSkillsElment());
        depot.setState(1);
        depot.setAuthToLower(0);
        depot.setOrganizationId(orgId);
        depot.setCode(CodeUtils.getModuleCode("st"));
        depot.setPath(depot.getId()+",");
        if (skilldepot!=null && StringUtils.isNotEmpty(skilldepot.getId())){
            depot.setPath(depot.getId()+","+skilldepot.getId()+",");
            depot.setParentName(skilldepot.getName());
            depot.setParentId(skilldepot.getId());
            depot.setName(q.getKnowledge());
        }
        return depot;
    }

    private List<QuestionDepot> findBySubName(String name){
        return questionDepotCommonDao.fetch(QUESTION_DEPOT.TYPE.eq(3),QUESTION_DEPOT.NAME.eq(name),QUESTION_DEPOT.PARENT_ID.isNotNull());
    }

    private List<QuestionDepot> findByName(String name){
        Integer b = 3;
        return questionDepotCommonDao.fetch(QUESTION_DEPOT.TYPE.eq(b),QUESTION_DEPOT.NAME.eq(name));
    }


    /**
     * 移除重复的试题目录
     * @param depots
     * @param existedDepots
     * @param insertQuestions
     * @return
     */
    private List<QuestionDepot> removeDuplicate(List<QuestionDepot> depots,List<QuestionDepot> existedDepots,List<Question> insertQuestions) {
        List<QuestionDepot> duplicatDepots = new ArrayList<QuestionDepot>();
        for (QuestionDepot depot:depots){
            for (QuestionDepot existedDepot:existedDepots){
                if (StringUtils.isEmpty(depot.getParentId())&&depot.getName().equals(existedDepot.getName())){  // 技能元素去重
                    duplicatDepots.add(depot);
                    break;
                }
                if (StringUtils.isNotEmpty(depot.getParentId())
                        &&depot.getName().equals(existedDepot.getName())){  // 知识点的名称已存在
                    List<QuestionDepot> skills = existedDepots.stream().filter( ex -> ex.getName().equals(depot.getParentName())).collect(Collectors.toList());
                    if (!skills.isEmpty()){ // 知识点对应的技能元素也存在,该知识点去重之后，需要修改对应Question的depotId
                        duplicatDepots.add(depot);
                        insertQuestions.forEach( inq ->{
                            if ((inq.getSkillsElment()+inq.getKnowledge() ).equals(depot.getParentName()+depot.getName())){
                                inq.setQuestionDepotId(depot.getId());
                            }
                        });
                        break;
                    }
                }
            }
        }
        if (!duplicatDepots.isEmpty()){
            depots.removeAll(duplicatDepots);
        }
        return depots;
    }

    private List<QuestionDepot> getByType(Integer b){
        return questionDepotCommonDao.fetch(QUESTION_DEPOT.TYPE.eq(b));
    }

    @Override
    public void saveSyncInfo(SyncInfo info) {
        this.syncInfoCommonDao.insert(info);
    }

    @Override
    public Map<String, Object> saveReasons(Map<String,Object> map,String memberId) {
        List<ErrorReason> reasons = new ArrayList<>();
        List<ErrorReason> needDeleteReasons = new ArrayList<>();
        for (Map.Entry<String,Object> entry:map.entrySet()){
        	String examNum = entry.getKey();
            ErrorReason reason = new ErrorReason();
            reason.setMemberId(memberId);
            reason.setExamNum(examNum);
            if (entry.getValue()!=null){
                reason.setErrorReason(entry.getValue().toString());
            }
            if(StringUtils.isBlank(reason.getErrorReason())) {
            	needDeleteReasons.add(reason);
            	continue;
            }
            reason.forInsert();
            reasons.add(reason);
        }
        List<ErrorReason> existedReasons = this.reasonCommonDao.fetch(ERROR_REASON.MEMBER_ID.eq(memberId));
        List<ErrorReason> duplicateReason = new ArrayList<>();
        List<ErrorReason> updateReason = new ArrayList<>();
        existedReasons.stream().forEach(r->{
             Optional<ErrorReason> reason=reasons.stream().filter(rea -> r.getExamNum().equals(rea.getExamNum()) && memberId.equals(r.getMemberId())).findFirst();
             if (reason.isPresent()){
                 duplicateReason.add(reason.get());
                 r.setErrorReason(reason.get().getErrorReason());
                 updateReason.add(r);
             }
        });
        //删除无报错原因（如试题报错取消）的数据
        List<String> examNums = needDeleteReasons.stream().map(e -> e.getExamNum()).collect(Collectors.toList());
        if(!examNums.isEmpty()) {
        	this.reasonCommonDao.delete(ERROR_REASON.EXAM_NUM.in(examNums), ERROR_REASON.MEMBER_ID.eq(memberId));
        }

        if (!duplicateReason.isEmpty()){
            this.reasonCommonDao.update(updateReason);
            reasons.removeAll(duplicateReason);
        }
        if (!reasons.isEmpty()){
            this.reasonCommonDao.insert(reasons);
        }
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("success","success");
        return resultMap;
    }

    @Override
    public String findReason(String paraStr, String paperName,String memberId) {
        JSONArray array = JSONArray.parseArray(paraStr);
        List<String> examNums = new ArrayList<>();
        for (Object o:array){
            JSONObject json = JSONObject.parseObject(o.toString());
            String examNum = json.containsKey("examNum") ? json.getString("examNum") : "";
            examNums.add(examNum);
        }
        List<ErrorReason> reasons = this.reasonCommonDao.fetch(ERROR_REASON.EXAM_NUM.in(examNums),ERROR_REASON.MEMBER_ID.eq(memberId));
        JSONArray reArray = new JSONArray();
        reasons.stream().forEach(r ->{
            JSONObject json =new JSONObject();
            json.put("examNum",r.getExamNum());
            json.put("errorReason",r.getErrorReason());
            json.put("paperName",paperName);
            reArray.add(json);
        });
        return reArray.toJSONString();
    }

    private void insertPaperClassQuestion(String paperClassId, List<PaperClassQuestion> paperClassQuestions) {
        paperClassQuestionCommonDao.delete(PAPER_CLASS_QUESTION.PAPER_CLASS_ID.eq(paperClassId));
        List<PaperClassQuestion> list = paperClassQuestions.stream().map(q -> {
            q.forInsert();
            q.setPaperClassId(paperClassId);
            return q;
        }).collect(Collectors.toList());

        // 阅读题子题目的parentId 替换为paperCLassQuestion 的id
        Map<String, PaperClassQuestion> map = list.stream().collect(Collectors.toMap(PaperClassQuestion::getQuestionId, e -> e, (k, v) -> v));
        list = list.stream().map(t -> {
            if (t.getParentId() != null) {
                t.setParentId(map.get(t.getParentId()).getId());
            }
            return t;
        }).collect(Collectors.toList());

        List<List<PaperClassQuestion>> bigList = Lists.partition(list, 30);
        for (List<PaperClassQuestion> temp : bigList) {
            paperClassQuestionCommonDao.insert(temp);
        }
    }

    private void updateQuestionsToPublished(String paperClassId, List<PaperClassQuestion> paperClassQuestions) {
        List<Question> questions = paperClassCommonDao.execute(e -> {
            return e.selectDistinct(Fields.start().add(QUESTION).end())
                    .from(PAPER_CLASS_QUESTION)
                    .leftJoin(QUESTION).on(PAPER_CLASS_QUESTION.QUESTION_ID.eq(QUESTION.ID))
                    .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(PAPER_CLASS_QUESTION.PAPER_CLASS_ID))
                    .where(PAPER_CLASS.ID.eq(paperClassId)).fetchInto(Question.class);
        }).stream().map(q -> {
            q.setStatus(Question.PUBLISH);
            return q;
        }).collect(Collectors.toList());


        List<List<Question>> bigList = Lists.partition(questions, 30);
        for (List<Question> temp : bigList) {
            for (Question tmp:temp){
                tmp.setModifyDate(null);
            }
            questionCommonDao.update(temp);
            messageSender.send(
                    MessageTypeContent.EXAM_QUESTION_PUBLISH,
                    MessageHeaderContent.IDS,
                    temp.stream().filter(t ->
                            t.getOrganizationId() != null && t.getQuestionDepotId() != null)
                            .map(Question::getId).collect(Collectors.joining(",")));
        }

    }

    @Autowired
    public void setQuestionCommonDao(CommonDao<Question> questionCommonDao) {
        this.questionCommonDao = questionCommonDao;
    }

    @Autowired
    public void setPaperClassCommonDao(CommonDao<PaperClass> paperClassCommonDao) {
        this.paperClassCommonDao = paperClassCommonDao;
    }

    @Autowired
    public void setExamCommonDao(CommonDao<Exam> examCommonDao) {
        this.examCommonDao = examCommonDao;
    }

    @Autowired
    public void setQuestionAttrCommonDao(CommonDao<QuestionAttr> questionAttrCommonDao) {
        this.questionAttrCommonDao = questionAttrCommonDao;
    }

    @Autowired
    public void setPaperClassService(PaperClassService paperClassService) {
        this.paperClassService = paperClassService;
    }

    @Autowired
    public void setQuestionService(QuestionService questionService) {
        this.questionService = questionService;
    }

    @Autowired
    public void setPaperClassQuestionCommonDao(CommonDao<PaperClassQuestion> paperClassQuestionCommonDao) {
        this.paperClassQuestionCommonDao = paperClassQuestionCommonDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setSyncInfoCommonDao(CommonDao<SyncInfo> syncInfoCommonDao) {
        this.syncInfoCommonDao = syncInfoCommonDao;
    }

    @Autowired
    public void setQuestionDepotCommonDao(CommonDao<QuestionDepot> questionDepotCommonDao) {
        this.questionDepotCommonDao = questionDepotCommonDao;
    }

    @Autowired
    public void setReasonCommonDao(CommonDao<ErrorReason> reasonCommonDao) {
        this.reasonCommonDao = reasonCommonDao;
    }
}
