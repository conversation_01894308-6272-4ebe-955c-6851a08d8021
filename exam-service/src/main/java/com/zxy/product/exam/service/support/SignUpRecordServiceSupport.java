package com.zxy.product.exam.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.api.SignUpRecordService;
import com.zxy.product.exam.entity.SignupRecord;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.SIGNUP_RECORD;

@Service
public class SignUpRecordServiceSupport implements SignUpRecordService{

    private CommonDao<SignupRecord> signUpRecordServiceDao;

    @Autowired
    public void setSignUpRecordServiceDao(CommonDao<SignupRecord> signUpRecordServiceDao) {
        this.signUpRecordServiceDao = signUpRecordServiceDao;
    }


    @Override
    public List<SignupRecord> findList(Optional<String> memberId,
            Optional<String> examId) {

        return signUpRecordServiceDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                Fields.start()
                .add(SIGNUP_RECORD.ID)
                .add(SIGNUP_RECORD.MEMBER_ID)
                .add(SIGNUP_RECORD.CREATE_TIME)
                .add(SIGNUP_RECORD.AUDIT_STATUS)
                .end()
            );

            List<Condition> conditions = Stream.of(
                memberId.map(SIGNUP_RECORD.MEMBER_ID::eq),
                examId.map(SIGNUP_RECORD.EXAM_ID::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(SIGNUP_RECORD)
                    .where(conditions);
                return select;
            };

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(SIGNUP_RECORD.CREATE_TIME.desc());
            Result<Record> record = listSetp.fetch();

            return record.stream().map(t -> {
                SignupRecord signupRecord = new SignupRecord();
                signupRecord.setId(t.getValue(SIGNUP_RECORD.ID));
                signupRecord.setMemberId(t.getValue(SIGNUP_RECORD.MEMBER_ID));
                signupRecord.setCreateTime(t.getValue(SIGNUP_RECORD.CREATE_TIME));
                signupRecord.setAuditStatus(t.getValue(SIGNUP_RECORD.AUDIT_STATUS));
                return signupRecord;
            }).collect(Collectors.toList());

        });
    }


}
