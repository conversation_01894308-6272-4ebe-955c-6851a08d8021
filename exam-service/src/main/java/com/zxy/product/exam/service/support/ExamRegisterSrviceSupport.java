package com.zxy.product.exam.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.annotation.DataSource;
import com.zxy.product.exam.api.AnswerRecordService;
import com.zxy.product.exam.api.ExamRegisterSrvice;
import com.zxy.product.exam.entity.*;
import com.zxy.product.exam.service.util.GetTableUtil;
import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class ExamRegisterSrviceSupport implements ExamRegisterSrvice{

    private CommonDao<ExamRegist> examRegistCommonDao;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Value("${network.organizationId}")
    private String orgId;

    @Autowired
    public void setExamRegistCommonDao(CommonDao<ExamRegist> examRegistCommonDao) {
        this.examRegistCommonDao = examRegistCommonDao;
    }

    private AnswerRecordService answerRecordService;

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Override
    public List<ExamRegist> getExamCertificationByMemberId(String memberId,
                                                           Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        String[] allExamRegistStringTable = ExamRegist.STRING_EXAM_REGIST_ALL;

        for (String examRegistStringTable : allExamRegistStringTable) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);

            List<ExamRegist> examRegistForYear = examRegistCommonDao.execute(e -> {
                com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                                .add(examRegistTable.field("f_id", String.class))
                                .add(examRegistTable.field("f_top_score", Integer.class))
                                .add(examRegistTable.field("f_top_score_record_id", String.class))
                                .add(examRegistTable.field("f_pass_status", Integer.class))
                                .add(examRegistTable.field("f_status", Integer.class))
                                .add(EXAM.NAME)
                                .add(PROFESSION.ID)
                                .add(PROFESSION.NAME)
                                .add(subProfessionTable.ID)
                                .add(subProfessionTable.NAME)
                                .add(PROFESSION_LEVEL.ID)
                                .add(PROFESSION_LEVEL.LEVEL_NAME)
                                .add(EQUIPMENT_TYPE.ID)
                                .add(EQUIPMENT_TYPE.NAME)
                                .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(EXAM.EQUIPMENT_TYPE_ID))
                        .leftJoin(PROFESSION).on(PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                        .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(EXAM.SUB_PROFESSION_ID))
                        .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(EXAM.ORGANIZATION_ID.eq(orgId))
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE, Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                            examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));

                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));
                            // 专业
                            Profession professions = new Profession();
                            professions.setId(r.getValue(PROFESSION.ID));
                            professions.setName(r.getValue(PROFESSION.NAME));
                            exam.setProfession(professions);
                            // 子专业
                            Profession subProfessions = new Profession();
                            subProfessions.setId(r.getValue(subProfessionTable.ID));
                            subProfessions.setName(r.getValue(subProfessionTable.NAME));
                            exam.setSubProfession(subProfessions);
                            // 等级
                            ProfessionLevel professionLevels = new ProfessionLevel();
                            professionLevels.setId(r.getValue(PROFESSION_LEVEL.ID));
                            professionLevels.setLevelName(r.getValue(PROFESSION_LEVEL.LEVEL_NAME));
                            exam.setLevel(professionLevels);
                            // 设备
                            EquipmentType equipmentTypes = new EquipmentType();
                            equipmentTypes.setId(r.getValue(EQUIPMENT_TYPE.ID));
                            equipmentTypes.setName(r.getValue(EQUIPMENT_TYPE.NAME));
                            exam.setEquipmentType(equipmentTypes);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
                });
            examRegistList.addAll(examRegistForYear);
        }
        return examRegistList;
    }

    @Override
    public List<ExamRegist> getCloudExamCertificationByMemberId(String memberId,
            Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        String[] allExamRegistStringTable = ExamRegist.STRING_EXAM_REGIST_ALL;

        for (String examRegistStringTable : allExamRegistStringTable) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);

            List<ExamRegist> examRegistListForYear = examRegistCommonDao.execute(e -> {

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                        .add(examRegistTable.field("f_id", String.class))
                        .add(examRegistTable.field("f_status", Integer.class))
                        .add(examRegistTable.field("f_pass_status", Integer.class))
                        .add(EXAM.NAME)
                        .add(CLOUD_PROFESSION.ID)
                        .add(CLOUD_PROFESSION.NAME)
                        .add(CLOUD_LEVEL.ID)
                        .add(CLOUD_LEVEL.LEVEL_NAME)
                        .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                        .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.eq(Exam.EXAM_CLOUD_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));
                            // 专业
                            CloudProfession cloudProfession = new CloudProfession();
                            cloudProfession.setId(r.getValue(CLOUD_PROFESSION.ID));
                            cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                            exam.setCloudProfession(cloudProfession);
                            // 等级
                            CloudLevel cloudLevel = new CloudLevel();
                            cloudLevel.setId(r.getValue(CLOUD_LEVEL.ID));
                            cloudLevel.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
                            exam.setCloudLevel(cloudLevel);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
            });
            examRegistList.addAll(examRegistListForYear);
        }
        return examRegistList;
    }

    @Override
    public List<ExamRegist> getGridExamCertificationByMemberId(String memberId,
            Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        for (String stringYear : ExamRegist.STRING_ONLY_YEAR) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(ExamRegist.STRING_EXAM_REGIST+"_"+stringYear);

            List<ExamRegist> examRegistListForYear = examRegistCommonDao.execute(e -> {

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                        .add(examRegistTable.field("f_id", String.class))
                        .add(examRegistTable.field("f_status", Integer.class))
                        .add(examRegistTable.field("f_pass_status", Integer.class))
                        .add(EXAM.NAME)
                        .add(GRID_LEVEL.ID)
                        .add(GRID_LEVEL.LEVEL_NAME)
                        .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(GRID_LEVEL).on(GRID_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.eq(Exam.EXAM_GRID_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));

                            // 等级
                            GridLevel gridLevel = new GridLevel();
                            gridLevel.setId(r.getValue(GRID_LEVEL.ID));
                            gridLevel.setLevelName(r.getValue(GRID_LEVEL.LEVEL_NAME));
                            exam.setGridLevel(gridLevel);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
            });
            examRegistList.addAll(examRegistListForYear);
        }
        return examRegistList;
    }

    @Override
    public List<Exam> getCloudExamList(String memberId) {

        //单独查询 AUDIENCE_MEMBER
        List<String> itemIds = examRegistCommonDao.execute(e -> e.selectDistinct(AUDIENCE_MEMBER.ITEM_ID))
                .from(AUDIENCE_MEMBER)
                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                .fetch(AUDIENCE_MEMBER.ITEM_ID);

        List<Exam> list = examRegistCommonDao.execute(e -> {
            return e.selectDistinct(
                    Fields.start()
                    .add(CLOUD_EXAM.ID)
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.APPLICANT_START_TIME)
                    .add(EXAM.APPLICANT_END_TIME)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.DURATION)
                    .add(EXAM.STATUS)
                    .add(CLOUD_PROFESSION.ID)
                    .add(CLOUD_PROFESSION.NAME)
                    .add(CLOUD_LEVEL.ID)
                    .add(CLOUD_LEVEL.LEVEL_NAME)
                    .add(CLOUD_EXAM.NUM)
                    .end())
                    .from(CLOUD_EXAM)
                    .leftJoin(EXAM).on(CLOUD_EXAM.EXAM_ID.eq(EXAM.ID))
                    .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                    .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
                    .whereExists(e.select(AUDIENCE_OBJECT.ID).from(AUDIENCE_OBJECT)
                            .where(AUDIENCE_OBJECT.TARGET_ID.eq(CLOUD_EXAM.EXAM_ID))
                            .and(AUDIENCE_OBJECT.ITEM_ID.in(itemIds))
                            .and(AUDIENCE_OBJECT.TYPE.eq(AudienceObject.TYPE_EXAM))
                            .limit(1)
                    )
                    .fetch().stream().map(r -> {
                        Exam exam = new Exam();
                        exam.setId(r.getValue(EXAM.ID));
                        exam.setName(r.getValue(EXAM.NAME));
                        exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                        exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                        exam.setStartTime(r.getValue(EXAM.START_TIME));
                        exam.setEndTime(r.getValue(EXAM.END_TIME));
                        exam.setDuration(r.getValue(EXAM.DURATION));
                        exam.setStatus(r.getValue(EXAM.STATUS));

                        exam.setNum(r.getValue(CLOUD_EXAM.NUM));
                        // 专业
                        CloudProfession cloudProfession = new CloudProfession();
                        cloudProfession.setId(r.getValue(CLOUD_PROFESSION.ID));
                        cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                        exam.setCloudProfession(cloudProfession);
                        // 等级
                        CloudLevel cloudLevel = new CloudLevel();
                        cloudLevel.setId(r.getValue(CLOUD_LEVEL.ID));
                        cloudLevel.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
                        exam.setCloudProfession(cloudProfession);
                        return exam;
                    }).collect(Collectors.toList());
        });
//        1-1 中级客户模拟，1-2 中级客户正式，2-1 中级方案模拟，2-2 中级方案正式，3-1 中级交付模拟，3-2 中级交付正式'
        if(list.stream().filter(a -> a.getNum().equals("1-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("1-1")).collect(Collectors.toList());
        if(list.stream().filter(a -> a.getNum().equals("2-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("2-1")).collect(Collectors.toList());
        if(list.stream().filter(a -> a.getNum().equals("3-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("3-1")).collect(Collectors.toList());

        return list;
    }

    @Override
    public Integer getPassCount(String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                .from(examRegistTable)
                .where(examRegistTable.field("f_pass_status", Integer.class).eq(ExamRegist.PASS_STATUS_YES)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }


//    @Override
//    public ExamRegist getExamRegistByMemberIdAndExamId(String memberId, String examId) {
//
//        TableImpl<?> examRegistTable = getExamRegistTable(answerRecordService.getExamRegistStringTable(examId));
//
//        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
//                Fields.start()
//                .add(examRegistTable.fields())
//                .end())
//                .from(examRegistTable)
//                .where(examRegistTable.field("f_member_id", String.class).eq(memberId)
//                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
//                .limit(1)
//                .fetch(r -> {
//                    ExamRegist examRegist = new ExamRegist();
//                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
//                    examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
//                    examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
//                    examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
//                    examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
//                    examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
//                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
//                    examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
//                    examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));
//                    examRegist.setCreateTime(r.getValue(examRegistTable.field("f_create_time", Long.class)));
//                    examRegist.setType(r.getValue(examRegistTable.field("f_type", Integer.class)));
//                    return examRegist;
//                }));
//        if (examRegistList != null && examRegistList.size() > 0)
//            return examRegistList.get(0);
//        return new ExamRegist();
//    }

    @Override
    public List<ExamRegist> getExamRegistList(String currentUserId, List<String> examIds) {

        List<ExamRegist> examRegistLists = new ArrayList<ExamRegist>();

        for (String examId : examIds) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

            List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                    Fields.start()
                    .add(examRegistTable.field("f_top_score", Integer.class))
                    .add(examRegistTable.field("f_exam_id", String.class))
                    .add(examRegistTable.field("f_status", Integer.class))
                    .add(EXAM.ID)
                    .add(EXAM.SHOW_ANSWER_RULE)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .add(EXAM.NAME)
                    .add(EXAM.START_TIME)
                    .add(EXAM.CREATE_TIME)
                    .end())
                    .from(examRegistTable)
                    .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                    .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                    .fetch(r -> {
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                        examRegist.setCreateTime(System.currentTimeMillis());
                        examRegist.setStartTime(r.getValue(EXAM.CREATE_TIME));
                        Exam exam = new Exam();
                        exam.setId(r.getValue(EXAM.ID));
                        exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                        exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
                        exam.setName(r.getValue(EXAM.NAME));
                        exam.setStartTime(r.getValue(EXAM.START_TIME));
                        examRegist.setExam(exam);
                        return examRegist;
                    }));

            examRegistLists.addAll(examRegistList);
        }

        examRegistLists = examRegistLists.stream().sorted(Comparator.comparing(ExamRegist::getStartTime).reversed()).collect(Collectors.toList());

        return examRegistLists;
    }



    @Override
    public List<ExamRegist> getNewenergyScoreList(String currentUserId, List<String> examIds) {

        List<ExamRegist> examRegistLists = new ArrayList<ExamRegist>();

        for (String examId : examIds) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

            List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                    Fields.start()
                    .add(examRegistTable.field("f_exam_id", String.class))
                    .add(examRegistTable.field("f_top_score", Integer.class))
                    .add(EXAM.NAME)
                    .end())
                    .from(examRegistTable)
                    .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                    .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                    .fetch(r -> {
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setExamName(r.getValue(EXAM.NAME));
                        Integer topScore = r.getValue(examRegistTable.field("f_top_score", Integer.class));
                        examRegist.setTopScore(topScore == null ? 0 : topScore);
                        return examRegist;
                    }));

            examRegistLists.addAll(examRegistList);
        }
        examRegistLists = examRegistLists.stream().sorted(Comparator.comparing(ExamRegist::getTopScore).reversed()).collect(Collectors.toList());
        return examRegistLists;
    }



    @Override
    public ExamRegist getNewenergyScore(String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_exam_id", String.class))
                .add(examRegistTable.field("f_top_score", Integer.class))
                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                    examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                    return examRegist;
                }));

        return examRegistList != null && examRegistList.size() > 0 ? examRegistList.get(0) : new ExamRegist();
    }


    @Override
    public ExamRegist getExamRegisterByExamIdAndMemberId(String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_id", String.class)).add(examRegistTable.field("f_exam_id", String.class))
                .add(examRecordTable.field("f_id", String.class)).add(examRecordTable.field("f_member_id", String.class))
                .add(examRecordTable.field("f_start_time", Long.class)).add(examRecordTable.field("f_submit_time", Long.class))
                .add(examRecordTable.field("f_score", Integer.class)).add(examRecordTable.field("f_status", Integer.class))
                .add(examRecordTable.field("f_paper_instance_id", String.class)).add(EXAM.DURATION)
                .add(examRecordTable.field("f_order_content", String.class))
                .end())
                .from(examRegistTable)
                .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));

                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                    examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                    examRegist.setExamRecord(examRecord);

                    Exam exam = new Exam();
                    exam.setDuration(r.getValue(EXAM.DURATION));
                    examRegist.setExam(exam);
                    return examRegist;
                }));

        return examRegistList != null && examRegistList.size() > 0 ? examRegistList.get(0) : null;
    }


    @Override
    public String getExamRecordIdByExamIdAndMemberId(String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<String> examRecordIdList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_top_score_record_id", String.class))
                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(examRegistTable.field("f_top_score_record_id", String.class)));

        return examRecordIdList != null && examRecordIdList.size() > 0 ? examRecordIdList.get(0) : null;
    }


    @Override
    public ExamRegist getExamRegisterForCourseStudy(String memberId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        return examRegistCommonDao.execute(x -> x.select(examRegistTable.field("f_top_score", Integer.class),
                examRegistTable.field("f_pass_status", Integer.class),
                examRegistTable.field("f_id", String.class)).from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(memberId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .limit(1)
                .fetchOptional(r -> {
                    ExamRegist er = new ExamRegist();
                    er.setTopScore(r.get(examRegistTable.field("f_top_score", Integer.class)));
                    er.setPassStatus(r.get(examRegistTable.field("f_pass_status", Integer.class)));
                    er.setId(r.get(examRegistTable.field("f_id", String.class)));
                    return er;
                }).orElse(null)
        );
    }

    @Override
    public List<String> getPersonalExamIds() {
        return examRegistCommonDao.execute(e -> e.selectDistinct(
                Fields.start()
                .add(PERSONAL_DEPOT.EXAM_ID)
                .end())
                .from(PERSONAL_DEPOT)
                .fetch(PERSONAL_DEPOT.EXAM_ID));
    }

    @Override
    public List<String> getQuestionDepotListByExamId(String examId) {
        return examRegistCommonDao.execute(e -> e.selectDistinct(
                Fields.start()
                .add(PERSONAL_DEPOT.QUESTION_DPORT_ID)
                .end())
                .from(PERSONAL_DEPOT)
                .where(PERSONAL_DEPOT.EXAM_ID.eq(examId))
                .fetch(PERSONAL_DEPOT.QUESTION_DPORT_ID));
    }

    @Override
    @DataSource
    public Integer getSubmitPaperNumber(String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                                           .from(examRegistTable)
                                           .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                                           .and(examRegistTable.field("f_pass_status", Integer.class).isNotNull())
                                           .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }

    @Override
    @DataSource
    public Integer getPassNumber(String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                                           .from(examRegistTable)
                                           .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                                           .and(examRegistTable.field("f_pass_status", Integer.class).eq(ExamRegist.PASS_STATUS_YES))
                                           .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }


}
