package com.zxy.product.exam.service.support;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.zxy.product.exam.api.*;
import com.zxy.product.exam.service.util.GetTableUtil;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.exam.content.ErrorCode;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.entity.AnswerRecord;
import com.zxy.product.exam.entity.MarkConfig;
import com.zxy.product.exam.entity.MarkRecord;
import com.zxy.product.exam.entity.Question;
import com.zxy.product.exam.entity.QuestionCopy;
import com.zxy.product.exam.entity.ToDo;
import org.springframework.util.CollectionUtils;

@Service
public class MarkRecordServiceSupport implements MarkRecordService {

    private CommonDao<MarkConfig> markConfigDao;

    private CommonDao<MarkRecord> markRecordDao;

    private CommonDao<AnswerRecord> answerRecordDao;

    private CommonDao<QuestionCopy> questionCopyDao;

    private CommonDao<ToDo> toDoDao;

    private MarkConfigService markConfigService;

    private AnswerRecordService answerRecordService;

    private ExamRecordService examRecordService;

    private ToDoService toDoService;

    private MessageSender messageSender;

    private QuestionCopyService questionCopyService;

    private MarkRecordService markRecordService;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setMarkConfigDao(CommonDao<MarkConfig> markConfigDao) {
        this.markConfigDao = markConfigDao;
    }

    @Autowired
    public void setToDoDao(CommonDao<ToDo> toDoDao) {
		this.toDoDao = toDoDao;
	}

    @Autowired
    public void setQuestionCopyDao(CommonDao<QuestionCopy> questionCopyDao) {
		this.questionCopyDao = questionCopyDao;
	}

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setMarkRecordDao(CommonDao<MarkRecord> markRecordDao) {
        this.markRecordDao = markRecordDao;
    }

    @Autowired
    public void setMarkConfigService(MarkConfigService markConfigService) {
        this.markConfigService = markConfigService;
    }

    @Autowired
    public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
        this.answerRecordDao = answerRecordDao;
    }


    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}

    @Autowired
    public void setToDoService(ToDoService toDoService) {
		this.toDoService = toDoService;
	}

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    @Autowired
    public void setMarkRecordService(MarkRecordService markRecordService) {
        this.markRecordService = markRecordService;
    }

    /**
     * 保存评卷数据  暂时不需要管理markconfig 因为后台管理员评卷 无需配置评卷人，所以markconfig无信息
     */
    @Override
    public String insertMarkPaperInfo(Integer examRegion, String memberId, String examRecordId,
                                      String examId, List<AnswerRecord> answerRecords, Optional<Integer> adminMark, String plainName) {

    	adminMark.orElseGet(() -> {
    		if (markConfigService.invigilatorBeDeleted(examId, memberId)) {
    			throw new UnprocessableException(ErrorCode.SubmitMarkNoPermitError);
    		}

    		if (toDoService.toDobeDeleted(examRecordId, memberId)) {
                throw new UnprocessableException(ErrorCode.MarkPaperError);
            }

    		if (toDoService.hadMarked(examRecordId, memberId)) {
    			throw new UnprocessableException(ErrorCode.HadMarkedError);
    		}
    		return null;
    	});

        //评卷配置
    	List<MarkConfig> markConfigs = markConfigService.find(examId, memberId);

    	List<AnswerRecord> updateAnswerRecords = new ArrayList<>();

    	List<MarkRecord> insertMarkRecords = new ArrayList<>();

    	List<AnswerRecord> hadMarkedRecords = new ArrayList<>();
        answerRecords.forEach(t -> {
        	//检查分数是否已评
            answerRecordService.findByExamRecordIdAndQuestionId(examId, examRecordId, t.getQuestionId()).map(a -> {
            	AnswerRecord answerRecord = null;
            	MarkRecord markRecord = null;
        		answerRecord = a;
        		if (answerRecord.getScore() == null) {
        			answerRecord.setScore(t.getScore());
        			answerRecord.setIsRight(t.getScore() == AnswerRecord.ZERO_SCORE ? AnswerRecord.WRONG : AnswerRecord.RIGHT);
        			updateAnswerRecords.add(answerRecord);
        			// 创建评卷记录
        			markRecord = new MarkRecord();
        			markRecord.forInsert();
        			markRecord.setAnswerRecordId(answerRecord.getId());
        			markRecord.setMemberId(memberId);
        			markRecord.setMarkConfigId(getMarkConfigId(markConfigs, t));
        			insertMarkRecords.add(markRecord);
        		} else {
        			//已被评的题目数
        			hadMarkedRecords.add(answerRecord);
        		}
        		return t;
            });
        });

        //删除同一考生其他老师的 待办任务
        deleteOtherTodoMake(examRecordId, memberId, examId);

        insert(insertMarkRecords);

        if (!CollectionUtils.isEmpty(updateAnswerRecords)){
            String member = examRecordService.getMemberId(examRecordId, examId);
            messageSender.send(MessageTypeContent.EXAM_STU_ANSWER_RECORD_UPDATE,
                    MessageHeaderContent.MEMBER_ID, member,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.BUSINESS_ID, examRecordId,
                    MessageHeaderContent.PARAMS, JSON.toJSONString(updateAnswerRecords));
        }

//
//        //  判断主观题是否评分完毕
//        if (answerRecordService.markPaperCompleted(examRecordId, examId)) {
//        	examRecordService.confirmExamRecord(examRegion, examRecordId, plainName, examId);
//        }

        toDoService.updateSubmitTime(examRecordId, memberId);
        messageSender.send(MessageTypeContent.EXAM_MARK_PAPER_INSERT, MessageHeaderContent.ID, examRecordId,
                MessageHeaderContent.EXAM_ID,examId);

        // 已被别的老师评完了对应试题
        if (hadMarkedRecords.size() == answerRecords.size()) {
        	return "0";
        }
        return "1";
    }

    /**
     * 保存复核数据  暂时不需要管理markconfig 因为后台管理员复核 无需配置复核人，所以markconfig无信息
     */
    @Override
    public String insertAuditPaperInfo(Integer examRegion, String memberId, String examRecordId,
                                       String examId, List<AnswerRecord> answerRecords, Optional<Integer> adminMark, String plainName) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        adminMark.orElseGet(() -> {
            if (markConfigService.invigilatorBeDeleted(examId, memberId)) {
                throw new UnprocessableException(ErrorCode.SubmitMarkNoPermitError);
            }

            if (toDoService.toDobeDeleted(examRecordId, memberId)) {
                throw new UnprocessableException(ErrorCode.MarkPaperError);
            }

            if (toDoService.hadAudited(examRecordId, memberId)) {
                throw new UnprocessableException(ErrorCode.HadMarkedError);
            }
            return null;
        });

        //评卷配置
        List<MarkConfig> markConfigs = markConfigService.find(examId, memberId);

        List<AnswerRecord> updateAnswerRecords = new ArrayList<>();

        List<MarkRecord> insertMarkRecords = new ArrayList<>();

        List<AnswerRecord> hadMarkedRecords = new ArrayList<>();

        answerRecords.forEach(t -> {
            //检查分数是否复核
            answerRecordService.findByExamRecordIdAndQuestionId(examId, examRecordId, t.getQuestionId()).map(a -> {
                AnswerRecord answerRecord = null;
                MarkRecord markRecord = null;
                answerRecord = a;
                if (t.getScore() != null) {
                    answerRecord.setScore(t.getScore());
                    answerRecord.setIsRight(t.getScore() == AnswerRecord.ZERO_SCORE ? AnswerRecord.WRONG : AnswerRecord.RIGHT);
                    updateAnswerRecords.add(answerRecord);
                    // 创建评卷记录
                    markRecord = new MarkRecord();
                    markRecord.forInsert();
                    markRecord.setAnswerRecordId(answerRecord.getId());
                    markRecord.setMemberId(memberId);
                    markRecord.setMarkConfigId(getMarkConfigId(markConfigs, t));
                    insertMarkRecords.add(markRecord);
                }
                return t;
            });
        });

        insert(insertMarkRecords);

        //删除同一考生其他老师的 待办任务
        deleteOtherTodoAudit(examRecordId, memberId, examId);

        if (!CollectionUtils.isEmpty(updateAnswerRecords)){
            String member = examRecordService.getMemberId(examRecordId, examId);
            messageSender.send(MessageTypeContent.EXAM_STU_ANSWER_RECORD_UPDATE,
                    MessageHeaderContent.MEMBER_ID, member,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.BUSINESS_ID, examRecordId,
                    MessageHeaderContent.PARAMS, JSON.toJSONString(updateAnswerRecords));
        }

//        //  判断主观题是否评分完毕
//        if (answerRecordService.markPaperCompleted(examRecordId, examId)) {
//            examRecordService.confirmExamRecord(examRegion, examRecordId, plainName, examId);
//        }
        messageSender.send(MessageTypeContent.EXAM_STU_TODO_UPDATE_AUDITED,
                           MessageHeaderContent.MEMBER_ID, memberId,
                           MessageHeaderContent.BUSINESS_ID, examRecordId);
//        toDoService.updateAuditTime(examRecordId, memberId);

        messageSender.send(MessageTypeContent.EXAM_MARK_PAPER_INSERT,
                MessageHeaderContent.ID, examRecordId,
                MessageHeaderContent.EXAM_ID,examId);

        return "1";
    }

    @Override
    public void insert(List<MarkRecord> markRecord) {
        markRecordDao.insert(markRecord);
    }


    private void deleteOtherTodoMake(String examRecordId, String memberId, String examId) {
        List<ToDo> toDos  = findSameQuestionsTodo(examRecordId, memberId, examId);
        if (!CollectionUtils.isEmpty(toDos)){

            Map<Integer, List<ToDo>> todoMap = toDos.stream().collect(Collectors.groupingBy(ToDo::getIncludeType));
            List<ToDo> deleteToDos = todoMap.get(ToDo.INCLUDE_TYPE_0);

            if (!CollectionUtils.isEmpty(deleteToDos)){
               messageSender.send(MessageTypeContent.EXAM_STU_TODO_DELETE,
                                  MessageHeaderContent.PARAMS,JSON.toJSONString(deleteToDos));
            }

            List<ToDo> updateToDos = todoMap.get(ToDo.INCLUDE_TYPE_1);

            if (!CollectionUtils.isEmpty(updateToDos)){
               messageSender.send(MessageTypeContent.EXAM_STU_TODO_UPDATE,
                                  MessageHeaderContent.PARAMS,JSON.toJSONString(updateToDos));
            }
        }
    }
    private void deleteOtherTodoAudit(String examRecordId, String memberId, String examId) {
        List<ToDo> toDos  = findSameQuestionsTodo(examRecordId, memberId, examId);

        if (!CollectionUtils.isEmpty(toDos)){

            List<ToDo> updateToDos = toDos.stream().filter(t -> t.getIncludeType() == ToDo.INCLUDE_TYPE_1 ||
                    t.getIncludeType() == ToDo.INCLUDE_TYPE_2)
                 .peek(t -> t.setAudited(1)).collect(Collectors.toList());


            if (!CollectionUtils.isEmpty(updateToDos)){
                messageSender.send(MessageTypeContent.EXAM_STU_TODO_UPDATE_ENTITY,
                                   MessageHeaderContent.PARAMS,JSON.toJSONString(updateToDos));
            }
        }
    }

    private List<ToDo> findSameQuestionsTodo(String examRecordId, String memberId, String examId) {
        //找出相同考生的待办
        List<ToDo> toDos = toDoService.findByTargetId(examRecordId);

            Map<String, List<ToDo>> toDoMap =toDos.stream().collect(Collectors.groupingBy(ToDo::getMemberId));

           //找出考试的评卷策略
            List<MarkConfig> markConfigs = markConfigService.findByExamId(examId);

            Map<String, List<MarkConfig>> markConfigMap = markConfigs.stream().collect(Collectors.groupingBy(MarkConfig::getMemberId));

            //找出当前评卷的老师的被选的策略
            List<MarkConfig> targetMarkConfigs = markConfigMap.get(memberId);

            // 如果为空，说明是管理员在后台进行评卷,返回这个考生的所有代办
            if (targetMarkConfigs == null) {
                return toDos;
            }

            Map<String, MarkConfig> targetMap = targetMarkConfigs.stream().collect(Collectors.toMap(MarkConfig::getTypeId, e -> e, (e1,e2) -> e2));

            //找出相同策略的老师
            List<String> memberIds  = markConfigMap.keySet().stream().filter(m -> {
                //排除自己
                if (!m.equals(memberId)) {
                    List<MarkConfig> beCompared = markConfigMap.get(m);
                    //其他老师的策略和当前老师的策略一直
                        return beCompared.stream().allMatch(b -> {
                            return targetMap.get(b.getTypeId()) != null;
                    });
                }
                return false;
            }).collect(Collectors.toList());

           //找出最终相同题目的待办
           List<ToDo> finalTargets = memberIds.stream().map(member -> {

                return toDoMap.get(member);
            }).flatMap(l -> l.stream()).collect(Collectors.toList());;

        return finalTargets;
    }

	/**
       * 根据答题记录 获取 对应的markconfig
       */
    private String getMarkConfigId(List<MarkConfig> markConfigs, AnswerRecord answerRecord) {
    	Integer type = markConfigs.get(0).getType();
        switch (type) {
        case 1:
            return markConfigs.get(0).getId();
        case 2:
        	// 如果是阅读题题型 typeId 直接等于 问答题
        	markConfigs =  markConfigs.stream().filter(t -> {
            	String typeId = "";
            	if (Integer.valueOf(t.getTypeId()) == Question.READING_COMPREHENSION) {
            		typeId = "5";
            	} else {
            		typeId = t.getTypeId();
            	}
            	return typeId.equals(answerRecord.getQuestionCopy().getType().toString());
            }).collect(Collectors.toList());
        	return markConfigs.size() > 0 ? markConfigs.get(0).getId() : "";
        case 3:
        	QuestionCopy questionCopy = questionCopyService.getById(answerRecord.getQuestionId());
            markConfigs =  markConfigs.stream().filter(t -> {
            	String questionId = "";
            	if (questionCopy.getParentId() != null) {
            		questionId = questionCopyService.getById(questionCopy.getParentId()).getQuestionId();
            	} else {
            		questionId = questionCopyService.getById(answerRecord.getQuestionId()).getQuestionId();
            	}
            	return t.getTypeId().equals(questionId);
            }).collect(Collectors.toList());
            return markConfigs.size() > 0 ? markConfigs.get(0).getId() : "";
        default:
            break;
        }
        return "";
    }






}
