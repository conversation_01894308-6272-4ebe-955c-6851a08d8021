package com.zxy.product.exam.config;

import com.zxy.product.exam.jooq.Exam;
import org.jooq.impl.DefaultConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Created by ouyang on 2017/11/25.
 */
@Configuration
public class ExamEnvConfig {

    @Bean
    public Exam examSchema() {
        return Exam.EXAM_SCHEMA;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public org.jooq.Configuration jooqConfiguration() {
        org.jooq.Configuration configuration = new DefaultConfiguration();

        configuration.set(dataSource());
//        Settings;
        return configuration;
    }
}
