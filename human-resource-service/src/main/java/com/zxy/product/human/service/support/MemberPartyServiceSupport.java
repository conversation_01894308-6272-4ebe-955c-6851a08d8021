package com.zxy.product.human.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.human.api.MemberPartyService;
import com.zxy.product.human.entity.MemberParty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.human.jooq.Tables.MEMBER_PARTY;

/**
 * <AUTHOR>
 * @date 2020/8/16
 */
@Service
public class MemberPartyServiceSupport implements MemberPartyService {

    private CommonDao<MemberParty> dao;

    @Autowired
    public void setDao(CommonDao<MemberParty> dao) {
        this.dao = dao;
    }

    @Override
    public Map<String, List<String>> batchSync(List<MemberParty> list) {

        boolean status = true;
        try {
            Set<String> ids = list.stream().map(MemberParty::getId).collect(Collectors.toSet());

            Set<String> existsIds = dao.execute(e->e.select(MEMBER_PARTY.ID).from(MEMBER_PARTY)
                    .where(MEMBER_PARTY.ID.in(ids))).fetchSet(MEMBER_PARTY.ID);

            List<MemberParty> updateList = list.stream().filter(x->existsIds.contains(x.getId())).collect(Collectors.toList());
            List<MemberParty> insertList = list.stream().filter(x->!existsIds.contains(x.getId())).collect(Collectors.toList());

            dao.insert(insertList);
            dao.update(updateList);
        } catch (Exception e) {
            e.printStackTrace();
            status = false;
        }

        Map<String, List<String>> resultMap = new HashMap<>();
        List<String> itemIds = list.stream().map(MemberParty::getItemId).collect(Collectors.toList());
        resultMap.put("success_ids", status ? itemIds : new ArrayList<>());
        resultMap.put("failure_ids", status ? new ArrayList<>() : itemIds);
        return resultMap;
    }
}
