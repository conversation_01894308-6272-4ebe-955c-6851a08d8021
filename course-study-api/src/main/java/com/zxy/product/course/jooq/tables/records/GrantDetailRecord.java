/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.GrantDetail;
import com.zxy.product.course.jooq.tables.interfaces.IGrantDetail;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GrantDetailRecord extends UpdatableRecordImpl<GrantDetailRecord> implements Record8<String, String, String, String, Long, String, String, String>, IGrantDetail {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_grant_detail.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_grant_id</code>. 授权ID
     */
    @Override
    public void setGrantId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_grant_id</code>. 授权ID
     */
    @Override
    public String getGrantId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_member_id</code>. 人员ID
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_member_id</code>. 人员ID
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_uri</code>. 菜单uri
     */
    @Override
    public void setUri(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_uri</code>. 菜单uri
     */
    @Override
    public String getUri() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_menu_id</code>. 菜单id
     */
    @Override
    public void setMenuId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_menu_id</code>. 菜单id
     */
    @Override
    public String getMenuId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_grant_detail.f_operator_types</code>. 操作类型
     */
    @Override
    public void setOperatorTypes(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_grant_detail.f_operator_types</code>. 操作类型
     */
    @Override
    public String getOperatorTypes() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, Long, String, String, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, Long, String, String, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return GrantDetail.GRANT_DETAIL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return GrantDetail.GRANT_DETAIL.GRANT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return GrantDetail.GRANT_DETAIL.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return GrantDetail.GRANT_DETAIL.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return GrantDetail.GRANT_DETAIL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return GrantDetail.GRANT_DETAIL.URI;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return GrantDetail.GRANT_DETAIL.MENU_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return GrantDetail.GRANT_DETAIL.OPERATOR_TYPES;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getGrantId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getUri();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getMenuId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getOperatorTypes();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value2(String value) {
        setGrantId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value4(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value6(String value) {
        setUri(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value7(String value) {
        setMenuId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord value8(String value) {
        setOperatorTypes(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetailRecord values(String value1, String value2, String value3, String value4, Long value5, String value6, String value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGrantDetail from) {
        setId(from.getId());
        setGrantId(from.getGrantId());
        setMemberId(from.getMemberId());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setUri(from.getUri());
        setMenuId(from.getMenuId());
        setOperatorTypes(from.getOperatorTypes());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGrantDetail> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached GrantDetailRecord
     */
    public GrantDetailRecord() {
        super(GrantDetail.GRANT_DETAIL);
    }

    /**
     * Create a detached, initialised GrantDetailRecord
     */
    public GrantDetailRecord(String id, String grantId, String memberId, String organizationId, Long createTime, String uri, String menuId, String operatorTypes) {
        super(GrantDetail.GRANT_DETAIL);

        set(0, id);
        set(1, grantId);
        set(2, memberId);
        set(3, organizationId);
        set(4, createTime);
        set(5, uri);
        set(6, menuId);
        set(7, operatorTypes);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.GrantDetailEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.GrantDetailEntity pojo = (com.zxy.product.course.jooq.tables.pojos.GrantDetailEntity)source;
        pojo.into(this);
        return true;
    }
}
