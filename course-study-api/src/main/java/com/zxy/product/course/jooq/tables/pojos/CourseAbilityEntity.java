/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseAbility;

import javax.annotation.Generated;


/**
 * 学习地图章关联能力表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseAbilityEntity extends BaseEntity implements ICourseAbility {

    private static final long serialVersionUID = 1L;

    private String  name;
    private Integer order;
    private Integer    learnSequence;
    private String  abilityId;
    private String  courseId;

    public CourseAbilityEntity() {}

    public CourseAbilityEntity(CourseAbilityEntity value) {
        this.name = value.name;
        this.order = value.order;
        this.learnSequence = value.learnSequence;
        this.abilityId = value.abilityId;
        this.courseId = value.courseId;
    }

    public CourseAbilityEntity(
        String  id,
        Long    createTime,
        String  name,
        Integer order,
        Integer    learnSequence,
        String  abilityId,
        String  courseId
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.name = name;
        this.order = order;
        this.learnSequence = learnSequence;
        this.abilityId = abilityId;
        this.courseId = courseId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Integer getOrder() {
        return this.order;
    }

    @Override
    public void setOrder(Integer order) {
        this.order = order;
    }

    @Override
    public Integer getLearnSequence() {
        return this.learnSequence;
    }

    @Override
    public void setLearnSequence(Integer learnSequence) {
        this.learnSequence = learnSequence;
    }

    @Override
    public String getAbilityId() {
        return this.abilityId;
    }

    @Override
    public void setAbilityId(String abilityId) {
        this.abilityId = abilityId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseAbilityEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(name);
        sb.append(", ").append(order);
        sb.append(", ").append(learnSequence);
        sb.append(", ").append(abilityId);
        sb.append(", ").append(courseId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseAbility from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setOrder(from.getOrder());
        setLearnSequence(from.getLearnSequence());
        setAbilityId(from.getAbilityId());
        setCourseId(from.getCourseId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseAbility> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseAbilityEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseAbilityRecord r = new com.zxy.product.course.jooq.tables.records.CourseAbilityRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.NAME, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ORDER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ORDER, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ORDER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.LEARN_SEQUENCE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.LEARN_SEQUENCE, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.LEARN_SEQUENCE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ABILITY_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ABILITY_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.ABILITY_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY.COURSE_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
