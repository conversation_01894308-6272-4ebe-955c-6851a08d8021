package com.zxy.product.course.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 课程分类统计
 * <AUTHOR> zhouyong
 */
public class CourseCategoryStatistics implements Serializable {

    private static final long serialVersionUID = 2740481416953675230L;

    private String name;
    private Integer ratio;
    private Integer count;

    public CourseCategoryStatistics() {
    }

    public CourseCategoryStatistics(String name, Integer count) {
        this.name = name;
        this.count = count;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
