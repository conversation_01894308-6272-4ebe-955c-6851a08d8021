/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.RepeatCourseSectionStudyProgressRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 重复专题学习进度记录备份
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RepeatCourseSectionStudyProgress extends TableImpl<RepeatCourseSectionStudyProgressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_repeat_course_section_study_progress</code>
     */
    public static final RepeatCourseSectionStudyProgress REPEAT_COURSE_SECTION_STUDY_PROGRESS = new RepeatCourseSectionStudyProgress();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RepeatCourseSectionStudyProgressRecord> getRecordType() {
        return RepeatCourseSectionStudyProgressRecord.class;
    }

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_id</code>.
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_member_id</code>. 用户ID
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_course_id</code>. 课程ID
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程ID");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_section_id</code>. 课程节ID
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程节ID");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_section_type</code>. 学习开始时间
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, Long> SECTION_TYPE = createField("f_section_type", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "学习开始时间");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, Integer> STUDY_TOTAL_TIME = createField("f_study_total_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习累计时长，单位秒");

    /**
     * The column <code>course-study.t_repeat_course_section_study_progress.f_create_time</code>.
     */
    public final TableField<RepeatCourseSectionStudyProgressRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>course-study.t_repeat_course_section_study_progress</code> table reference
     */
    public RepeatCourseSectionStudyProgress() {
        this("t_repeat_course_section_study_progress", null);
    }

    /**
     * Create an aliased <code>course-study.t_repeat_course_section_study_progress</code> table reference
     */
    public RepeatCourseSectionStudyProgress(String alias) {
        this(alias, REPEAT_COURSE_SECTION_STUDY_PROGRESS);
    }

    private RepeatCourseSectionStudyProgress(String alias, Table<RepeatCourseSectionStudyProgressRecord> aliased) {
        this(alias, aliased, null);
    }

    private RepeatCourseSectionStudyProgress(String alias, Table<RepeatCourseSectionStudyProgressRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "重复专题学习进度记录备份");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RepeatCourseSectionStudyProgressRecord> getPrimaryKey() {
        return Keys.KEY_T_REPEAT_COURSE_SECTION_STUDY_PROGRESS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RepeatCourseSectionStudyProgressRecord>> getKeys() {
        return Arrays.<UniqueKey<RepeatCourseSectionStudyProgressRecord>>asList(Keys.KEY_T_REPEAT_COURSE_SECTION_STUDY_PROGRESS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RepeatCourseSectionStudyProgress as(String alias) {
        return new RepeatCourseSectionStudyProgress(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RepeatCourseSectionStudyProgress rename(String name) {
        return new RepeatCourseSectionStudyProgress(name, null);
    }
}
