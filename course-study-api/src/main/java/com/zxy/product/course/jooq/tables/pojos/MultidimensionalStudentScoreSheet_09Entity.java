/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IMultidimensionalStudentScoreSheet_09;

import javax.annotation.Generated;


/**
 * 学员评分表9
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MultidimensionalStudentScoreSheet_09Entity extends BaseEntity implements IMultidimensionalStudentScoreSheet_09 {

    private static final long serialVersionUID = 1L;

    private String  courseId;
    private String  scoringSubjectId;
    private Integer firstLatitudeScore;
    private Integer secondLatitudeScore;
    private Integer thirdLatitudeScore;
    private Integer fourthLatitudeScore;
    private Integer fifthLatitudeScore;
    private String  content;
    private String  memberId;

    public MultidimensionalStudentScoreSheet_09Entity() {}

    public MultidimensionalStudentScoreSheet_09Entity(MultidimensionalStudentScoreSheet_09Entity value) {
        this.courseId = value.courseId;
        this.scoringSubjectId = value.scoringSubjectId;
        this.firstLatitudeScore = value.firstLatitudeScore;
        this.secondLatitudeScore = value.secondLatitudeScore;
        this.thirdLatitudeScore = value.thirdLatitudeScore;
        this.fourthLatitudeScore = value.fourthLatitudeScore;
        this.fifthLatitudeScore = value.fifthLatitudeScore;
        this.content = value.content;
        this.memberId = value.memberId;
    }

    public MultidimensionalStudentScoreSheet_09Entity(
        String  id,
        String  courseId,
        String  scoringSubjectId,
        Integer firstLatitudeScore,
        Integer secondLatitudeScore,
        Integer thirdLatitudeScore,
        Integer fourthLatitudeScore,
        Integer fifthLatitudeScore,
        String  content,
        String  memberId,
        Long    createTime
    ) {
        super.setId(id);
        this.courseId = courseId;
        this.scoringSubjectId = scoringSubjectId;
        this.firstLatitudeScore = firstLatitudeScore;
        this.secondLatitudeScore = secondLatitudeScore;
        this.thirdLatitudeScore = thirdLatitudeScore;
        this.fourthLatitudeScore = fourthLatitudeScore;
        this.fifthLatitudeScore = fifthLatitudeScore;
        this.content = content;
        this.memberId = memberId;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String getScoringSubjectId() {
        return this.scoringSubjectId;
    }

    @Override
    public void setScoringSubjectId(String scoringSubjectId) {
        this.scoringSubjectId = scoringSubjectId;
    }

    @Override
    public Integer getFirstLatitudeScore() {
        return this.firstLatitudeScore;
    }

    @Override
    public void setFirstLatitudeScore(Integer firstLatitudeScore) {
        this.firstLatitudeScore = firstLatitudeScore;
    }

    @Override
    public Integer getSecondLatitudeScore() {
        return this.secondLatitudeScore;
    }

    @Override
    public void setSecondLatitudeScore(Integer secondLatitudeScore) {
        this.secondLatitudeScore = secondLatitudeScore;
    }

    @Override
    public Integer getThirdLatitudeScore() {
        return this.thirdLatitudeScore;
    }

    @Override
    public void setThirdLatitudeScore(Integer thirdLatitudeScore) {
        this.thirdLatitudeScore = thirdLatitudeScore;
    }

    @Override
    public Integer getFourthLatitudeScore() {
        return this.fourthLatitudeScore;
    }

    @Override
    public void setFourthLatitudeScore(Integer fourthLatitudeScore) {
        this.fourthLatitudeScore = fourthLatitudeScore;
    }

    @Override
    public Integer getFifthLatitudeScore() {
        return this.fifthLatitudeScore;
    }

    @Override
    public void setFifthLatitudeScore(Integer fifthLatitudeScore) {
        this.fifthLatitudeScore = fifthLatitudeScore;
    }

    @Override
    public String getContent() {
        return this.content;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MultidimensionalStudentScoreSheet_09Entity (");

        sb.append(getId());
        sb.append(", ").append(courseId);
        sb.append(", ").append(scoringSubjectId);
        sb.append(", ").append(firstLatitudeScore);
        sb.append(", ").append(secondLatitudeScore);
        sb.append(", ").append(thirdLatitudeScore);
        sb.append(", ").append(fourthLatitudeScore);
        sb.append(", ").append(fifthLatitudeScore);
        sb.append(", ").append(content);
        sb.append(", ").append(memberId);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMultidimensionalStudentScoreSheet_09 from) {
        setId(from.getId());
        setCourseId(from.getCourseId());
        setScoringSubjectId(from.getScoringSubjectId());
        setFirstLatitudeScore(from.getFirstLatitudeScore());
        setSecondLatitudeScore(from.getSecondLatitudeScore());
        setThirdLatitudeScore(from.getThirdLatitudeScore());
        setFourthLatitudeScore(from.getFourthLatitudeScore());
        setFifthLatitudeScore(from.getFifthLatitudeScore());
        setContent(from.getContent());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMultidimensionalStudentScoreSheet_09> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends MultidimensionalStudentScoreSheet_09Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.MultidimensionalStudentScoreSheet_09Record r = new com.zxy.product.course.jooq.tables.records.MultidimensionalStudentScoreSheet_09Record();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.ID, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SCORING_SUBJECT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SCORING_SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SCORING_SUBJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIRST_LATITUDE_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIRST_LATITUDE_SCORE, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIRST_LATITUDE_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SECOND_LATITUDE_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SECOND_LATITUDE_SCORE, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.SECOND_LATITUDE_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.THIRD_LATITUDE_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.THIRD_LATITUDE_SCORE, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.THIRD_LATITUDE_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FOURTH_LATITUDE_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FOURTH_LATITUDE_SCORE, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FOURTH_LATITUDE_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIFTH_LATITUDE_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIFTH_LATITUDE_SCORE, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.FIFTH_LATITUDE_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CONTENT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CONTENT, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CONTENT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
