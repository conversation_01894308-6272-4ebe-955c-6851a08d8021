/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IMemberParty;

import javax.annotation.Generated;


/**
 * 党员信息（原样同步党建云数据）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberPartyEntity extends BaseEntity implements IMemberParty {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  ihrCode;
    private String  userName;
    private String  code;
    private String  displayName;
    private String  idcardNumber;
    private Long    workHour;
    private String  workOrganization;
    private String  orgId;
    private String  orgName;
    private String  userType;
    private Long    partyDate;
    private String  partyMemberState;
    private Long    applyPartyDate;
    private Long    toActivistDate;
    private Long    toObjectDate;
    private Long    toMemberDate;
    private String  partyState;
    private Integer isFloating;
    private String  influxPlace;
    private String  postId;
    private String  userId;

    public MemberPartyEntity() {}

    public MemberPartyEntity(MemberPartyEntity value) {
        this.memberId = value.memberId;
        this.ihrCode = value.ihrCode;
        this.userName = value.userName;
        this.code = value.code;
        this.displayName = value.displayName;
        this.idcardNumber = value.idcardNumber;
        this.workHour = value.workHour;
        this.workOrganization = value.workOrganization;
        this.orgId = value.orgId;
        this.orgName = value.orgName;
        this.userType = value.userType;
        this.partyDate = value.partyDate;
        this.partyMemberState = value.partyMemberState;
        this.applyPartyDate = value.applyPartyDate;
        this.toActivistDate = value.toActivistDate;
        this.toObjectDate = value.toObjectDate;
        this.toMemberDate = value.toMemberDate;
        this.partyState = value.partyState;
        this.isFloating = value.isFloating;
        this.influxPlace = value.influxPlace;
        this.postId = value.postId;
        this.userId = value.userId;
    }

    public MemberPartyEntity(
        String  id,
        String  memberId,
        String  ihrCode,
        String  userName,
        String  code,
        String  displayName,
        String  idcardNumber,
        Long    workHour,
        String  workOrganization,
        String  orgId,
        String  orgName,
        String  userType,
        Long    partyDate,
        String  partyMemberState,
        Long    applyPartyDate,
        Long    toActivistDate,
        Long    toObjectDate,
        Long    toMemberDate,
        String  partyState,
        Integer isFloating,
        String  influxPlace,
        String  postId,
        String  userId
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.ihrCode = ihrCode;
        this.userName = userName;
        this.code = code;
        this.displayName = displayName;
        this.idcardNumber = idcardNumber;
        this.workHour = workHour;
        this.workOrganization = workOrganization;
        this.orgId = orgId;
        this.orgName = orgName;
        this.userType = userType;
        this.partyDate = partyDate;
        this.partyMemberState = partyMemberState;
        this.applyPartyDate = applyPartyDate;
        this.toActivistDate = toActivistDate;
        this.toObjectDate = toObjectDate;
        this.toMemberDate = toMemberDate;
        this.partyState = partyState;
        this.isFloating = isFloating;
        this.influxPlace = influxPlace;
        this.postId = postId;
        this.userId = userId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getIhrCode() {
        return this.ihrCode;
    }

    @Override
    public void setIhrCode(String ihrCode) {
        this.ihrCode = ihrCode;
    }

    @Override
    public String getUserName() {
        return this.userName;
    }

    @Override
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    @Override
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String getIdcardNumber() {
        return this.idcardNumber;
    }

    @Override
    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    @Override
    public Long getWorkHour() {
        return this.workHour;
    }

    @Override
    public void setWorkHour(Long workHour) {
        this.workHour = workHour;
    }

    @Override
    public String getWorkOrganization() {
        return this.workOrganization;
    }

    @Override
    public void setWorkOrganization(String workOrganization) {
        this.workOrganization = workOrganization;
    }

    @Override
    public String getOrgId() {
        return this.orgId;
    }

    @Override
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public String getOrgName() {
        return this.orgName;
    }

    @Override
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    public String getUserType() {
        return this.userType;
    }

    @Override
    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Override
    public Long getPartyDate() {
        return this.partyDate;
    }

    @Override
    public void setPartyDate(Long partyDate) {
        this.partyDate = partyDate;
    }

    @Override
    public String getPartyMemberState() {
        return this.partyMemberState;
    }

    @Override
    public void setPartyMemberState(String partyMemberState) {
        this.partyMemberState = partyMemberState;
    }

    @Override
    public Long getApplyPartyDate() {
        return this.applyPartyDate;
    }

    @Override
    public void setApplyPartyDate(Long applyPartyDate) {
        this.applyPartyDate = applyPartyDate;
    }

    @Override
    public Long getToActivistDate() {
        return this.toActivistDate;
    }

    @Override
    public void setToActivistDate(Long toActivistDate) {
        this.toActivistDate = toActivistDate;
    }

    @Override
    public Long getToObjectDate() {
        return this.toObjectDate;
    }

    @Override
    public void setToObjectDate(Long toObjectDate) {
        this.toObjectDate = toObjectDate;
    }

    @Override
    public Long getToMemberDate() {
        return this.toMemberDate;
    }

    @Override
    public void setToMemberDate(Long toMemberDate) {
        this.toMemberDate = toMemberDate;
    }

    @Override
    public String getPartyState() {
        return this.partyState;
    }

    @Override
    public void setPartyState(String partyState) {
        this.partyState = partyState;
    }

    @Override
    public Integer getIsFloating() {
        return this.isFloating;
    }

    @Override
    public void setIsFloating(Integer isFloating) {
        this.isFloating = isFloating;
    }

    @Override
    public String getInfluxPlace() {
        return this.influxPlace;
    }

    @Override
    public void setInfluxPlace(String influxPlace) {
        this.influxPlace = influxPlace;
    }

    @Override
    public String getPostId() {
        return this.postId;
    }

    @Override
    public void setPostId(String postId) {
        this.postId = postId;
    }

    @Override
    public String getUserId() {
        return this.userId;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MemberPartyEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(ihrCode);
        sb.append(", ").append(userName);
        sb.append(", ").append(code);
        sb.append(", ").append(displayName);
        sb.append(", ").append(idcardNumber);
        sb.append(", ").append(workHour);
        sb.append(", ").append(workOrganization);
        sb.append(", ").append(orgId);
        sb.append(", ").append(orgName);
        sb.append(", ").append(userType);
        sb.append(", ").append(partyDate);
        sb.append(", ").append(partyMemberState);
        sb.append(", ").append(applyPartyDate);
        sb.append(", ").append(toActivistDate);
        sb.append(", ").append(toObjectDate);
        sb.append(", ").append(toMemberDate);
        sb.append(", ").append(partyState);
        sb.append(", ").append(isFloating);
        sb.append(", ").append(influxPlace);
        sb.append(", ").append(postId);
        sb.append(", ").append(userId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMemberParty from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setIhrCode(from.getIhrCode());
        setUserName(from.getUserName());
        setCode(from.getCode());
        setDisplayName(from.getDisplayName());
        setIdcardNumber(from.getIdcardNumber());
        setWorkHour(from.getWorkHour());
        setWorkOrganization(from.getWorkOrganization());
        setOrgId(from.getOrgId());
        setOrgName(from.getOrgName());
        setUserType(from.getUserType());
        setPartyDate(from.getPartyDate());
        setPartyMemberState(from.getPartyMemberState());
        setApplyPartyDate(from.getApplyPartyDate());
        setToActivistDate(from.getToActivistDate());
        setToObjectDate(from.getToObjectDate());
        setToMemberDate(from.getToMemberDate());
        setPartyState(from.getPartyState());
        setIsFloating(from.getIsFloating());
        setInfluxPlace(from.getInfluxPlace());
        setPostId(from.getPostId());
        setUserId(from.getUserId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMemberParty> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends MemberPartyEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.MemberPartyRecord r = new com.zxy.product.course.jooq.tables.records.MemberPartyRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ID, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IHR_CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IHR_CODE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IHR_CODE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_NAME, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.CODE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.CODE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.DISPLAY_NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.DISPLAY_NAME, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.DISPLAY_NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IDCARD_NUMBER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IDCARD_NUMBER, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IDCARD_NUMBER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_HOUR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_HOUR, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_HOUR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_ORGANIZATION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_ORGANIZATION, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.WORK_ORGANIZATION));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_ID, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_NAME, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.ORG_NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_TYPE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_DATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_MEMBER_STATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_MEMBER_STATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_MEMBER_STATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.APPLY_PARTY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.APPLY_PARTY_DATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.APPLY_PARTY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_ACTIVIST_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_ACTIVIST_DATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_ACTIVIST_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_OBJECT_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_OBJECT_DATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_OBJECT_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_MEMBER_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_MEMBER_DATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.TO_MEMBER_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_STATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_STATE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.PARTY_STATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IS_FLOATING) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IS_FLOATING, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.IS_FLOATING));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.INFLUX_PLACE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.INFLUX_PLACE, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.INFLUX_PLACE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.POST_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.POST_ID, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.POST_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_ID, record.getValue(com.zxy.product.course.jooq.tables.MemberParty.MEMBER_PARTY.USER_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
