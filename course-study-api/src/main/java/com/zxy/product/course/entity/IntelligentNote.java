package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.IntelligentNoteEntity;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/1
 * @description ：智能笔记
 */
public class IntelligentNote extends IntelligentNoteEntity {
    private static final long serialVersionUID = 7473040566218118492L;

    private Member member;
    private CourseInfo courseInfo;
    private CourseChapterSection courseChapterSection;
    private Organization organization;
    private String organizationName;
    private String createName;
    private String auditMemberName;

    private String praiseId;
    private List<IntelligentNoteBookmark> bookmarks;

    // 审核状态 0未审核 1通过 2拒绝 3无需审核
    public static final Integer AUDIT_STATUS_W = 0;
    public static final Integer AUDIT_STATUS_Y = 1;
    public static final Integer AUDIT_STATUS_N = 2;
    public static final Integer NO_NEED_AUDIT = 3;

    // 公开状态 0-否 1-是
    public static final Integer IS_PUBLIC_N = 0;
    public static final Integer IS_PUBLIC_Y = 1;

    // 置顶状态 0-否 1-是
    public static final Integer TOP_STATUS_N = 0;
    public static final Integer TOP_STATUS_Y = 1;

    // 保存类型 1-自动 2-手动
    public static final Integer AUTOMATIC_SAVE = 1;
    public static final Integer MANUAL_SAVE = 2;

    public String getPraiseId() {
        return praiseId;
    }

    public void setPraiseId(String praiseId) {
        this.praiseId = praiseId;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public CourseChapterSection getCourseChapterSection() {
        return courseChapterSection;
    }

    public void setCourseChapterSection(CourseChapterSection courseChapterSection) {
        this.courseChapterSection = courseChapterSection;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getAuditMemberName() {
        return auditMemberName;
    }

    public void setAuditMemberName(String auditMemberName) {
        this.auditMemberName = auditMemberName;
    }

    public List<IntelligentNoteBookmark> getBookmarks() {
        return bookmarks;
    }

    public void setBookmarks(List<IntelligentNoteBookmark> bookmarks) {
        this.bookmarks = bookmarks;
    }
}
