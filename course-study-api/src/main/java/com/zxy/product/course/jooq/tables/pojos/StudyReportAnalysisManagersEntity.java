/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IStudyReportAnalysisManagers;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 课程/专题-学情分析分析报告管理员 - 关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyReportAnalysisManagersEntity extends BaseEntity implements IStudyReportAnalysisManagers {

    private static final long serialVersionUID = 1L;

    private String    courseId;
    private String    memberId;
    private Integer   viewScope;
    private Timestamp modifyDate;

    public StudyReportAnalysisManagersEntity() {}

    public StudyReportAnalysisManagersEntity(StudyReportAnalysisManagersEntity value) {
        this.courseId = value.courseId;
        this.memberId = value.memberId;
        this.viewScope = value.viewScope;
        this.modifyDate = value.modifyDate;
    }

    public StudyReportAnalysisManagersEntity(
        String    id,
        String    courseId,
        String    memberId,
        Integer   viewScope,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.courseId = courseId;
        this.memberId = memberId;
        this.viewScope = viewScope;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getViewScope() {
        return this.viewScope;
    }

    @Override
    public void setViewScope(Integer viewScope) {
        this.viewScope = viewScope;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("StudyReportAnalysisManagersEntity (");

        sb.append(getId());
        sb.append(", ").append(courseId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(viewScope);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyReportAnalysisManagers from) {
        setId(from.getId());
        setCourseId(from.getCourseId());
        setMemberId(from.getMemberId());
        setViewScope(from.getViewScope());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyReportAnalysisManagers> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends StudyReportAnalysisManagersEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.StudyReportAnalysisManagersRecord r = new com.zxy.product.course.jooq.tables.records.StudyReportAnalysisManagersRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.ID, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.VIEW_SCOPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.VIEW_SCOPE, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.VIEW_SCOPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
