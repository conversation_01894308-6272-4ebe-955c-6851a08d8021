/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IStudyTask;

import javax.annotation.Generated;


/**
 * 专题任务信息
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTaskEntity extends BaseEntity implements IStudyTask {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  description;
    private Integer auditType;

    public StudyTaskEntity() {}

    public StudyTaskEntity(StudyTaskEntity value) {
        this.name = value.name;
        this.description = value.description;
        this.auditType = value.auditType;
    }

    public StudyTaskEntity(
        String  id,
        String  name,
        String  description,
        Integer auditType,
        Long    createTime
    ) {
        super.setId(id);
        this.name = name;
        this.description = description;
        this.auditType = auditType;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getAuditType() {
        return this.auditType;
    }

    @Override
    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("StudyTaskEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(auditType);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTask from) {
        setId(from.getId());
        setName(from.getName());
        setDescription(from.getDescription());
        setAuditType(from.getAuditType());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTask> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends StudyTaskEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.StudyTaskRecord r = new com.zxy.product.course.jooq.tables.records.StudyTaskRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.ID, record.getValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.NAME, record.getValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.DESCRIPTION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.DESCRIPTION, record.getValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.DESCRIPTION));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.AUDIT_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.AUDIT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.AUDIT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.StudyTask.STUDY_TASK.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
