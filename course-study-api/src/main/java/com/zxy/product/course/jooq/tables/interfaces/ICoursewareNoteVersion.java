/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 数智导师-官方笔记版本表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICoursewareNoteVersion extends Serializable {

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_version_note_id</code>. 笔记版本主键
     */
    public void setVersionNoteId(String value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_version_note_id</code>. 笔记版本主键
     */
    public String getVersionNoteId();

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_essential</code>. 要点
     */
    public void setEssential(String value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_essential</code>. 要点
     */
    public String getEssential();

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_content</code>. 内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_content</code>. 内容
     */
    public String getContent();

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_courseware_note_version.f_update_time</code>. 更新时间
     */
    public void setUpdateTime(Timestamp value);

    /**
     * Getter for <code>course-study.t_courseware_note_version.f_update_time</code>. 更新时间
     */
    public Timestamp getUpdateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICoursewareNoteVersion
     */
    public void from(ICoursewareNoteVersion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICoursewareNoteVersion
     */
    public <E extends ICoursewareNoteVersion> E into(E into);
}
