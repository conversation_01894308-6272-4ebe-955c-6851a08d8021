/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseSectionScormProgressRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionScormProgress extends TableImpl<CourseSectionScormProgressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_section_scorm_progress</code>
     */
    public static final CourseSectionScormProgress COURSE_SECTION_SCORM_PROGRESS = new CourseSectionScormProgress();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSectionScormProgressRecord> getRecordType() {
        return CourseSectionScormProgressRecord.class;
    }

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_id</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_course_id</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_scorm_id</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, String> SCORM_ID = createField("f_scorm_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_scorm_item_id</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, String> SCORM_ITEM_ID = createField("f_scorm_item_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_member_id</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_study_time</code>. 学习时长 秒
     */
    public final TableField<CourseSectionScormProgressRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习时长 秒");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_create_time</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_finish_status</code>.
     */
    public final TableField<CourseSectionScormProgressRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_audit_status</code>. 0 未通过 1 通过
     */
    public final TableField<CourseSectionScormProgressRecord, Integer> AUDIT_STATUS = createField("f_audit_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0 未通过 1 通过");

    /**
     * The column <code>course-study.t_course_section_scorm_progress.f_suspend_data</code>. scrom课件传递的
     */
    public final TableField<CourseSectionScormProgressRecord, String> SUSPEND_DATA = createField("f_suspend_data", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "scrom课件传递的");

    /**
     * Create a <code>course-study.t_course_section_scorm_progress</code> table reference
     */
    public CourseSectionScormProgress() {
        this("t_course_section_scorm_progress", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_section_scorm_progress</code> table reference
     */
    public CourseSectionScormProgress(String alias) {
        this(alias, COURSE_SECTION_SCORM_PROGRESS);
    }

    private CourseSectionScormProgress(String alias, Table<CourseSectionScormProgressRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSectionScormProgress(String alias, Table<CourseSectionScormProgressRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSectionScormProgressRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SECTION_SCORM_PROGRESS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSectionScormProgressRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSectionScormProgressRecord>>asList(Keys.KEY_T_COURSE_SECTION_SCORM_PROGRESS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionScormProgress as(String alias) {
        return new CourseSectionScormProgress(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSectionScormProgress rename(String name) {
        return new CourseSectionScormProgress(name, null);
    }
}
