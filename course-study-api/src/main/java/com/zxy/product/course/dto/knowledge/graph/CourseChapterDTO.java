package com.zxy.product.course.dto.knowledge.graph;

import java.io.Serializable;

/**
 * 知识图谱——课程章节DTO
 *
 * <AUTHOR>
 * @date 2023年10月27日 13:44
 */
public class CourseChapterDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**课程Id*/
    private String courseId;

    /**课程章节*/
    private String chapterId;

    /**课程章节课时*/
    private Integer chapterTime;

    public String getCourseId() { return courseId; }

    public void setCourseId(String courseId) { this.courseId = courseId; }

    public String getChapterId() { return chapterId; }

    public void setChapterId(String chapterId) { this.chapterId = chapterId; }

    public Integer getChapterTime() { return chapterTime; }

    public void setChapterTime(Integer chapterTime) { this.chapterTime = chapterTime; }

    @Override
    public String toString() {
        return "CourseChapterDTO{" +
                "courseId='" + courseId + '\'' +
                ", chapterId='" + chapterId + '\'' +
                ", chapterTime=" + chapterTime +
                '}';
    }
}
