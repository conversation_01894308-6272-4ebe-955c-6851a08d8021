package com.zxy.product.course.content;

import java.util.HashMap;
import java.util.Map;

public enum CourseStudyProgressFinishStatusEnum {

    notStart(0,"未开始"),
    studying(1,"学习中"),
    finished(2,"已完成"),
    giveup(3,"已放弃"),
    markSuc<PERSON>(4,"标记完成"),
    defaultStatus(5,"默认");


    private String code;
    private Integer value;

    CourseStudyProgressFinishStatusEnum(int value,String code){
        this.value = value;
        this.code = code;
    }

    public static Map<Integer,String> finishStatusMap = new HashMap<>(18);

    static {
        for (CourseStudyProgressFinishStatusEnum type : values()) {
            finishStatusMap.put(type.getValue(),type.getCode());
        }
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public static Map<Integer, String> getFinishStatusMap() {
        return finishStatusMap;
    }

    public static void setFinishStatusMap(Map<Integer, String> finishStatusMap) {
        CourseStudyProgressFinishStatusEnum.finishStatusMap = finishStatusMap;
    }
}
