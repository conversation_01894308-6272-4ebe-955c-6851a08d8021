/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 数智导师——同步表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAiSynchronous extends Serializable {

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_course_id</code>. 课程Id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_course_id</code>. 课程Id
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_example_sync_state</code>. 同步状态 -1失败 0成功
     */
    public void setExampleSyncState(Integer value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_example_sync_state</code>. 同步状态 -1失败 0成功
     */
    public Integer getExampleSyncState();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_main_note_sync_state</code>. 同步状态 -1失败 0成功
     */
    public void setMainNoteSyncState(Integer value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_main_note_sync_state</code>. 同步状态 -1失败 0成功
     */
    public Integer getMainNoteSyncState();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_warenote_sync_state</code>. 同步状态 -1失败 0成功
     */
    public void setWarenoteSyncState(Integer value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_warenote_sync_state</code>. 同步状态 -1失败 0成功
     */
    public Integer getWarenoteSyncState();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_update_time</code>. 更新时间
     */
    public void setUpdateTime(Timestamp value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_update_time</code>. 更新时间
     */
    public Timestamp getUpdateTime();

    /**
     * Setter for <code>course-study.t_ai_synchronous.f_error_reason</code>. 同步失败原因
     */
    public void setErrorReason(String value);

    /**
     * Getter for <code>course-study.t_ai_synchronous.f_error_reason</code>. 同步失败原因
     */
    public String getErrorReason();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAiSynchronous
     */
    public void from(IAiSynchronous from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAiSynchronous
     */
    public <E extends IAiSynchronous> E into(E into);
}
