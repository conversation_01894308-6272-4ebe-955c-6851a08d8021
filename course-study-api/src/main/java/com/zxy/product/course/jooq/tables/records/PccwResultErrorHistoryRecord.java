/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.PccwResultErrorHistory;
import com.zxy.product.course.jooq.tables.interfaces.IPccwResultErrorHistory;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * PCCW HR接口调用结果
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PccwResultErrorHistoryRecord extends UpdatableRecordImpl<PccwResultErrorHistoryRecord> implements Record13<String, String, String, String, String, String, String, String, Integer, String, Long, Integer, Long>, IPccwResultErrorHistory {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_method</code>. 业务(接口)ID
     */
    @Override
    public void setMethod(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_method</code>. 业务(接口)ID
     */
    @Override
    public String getMethod() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_instance_id</code>. 实例ID
     */
    @Override
    public void setInstanceId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_instance_id</code>. 实例ID
     */
    @Override
    public String getInstanceId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_link_id</code>. 上次处理ID
     */
    @Override
    public void setLinkId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_link_id</code>. 上次处理ID
     */
    @Override
    public String getLinkId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_resp_code</code>. 响应编码
     */
    @Override
    public void setRespCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_resp_code</code>. 响应编码
     */
    @Override
    public String getRespCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_resp_desc</code>. 返回描述
     */
    @Override
    public void setRespDesc(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_resp_desc</code>. 返回描述
     */
    @Override
    public String getRespDesc() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_status_code</code>. 处理结果标识
     */
    @Override
    public void setStatusCode(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_status_code</code>. 处理结果标识
     */
    @Override
    public String getStatusCode() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_err_reason</code>. 处理失败原因
     */
    @Override
    public void setErrReason(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_err_reason</code>. 处理失败原因
     */
    @Override
    public String getErrReason() {
        return (String) get(7);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_total_record</code>. 总条数
     */
    @Override
    public void setTotalRecord(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_total_record</code>. 总条数
     */
    @Override
    public Integer getTotalRecord() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_output_ext</code>. 查询结果扩展
     */
    @Override
    public void setOutputExt(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_output_ext</code>. 查询结果扩展
     */
    @Override
    public String getOutputExt() {
        return (String) get(9);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_status</code>. 1: 成功 0:失败 2:下一次处理
     */
    @Override
    public void setStatus(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_status</code>. 1: 成功 0:失败 2:下一次处理
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>course-study.t_pccw_result_error_history.f_update_time</code>. 更新时间
     */
    @Override
    public void setUpdateTime(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_pccw_result_error_history.f_update_time</code>. 更新时间
     */
    @Override
    public Long getUpdateTime() {
        return (Long) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, String, String, String, String, Integer, String, Long, Integer, Long> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, String, String, String, String, Integer, String, Long, Integer, Long> valuesRow() {
        return (Row13) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.METHOD;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.INSTANCE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.LINK_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.RESP_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.RESP_DESC;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.STATUS_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.ERR_REASON;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.TOTAL_RECORD;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.OUTPUT_EXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field13() {
        return PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.UPDATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMethod();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getInstanceId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getLinkId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getRespCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getRespDesc();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getStatusCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getErrReason();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getTotalRecord();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getOutputExt();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value13() {
        return getUpdateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value2(String value) {
        setMethod(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value3(String value) {
        setInstanceId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value4(String value) {
        setLinkId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value5(String value) {
        setRespCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value6(String value) {
        setRespDesc(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value7(String value) {
        setStatusCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value8(String value) {
        setErrReason(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value9(Integer value) {
        setTotalRecord(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value10(String value) {
        setOutputExt(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value11(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value12(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord value13(Long value) {
        setUpdateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResultErrorHistoryRecord values(String value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, Integer value9, String value10, Long value11, Integer value12, Long value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPccwResultErrorHistory from) {
        setId(from.getId());
        setMethod(from.getMethod());
        setInstanceId(from.getInstanceId());
        setLinkId(from.getLinkId());
        setRespCode(from.getRespCode());
        setRespDesc(from.getRespDesc());
        setStatusCode(from.getStatusCode());
        setErrReason(from.getErrReason());
        setTotalRecord(from.getTotalRecord());
        setOutputExt(from.getOutputExt());
        setCreateTime(from.getCreateTime());
        setStatus(from.getStatus());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPccwResultErrorHistory> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PccwResultErrorHistoryRecord
     */
    public PccwResultErrorHistoryRecord() {
        super(PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY);
    }

    /**
     * Create a detached, initialised PccwResultErrorHistoryRecord
     */
    public PccwResultErrorHistoryRecord(String id, String method, String instanceId, String linkId, String respCode, String respDesc, String statusCode, String errReason, Integer totalRecord, String outputExt, Long createTime, Integer status, Long updateTime) {
        super(PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY);

        set(0, id);
        set(1, method);
        set(2, instanceId);
        set(3, linkId);
        set(4, respCode);
        set(5, respDesc);
        set(6, statusCode);
        set(7, errReason);
        set(8, totalRecord);
        set(9, outputExt);
        set(10, createTime);
        set(11, status);
        set(12, updateTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.PccwResultErrorHistoryEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.PccwResultErrorHistoryEntity pojo = (com.zxy.product.course.jooq.tables.pojos.PccwResultErrorHistoryEntity)source;
        pojo.into(this);
        return true;
    }
}
