package com.zxy.product.course.api.other;

import com.zxy.common.base.annotation.RemoteService;

import java.util.Optional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by keeley on 2017/3/22.
 */
@RemoteService
public interface ScoreService {

    /**
     *  返回用户的评分
     * @param businessId 业务id
     * @param memberId 用户id
     * @return socre or null
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<Integer> userScore(String businessId,String memberId);
}
