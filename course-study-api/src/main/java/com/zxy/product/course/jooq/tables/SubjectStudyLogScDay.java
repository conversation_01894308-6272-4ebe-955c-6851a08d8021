/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubjectStudyLogScDayRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectStudyLogScDay extends TableImpl<SubjectStudyLogScDayRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_subject_study_log_sc_day</code>
     */
    public static final SubjectStudyLogScDay SUBJECT_STUDY_LOG_SC_DAY = new SubjectStudyLogScDay();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubjectStudyLogScDayRecord> getRecordType() {
        return SubjectStudyLogScDayRecord.class;
    }

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_id</code>. ID
     */
    public final TableField<SubjectStudyLogScDayRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_member_id</code>. 用户id
     */
    public final TableField<SubjectStudyLogScDayRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_subject_id</code>. 专题id
     */
    public final TableField<SubjectStudyLogScDayRecord, String> SUBJECT_ID = createField("f_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专题id");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_study_time</code>. 当天学习总时长
     */
    public final TableField<SubjectStudyLogScDayRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "当天学习总时长");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_day</code>. 日
     */
    public final TableField<SubjectStudyLogScDayRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "日");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_month</code>. 月
     */
    public final TableField<SubjectStudyLogScDayRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "月");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_year</code>. 年
     */
    public final TableField<SubjectStudyLogScDayRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "年");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_create_time</code>. 创建时间
     */
    public final TableField<SubjectStudyLogScDayRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_subject_study_log_sc_day.f_modify_date</code>. 修改时间
     */
    public final TableField<SubjectStudyLogScDayRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>course-study.t_subject_study_log_sc_day</code> table reference
     */
    public SubjectStudyLogScDay() {
        this("t_subject_study_log_sc_day", null);
    }

    /**
     * Create an aliased <code>course-study.t_subject_study_log_sc_day</code> table reference
     */
    public SubjectStudyLogScDay(String alias) {
        this(alias, SUBJECT_STUDY_LOG_SC_DAY);
    }

    private SubjectStudyLogScDay(String alias, Table<SubjectStudyLogScDayRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubjectStudyLogScDay(String alias, Table<SubjectStudyLogScDayRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubjectStudyLogScDayRecord> getPrimaryKey() {
        return Keys.KEY_T_SUBJECT_STUDY_LOG_SC_DAY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubjectStudyLogScDayRecord>> getKeys() {
        return Arrays.<UniqueKey<SubjectStudyLogScDayRecord>>asList(Keys.KEY_T_SUBJECT_STUDY_LOG_SC_DAY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogScDay as(String alias) {
        return new SubjectStudyLogScDay(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubjectStudyLogScDay rename(String name) {
        return new SubjectStudyLogScDay(name, null);
    }
}
