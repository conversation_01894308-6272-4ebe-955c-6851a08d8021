/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyProgressFfcls_2024;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 课程节学习进度(2024反复倡廉专题)
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyProgressFfcls_2024Entity extends BaseEntity implements ICourseSectionStudyProgressFfcls_2024 {

    private static final long serialVersionUID = 1L;

    private String    memberId;
    private String    courseId;
    private String    sectionId;
    private Long      beginTime;
    private Integer   finishStatus;
    private Long      finishTime;
    private Integer   completedRate;
    private Integer   studyTotalTime;
    private Long      lastAccessTime;
    private Integer   examStatus;
    private String    lessonLocation;
    private Long      commitTime;
    private String    submitText;
    private String    auditMemberId;
    private Integer   score;
    private String    comments;
    private Integer   auditPass;
    private Integer   visits;
    private Timestamp modifyDate;

    public CourseSectionStudyProgressFfcls_2024Entity() {}

    public CourseSectionStudyProgressFfcls_2024Entity(CourseSectionStudyProgressFfcls_2024Entity value) {
        this.memberId = value.memberId;
        this.courseId = value.courseId;
        this.sectionId = value.sectionId;
        this.beginTime = value.beginTime;
        this.finishStatus = value.finishStatus;
        this.finishTime = value.finishTime;
        this.completedRate = value.completedRate;
        this.studyTotalTime = value.studyTotalTime;
        this.lastAccessTime = value.lastAccessTime;
        this.examStatus = value.examStatus;
        this.lessonLocation = value.lessonLocation;
        this.commitTime = value.commitTime;
        this.submitText = value.submitText;
        this.auditMemberId = value.auditMemberId;
        this.score = value.score;
        this.comments = value.comments;
        this.auditPass = value.auditPass;
        this.visits = value.visits;
        this.modifyDate = value.modifyDate;
    }

    public CourseSectionStudyProgressFfcls_2024Entity(
        String    id,
        String    memberId,
        String    courseId,
        String    sectionId,
        Long      beginTime,
        Integer   finishStatus,
        Long      finishTime,
        Integer   completedRate,
        Integer   studyTotalTime,
        Long      lastAccessTime,
        Integer   examStatus,
        String    lessonLocation,
        Long      createTime,
        Long      commitTime,
        String    submitText,
        String    auditMemberId,
        Integer   score,
        String    comments,
        Integer   auditPass,
        Integer   visits,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.courseId = courseId;
        this.sectionId = sectionId;
        this.beginTime = beginTime;
        this.finishStatus = finishStatus;
        this.finishTime = finishTime;
        this.completedRate = completedRate;
        this.studyTotalTime = studyTotalTime;
        this.lastAccessTime = lastAccessTime;
        this.examStatus = examStatus;
        this.lessonLocation = lessonLocation;
        super.setCreateTime(createTime);
        this.commitTime = commitTime;
        this.submitText = submitText;
        this.auditMemberId = auditMemberId;
        this.score = score;
        this.comments = comments;
        this.auditPass = auditPass;
        this.visits = visits;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String getSectionId() {
        return this.sectionId;
    }

    @Override
    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public Long getBeginTime() {
        return this.beginTime;
    }

    @Override
    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Long getFinishTime() {
        return this.finishTime;
    }

    @Override
    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    @Override
    public Integer getCompletedRate() {
        return this.completedRate;
    }

    @Override
    public void setCompletedRate(Integer completedRate) {
        this.completedRate = completedRate;
    }

    @Override
    public Integer getStudyTotalTime() {
        return this.studyTotalTime;
    }

    @Override
    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    @Override
    public Long getLastAccessTime() {
        return this.lastAccessTime;
    }

    @Override
    public void setLastAccessTime(Long lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }

    @Override
    public Integer getExamStatus() {
        return this.examStatus;
    }

    @Override
    public void setExamStatus(Integer examStatus) {
        this.examStatus = examStatus;
    }

    @Override
    public String getLessonLocation() {
        return this.lessonLocation;
    }

    @Override
    public void setLessonLocation(String lessonLocation) {
        this.lessonLocation = lessonLocation;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getCommitTime() {
        return this.commitTime;
    }

    @Override
    public void setCommitTime(Long commitTime) {
        this.commitTime = commitTime;
    }

    @Override
    public String getSubmitText() {
        return this.submitText;
    }

    @Override
    public void setSubmitText(String submitText) {
        this.submitText = submitText;
    }

    @Override
    public String getAuditMemberId() {
        return this.auditMemberId;
    }

    @Override
    public void setAuditMemberId(String auditMemberId) {
        this.auditMemberId = auditMemberId;
    }

    @Override
    public Integer getScore() {
        return this.score;
    }

    @Override
    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public String getComments() {
        return this.comments;
    }

    @Override
    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public Integer getAuditPass() {
        return this.auditPass;
    }

    @Override
    public void setAuditPass(Integer auditPass) {
        this.auditPass = auditPass;
    }

    @Override
    public Integer getVisits() {
        return this.visits;
    }

    @Override
    public void setVisits(Integer visits) {
        this.visits = visits;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseSectionStudyProgressFfcls_2024Entity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(courseId);
        sb.append(", ").append(sectionId);
        sb.append(", ").append(beginTime);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(finishTime);
        sb.append(", ").append(completedRate);
        sb.append(", ").append(studyTotalTime);
        sb.append(", ").append(lastAccessTime);
        sb.append(", ").append(examStatus);
        sb.append(", ").append(lessonLocation);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(commitTime);
        sb.append(", ").append(submitText);
        sb.append(", ").append(auditMemberId);
        sb.append(", ").append(score);
        sb.append(", ").append(comments);
        sb.append(", ").append(auditPass);
        sb.append(", ").append(visits);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyProgressFfcls_2024 from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setSectionId(from.getSectionId());
        setBeginTime(from.getBeginTime());
        setFinishStatus(from.getFinishStatus());
        setFinishTime(from.getFinishTime());
        setCompletedRate(from.getCompletedRate());
        setStudyTotalTime(from.getStudyTotalTime());
        setLastAccessTime(from.getLastAccessTime());
        setExamStatus(from.getExamStatus());
        setLessonLocation(from.getLessonLocation());
        setCreateTime(from.getCreateTime());
        setCommitTime(from.getCommitTime());
        setSubmitText(from.getSubmitText());
        setAuditMemberId(from.getAuditMemberId());
        setScore(from.getScore());
        setComments(from.getComments());
        setAuditPass(from.getAuditPass());
        setVisits(from.getVisits());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyProgressFfcls_2024> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseSectionStudyProgressFfcls_2024Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseSectionStudyProgressFfcls_2024Record r = new com.zxy.product.course.jooq.tables.records.CourseSectionStudyProgressFfcls_2024Record();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SECTION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SECTION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SECTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.BEGIN_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.BEGIN_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.BEGIN_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.FINISH_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMPLETED_RATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMPLETED_RATE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMPLETED_RATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.STUDY_TOTAL_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.STUDY_TOTAL_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.STUDY_TOTAL_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LAST_ACCESS_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LAST_ACCESS_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LAST_ACCESS_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.EXAM_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.EXAM_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.EXAM_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LESSON_LOCATION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LESSON_LOCATION, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.LESSON_LOCATION));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMIT_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMIT_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMIT_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SUBMIT_TEXT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SUBMIT_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SUBMIT_TEXT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SCORE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMENTS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMENTS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.COMMENTS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_PASS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_PASS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.AUDIT_PASS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.VISITS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.VISITS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.VISITS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
