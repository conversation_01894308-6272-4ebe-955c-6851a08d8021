package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubjectStudyLogAhDayEntity;
import org.jooq.Record;
import org.jooq.impl.TableImpl;

public class SubjectStudyLogAhDay extends SubjectStudyLogAhDayEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1032250776080933442L;


	public SubjectStudyLogAhDay fill(TableImpl<?> table, Record r) {
		this.setId(r.get(table.field("f_id"), String.class));
		this.setMemberId(r.get(table.field("f_member_id"), String.class));
		this.setSubjectId(r.get(table.field("f_subject_id"), String.class));
		this.setStudyTime(r.get(table.field("f_study_time"), Integer.class));
		this.setDay(r.get(table.field("f_day"), Integer.class));
		this.setMonth(r.get(table.field("f_month"), Integer.class));
		this.setYear(r.get(table.field("f_year"), Integer.class));
		this.setCreateTime(r.get(table.field("f_create_time"), Long.class));
		return this;
	}

}
