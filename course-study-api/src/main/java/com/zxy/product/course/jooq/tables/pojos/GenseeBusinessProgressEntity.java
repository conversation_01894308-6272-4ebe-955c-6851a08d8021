/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IGenseeBusinessProgress;

import javax.annotation.Generated;


/**
 * 直播业务参与进度
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GenseeBusinessProgressEntity extends BaseEntity implements IGenseeBusinessProgress {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  genseeId;
    private String  genseeBusinessId;
    private Integer finishStatus;
    private Integer score;

    public GenseeBusinessProgressEntity() {}

    public GenseeBusinessProgressEntity(GenseeBusinessProgressEntity value) {
        this.memberId = value.memberId;
        this.genseeId = value.genseeId;
        this.genseeBusinessId = value.genseeBusinessId;
        this.finishStatus = value.finishStatus;
        this.score = value.score;
    }

    public GenseeBusinessProgressEntity(
        String  id,
        String  memberId,
        String  genseeId,
        String  genseeBusinessId,
        Integer finishStatus,
        Integer score,
        Long    createTime
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.genseeId = genseeId;
        this.genseeBusinessId = genseeBusinessId;
        this.finishStatus = finishStatus;
        this.score = score;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getGenseeId() {
        return this.genseeId;
    }

    @Override
    public void setGenseeId(String genseeId) {
        this.genseeId = genseeId;
    }

    @Override
    public String getGenseeBusinessId() {
        return this.genseeBusinessId;
    }

    @Override
    public void setGenseeBusinessId(String genseeBusinessId) {
        this.genseeBusinessId = genseeBusinessId;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Integer getScore() {
        return this.score;
    }

    @Override
    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("GenseeBusinessProgressEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(genseeId);
        sb.append(", ").append(genseeBusinessId);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(score);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGenseeBusinessProgress from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setGenseeId(from.getGenseeId());
        setGenseeBusinessId(from.getGenseeBusinessId());
        setFinishStatus(from.getFinishStatus());
        setScore(from.getScore());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGenseeBusinessProgress> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends GenseeBusinessProgressEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.GenseeBusinessProgressRecord r = new com.zxy.product.course.jooq.tables.records.GenseeBusinessProgressRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.ID, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_ID, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_BUSINESS_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_BUSINESS_ID, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.GENSEE_BUSINESS_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.SCORE, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
