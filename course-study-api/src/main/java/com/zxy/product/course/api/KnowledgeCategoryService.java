package com.zxy.product.course.api;

import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.KnowledgeCategory;

/**
 * Created by <PERSON> on 2017/3/6.
 */
@RemoteService
public interface KnowledgeCategoryService {
    @Transactional
    KnowledgeCategory insert(String organizationId, Optional<String> parentId, String name, String code, Optional<Integer> sequence, Optional<Integer> state);

    @Transactional
    KnowledgeCategory update(String id,String name, String code, String organizationId, Optional<Integer> sequence, Optional<String> parentId, Optional<Integer> state);

    @Transactional
    KnowledgeCategory updateState(String id,int state);

    @Transactional
    int delete(String id);

    @Transactional
    KnowledgeCategory get(String id);

    /**
     * 根据组织id查询该组织下的目录列表
     * @param memberId
     * @param organizationId
     * @param state 
     * @param notIncludeCatelogId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<KnowledgeCategory> findByOrgId(String memberId, String organizationId, Optional<Integer> state, Optional<String> notIncludeCatelogId);

    /**
     * 根据组织id查询该组织下的目录列表-选择器
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<KnowledgeCategory> findChooserByOrgId(String organizationId);
    /**
     * 根据组织id查询该组织下的目录列表[学员端使用]
     * @param memberId
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<KnowledgeCategory> findForFront(String memberId, String organizationId);
    /**
     * 根据目录id查询它的所有子目录id ps:该目录必须没有被禁用
     * @param id
     * @param isIncludeSelf //是否包含自己
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findChildrenIdsById(String id, Boolean isIncludeSelf);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String getPathName(String id);

    /**
     * 查询这个人有管理权限的集团的目录
     * @param memberId
     * @return
     */
//    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
//    List<KnowledgeCategory> find(String memberId);

    /**
     * 前端查询所有目录。
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<KnowledgeCategory> findForFront();

    @Transactional
    void save(List<KnowledgeCategory> categoryList);

    @Transactional
    KnowledgeCategory insert(KnowledgeCategory kc);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<KnowledgeCategory> findForExport(Optional<String> organizationId, Optional<String> id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	List<KnowledgeCategory> findByNoclude(String memberId, String organizationId, Optional<String> notIncludeCatelogId);

	/**
	 * 判断目录名称是否存在
	 * @param organizationId
	 * @param name
	 * @return
	 */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	List<KnowledgeCategory> findByName(String organizationId, String name);

	/**
	 * 判断目录code是否存在
	 * @param organizationId
	 * @param code
	 * @return
	 */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	List<KnowledgeCategory> findByCode(String organizationId, String code);
}
