/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.GenseeUserJoinHistoryRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 直播历史记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GenseeUserJoinHistory extends TableImpl<GenseeUserJoinHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_gensee_user_join_history</code>
     */
    public static final GenseeUserJoinHistory GENSEE_USER_JOIN_HISTORY = new GenseeUserJoinHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GenseeUserJoinHistoryRecord> getRecordType() {
        return GenseeUserJoinHistoryRecord.class;
    }

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_id</code>. ID
     */
    public final TableField<GenseeUserJoinHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_gensee_id</code>. 直播ID
     */
    public final TableField<GenseeUserJoinHistoryRecord, String> GENSEE_ID = createField("f_gensee_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "直播ID");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_user_id</code>. 用户ID
     */
    public final TableField<GenseeUserJoinHistoryRecord, String> USER_ID = createField("f_user_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_nick_name</code>.
     */
    public final TableField<GenseeUserJoinHistoryRecord, String> NICK_NAME = createField("f_nick_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_visit_ip</code>. ip
     */
    public final TableField<GenseeUserJoinHistoryRecord, String> VISIT_IP = createField("f_visit_ip", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "ip");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_join_time</code>. 加入时间
     */
    public final TableField<GenseeUserJoinHistoryRecord, Long> JOIN_TIME = createField("f_join_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "加入时间");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_leave_time</code>. 离开时间
     */
    public final TableField<GenseeUserJoinHistoryRecord, Long> LEAVE_TIME = createField("f_leave_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "离开时间");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_join_type</code>. 终端类型（0：pc,1：app）
     */
    public final TableField<GenseeUserJoinHistoryRecord, Integer> JOIN_TYPE = createField("f_join_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "终端类型（0：pc,1：app）");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_user_type</code>. 用户类型（0：内部用户,1：外部用户）
     */
    public final TableField<GenseeUserJoinHistoryRecord, Integer> USER_TYPE = createField("f_user_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "用户类型（0：内部用户,1：外部用户）");

    /**
     * The column <code>course-study.t_gensee_user_join_history.f_modify_date</code>. 修改时间
     */
    public final TableField<GenseeUserJoinHistoryRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>course-study.t_gensee_user_join_history</code> table reference
     */
    public GenseeUserJoinHistory() {
        this("t_gensee_user_join_history", null);
    }

    /**
     * Create an aliased <code>course-study.t_gensee_user_join_history</code> table reference
     */
    public GenseeUserJoinHistory(String alias) {
        this(alias, GENSEE_USER_JOIN_HISTORY);
    }

    private GenseeUserJoinHistory(String alias, Table<GenseeUserJoinHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private GenseeUserJoinHistory(String alias, Table<GenseeUserJoinHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "直播历史记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GenseeUserJoinHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_GENSEE_USER_JOIN_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GenseeUserJoinHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<GenseeUserJoinHistoryRecord>>asList(Keys.KEY_T_GENSEE_USER_JOIN_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GenseeUserJoinHistory as(String alias) {
        return new GenseeUserJoinHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GenseeUserJoinHistory rename(String name) {
        return new GenseeUserJoinHistory(name, null);
    }
}
