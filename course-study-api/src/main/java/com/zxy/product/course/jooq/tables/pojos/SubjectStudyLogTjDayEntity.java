/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ISubjectStudyLogTjDay;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectStudyLogTjDayEntity extends BaseEntity implements ISubjectStudyLogTjDay {

    private static final long serialVersionUID = 1L;

    private String    memberId;
    private String    subjectId;
    private Integer   studyTime;
    private Integer   day;
    private Integer   month;
    private Integer   year;
    private Timestamp modifyDate;

    public SubjectStudyLogTjDayEntity() {}

    public SubjectStudyLogTjDayEntity(SubjectStudyLogTjDayEntity value) {
        this.memberId = value.memberId;
        this.subjectId = value.subjectId;
        this.studyTime = value.studyTime;
        this.day = value.day;
        this.month = value.month;
        this.year = value.year;
        this.modifyDate = value.modifyDate;
    }

    public SubjectStudyLogTjDayEntity(
        String    id,
        String    memberId,
        String    subjectId,
        Integer   studyTime,
        Integer   day,
        Integer   month,
        Integer   year,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.subjectId = subjectId;
        this.studyTime = studyTime;
        this.day = day;
        this.month = month;
        this.year = year;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getSubjectId() {
        return this.subjectId;
    }

    @Override
    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubjectStudyLogTjDayEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(subjectId);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(day);
        sb.append(", ").append(month);
        sb.append(", ").append(year);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubjectStudyLogTjDay from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setSubjectId(from.getSubjectId());
        setStudyTime(from.getStudyTime());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubjectStudyLogTjDay> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SubjectStudyLogTjDayEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.SubjectStudyLogTjDayRecord r = new com.zxy.product.course.jooq.tables.records.SubjectStudyLogTjDayRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.SUBJECT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.SUBJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.DAY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.DAY, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.DAY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MONTH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MONTH, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.YEAR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.YEAR, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
