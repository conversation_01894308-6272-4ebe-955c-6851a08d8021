/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_07;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressArchived_07;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * course_study_progress已归档数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressArchived_07Record extends UpdatableRecordImpl<CourseStudyProgressArchived_07Record> implements Record5<String, String, String, Long, Integer>, ICourseStudyProgressArchived_07 {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_study_progress_archived_07.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_archived_07.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_archived_07.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_archived_07.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_archived_07.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_archived_07.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_archived_07.f_begin_time</code>. 归档时间
     */
    @Override
    public void setBeginTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_archived_07.f_begin_time</code>. 归档时间
     */
    @Override
    public Long getBeginTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_archived_07.f_finish_status</code>. 完成状态
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_archived_07.f_finish_status</code>. 完成状态
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Integer> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.BEGIN_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.FINISH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getBeginTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getFinishStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record value4(Long value) {
        setBeginTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record value5(Integer value) {
        setFinishStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_07Record values(String value1, String value2, String value3, Long value4, Integer value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProgressArchived_07 from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setBeginTime(from.getBeginTime());
        setFinishStatus(from.getFinishStatus());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProgressArchived_07> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseStudyProgressArchived_07Record
     */
    public CourseStudyProgressArchived_07Record() {
        super(CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07);
    }

    /**
     * Create a detached, initialised CourseStudyProgressArchived_07Record
     */
    public CourseStudyProgressArchived_07Record(String id, String memberId, String courseId, Long beginTime, Integer finishStatus) {
        super(CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, beginTime);
        set(4, finishStatus);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressArchived_07Entity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressArchived_07Entity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressArchived_07Entity)source;
        pojo.into(this);
        return true;
    }
}
