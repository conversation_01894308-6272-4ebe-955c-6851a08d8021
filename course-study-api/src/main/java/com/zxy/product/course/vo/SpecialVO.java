package com.zxy.product.course.vo;

import java.io.Serializable;

/**
 * 课程|直播|专题特殊处理：若首页配置了课程|直播|专题的图片地址和备注，优先使用首页配置的
 * <AUTHOR>
 * @date 2024年11月15日 16:04
 */
public class SpecialVO implements Serializable {
    private static final long serialVersionUID = 6377088210352173456L;

    /**特殊处理（数据Id）*/
    private String id;

    /**特殊处理（数据图片地址）*/
    private String imgPath;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getImgPath() { return imgPath; }

    public void setImgPath(String imgPath) { this.imgPath = imgPath; }

    @Override
    public String toString() {
        return "SpecialVO{" +
                "id='" + id + '\'' +
                ", imgPath='" + imgPath + '\'' +
                '}';
    }
}
