/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubjectSectionStudyLogHlRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectSectionStudyLogHl extends TableImpl<SubjectSectionStudyLogHlRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_subject_section_study_log_hl</code>
     */
    public static final SubjectSectionStudyLogHl SUBJECT_SECTION_STUDY_LOG_HL = new SubjectSectionStudyLogHl();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubjectSectionStudyLogHlRecord> getRecordType() {
        return SubjectSectionStudyLogHlRecord.class;
    }

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_id</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_member_id</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_subject_id</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, String> SUBJECT_ID = createField("f_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_section_id</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_client_type</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_finish_status</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_study_time</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_hl.f_create_time</code>.
     */
    public final TableField<SubjectSectionStudyLogHlRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>course-study.t_subject_section_study_log_hl</code> table reference
     */
    public SubjectSectionStudyLogHl() {
        this("t_subject_section_study_log_hl", null);
    }

    /**
     * Create an aliased <code>course-study.t_subject_section_study_log_hl</code> table reference
     */
    public SubjectSectionStudyLogHl(String alias) {
        this(alias, SUBJECT_SECTION_STUDY_LOG_HL);
    }

    private SubjectSectionStudyLogHl(String alias, Table<SubjectSectionStudyLogHlRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubjectSectionStudyLogHl(String alias, Table<SubjectSectionStudyLogHlRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubjectSectionStudyLogHlRecord> getPrimaryKey() {
        return Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_HL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubjectSectionStudyLogHlRecord>> getKeys() {
        return Arrays.<UniqueKey<SubjectSectionStudyLogHlRecord>>asList(Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_HL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectSectionStudyLogHl as(String alias) {
        return new SubjectSectionStudyLogHl(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubjectSectionStudyLogHl rename(String name) {
        return new SubjectSectionStudyLogHl(name, null);
    }
}
