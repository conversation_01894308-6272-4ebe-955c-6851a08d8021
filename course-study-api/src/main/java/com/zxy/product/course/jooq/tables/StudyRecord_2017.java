/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.StudyRecord_2017Record;

import java.sql.Date;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyRecord_2017 extends TableImpl<StudyRecord_2017Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_study_record_2017</code>
     */
    public static final StudyRecord_2017 STUDY_RECORD_2017 = new StudyRecord_2017();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyRecord_2017Record> getRecordType() {
        return StudyRecord_2017Record.class;
    }

    /**
     * The column <code>course-study.t_study_record_2017.f_course_id</code>.
     */
    public final TableField<StudyRecord_2017Record, Integer> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_course_name</code>.
     */
    public final TableField<StudyRecord_2017Record, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(240).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_person_code</code>.
     */
    public final TableField<StudyRecord_2017Record, String> PERSON_CODE = createField("f_person_code", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_person_name</code>.
     */
    public final TableField<StudyRecord_2017Record, String> PERSON_NAME = createField("f_person_name", org.jooq.impl.SQLDataType.VARCHAR.length(400).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_organization_name</code>.
     */
    public final TableField<StudyRecord_2017Record, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_organization_id</code>.
     */
    public final TableField<StudyRecord_2017Record, Long> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_start_time</code>.
     */
    public final TableField<StudyRecord_2017Record, Date> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.DATE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DATE)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_end_time</code>.
     */
    public final TableField<StudyRecord_2017Record, Date> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.DATE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DATE)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_status</code>.
     */
    public final TableField<StudyRecord_2017Record, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_duration</code>.
     */
    public final TableField<StudyRecord_2017Record, Long> DURATION = createField("f_duration", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_cid</code>.
     */
    public final TableField<StudyRecord_2017Record, String> CID = createField("f_cid", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_study_record_2017.f_client</code>. 0:PC  1:APP
     */
    public final TableField<StudyRecord_2017Record, Integer> CLIENT = createField("f_client", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0:PC  1:APP");

    /**
     * The column <code>course-study.t_study_record_2017.f_placeholder</code>.
     */
    public final TableField<StudyRecord_2017Record, String> PLACEHOLDER = createField("f_placeholder", org.jooq.impl.SQLDataType.VARCHAR.length(5).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * Create a <code>course-study.t_study_record_2017</code> table reference
     */
    public StudyRecord_2017() {
        this("t_study_record_2017", null);
    }

    /**
     * Create an aliased <code>course-study.t_study_record_2017</code> table reference
     */
    public StudyRecord_2017(String alias) {
        this(alias, STUDY_RECORD_2017);
    }

    private StudyRecord_2017(String alias, Table<StudyRecord_2017Record> aliased) {
        this(alias, aliased, null);
    }

    private StudyRecord_2017(String alias, Table<StudyRecord_2017Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyRecord_2017Record> getPrimaryKey() {
        return Keys.KEY_T_STUDY_RECORD_2017_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyRecord_2017Record>> getKeys() {
        return Arrays.<UniqueKey<StudyRecord_2017Record>>asList(Keys.KEY_T_STUDY_RECORD_2017_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyRecord_2017 as(String alias) {
        return new StudyRecord_2017(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyRecord_2017 rename(String name) {
        return new StudyRecord_2017(name, null);
    }
}
