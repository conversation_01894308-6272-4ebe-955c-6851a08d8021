package com.zxy.product.course.api.archived;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService
public interface CourseStudyProgressArchivedService {

    /**
     * 已经归档的数据表中是否存在当前人和课程的记录
     * @param memberId
     * @param courseId
     * @return true:存在 false:不存在
     */
    Boolean checkExistenceArchived(String memberId, String courseId);

    Boolean checkAllStatusExistenceArchived(String memberId, String courseId);

    Map<String, Boolean> checkExistenceArchived(List<String> memberIds, String courseId);

    Map<String, Integer> getArchivedStatus(String memberId, List<String> courseIds);

    Integer deleteArchived(String memberId, String courseId);

    /**
     * 查询归档记录存在的分表和id
     * @param memberId
     * @param courseId
     * @return key 表名称 value 记录ID(没命中就是空串)
     */
    Map<String,String> findArchivedId(String memberId, String courseId);

    Integer deleteArchived(Map<String,String> map);
    /**
     * 查看当前用户的个性化专题的数据是否被归档
     * 只有专题数据 咩有专题下的课程数据
     * 如果被归档了返回基本信息 否则返回空
     *
     * @param memberId
     * @param subjectUrl 个性化专题配置的链接
     * @param id
     * @return CourseInfo
     */
    CourseInfo whetherPersonalizedTopicsAreArchived(String memberId, Optional<String> subjectUrl, Optional<String> id);
    Boolean courseBackToTheSource(boolean isSubject,
                                  String courseStudyTableName,
                                  String courseSectionProgressTableName,
                                  CourseStudyProgress c,
                                  List<CourseSectionStudyProgress> progress,
                                  List<CourseRegister> courseRegisters,
                                  String subjectDayTableName,
                                  String courseDayTableName,
                                  List<SubjectStudyLogAhDay> subjectStudyLogDays,
                                  List<CourseSectionStudyLogAhDay> courseSectionStudyLogDays) ;

    /**
     * 查询归档课程是否更新
     * @param courseId
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true)
    Optional<CourseStudyProgressArchived>  findByCourseIdAndMemberId(String courseId, String currentUserId);
}
