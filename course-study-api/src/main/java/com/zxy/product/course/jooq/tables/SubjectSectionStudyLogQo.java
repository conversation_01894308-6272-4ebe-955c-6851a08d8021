/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubjectSectionStudyLogQoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectSectionStudyLogQo extends TableImpl<SubjectSectionStudyLogQoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_subject_section_study_log_qo</code>
     */
    public static final SubjectSectionStudyLogQo SUBJECT_SECTION_STUDY_LOG_QO = new SubjectSectionStudyLogQo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubjectSectionStudyLogQoRecord> getRecordType() {
        return SubjectSectionStudyLogQoRecord.class;
    }

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_id</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_member_id</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_subject_id</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, String> SUBJECT_ID = createField("f_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_section_id</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_client_type</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_finish_status</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_study_time</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_qo.f_create_time</code>.
     */
    public final TableField<SubjectSectionStudyLogQoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>course-study.t_subject_section_study_log_qo</code> table reference
     */
    public SubjectSectionStudyLogQo() {
        this("t_subject_section_study_log_qo", null);
    }

    /**
     * Create an aliased <code>course-study.t_subject_section_study_log_qo</code> table reference
     */
    public SubjectSectionStudyLogQo(String alias) {
        this(alias, SUBJECT_SECTION_STUDY_LOG_QO);
    }

    private SubjectSectionStudyLogQo(String alias, Table<SubjectSectionStudyLogQoRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubjectSectionStudyLogQo(String alias, Table<SubjectSectionStudyLogQoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubjectSectionStudyLogQoRecord> getPrimaryKey() {
        return Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_QO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubjectSectionStudyLogQoRecord>> getKeys() {
        return Arrays.<UniqueKey<SubjectSectionStudyLogQoRecord>>asList(Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_QO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectSectionStudyLogQo as(String alias) {
        return new SubjectSectionStudyLogQo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubjectSectionStudyLogQo rename(String name) {
        return new SubjectSectionStudyLogQo(name, null);
    }
}
