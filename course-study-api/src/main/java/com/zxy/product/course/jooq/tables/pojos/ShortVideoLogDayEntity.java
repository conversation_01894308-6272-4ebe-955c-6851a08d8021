/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IShortVideoLogDay;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 个人短视频每日时长表-短视频人课天表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ShortVideoLogDayEntity extends BaseEntity implements IShortVideoLogDay {

    private static final long serialVersionUID = 1L;

    private String    memberName;
    private String    memberId;
    private String    unit;
    private String    organizationId;
    private String    job;
    private String    line;
    private String    memberStatus;
    private String    shortVideoName;
    private String    shortVideoCode;
    private Integer   studyTime;
    private Integer   day;
    private Integer   month;
    private Integer   year;
    private Timestamp modifyDate;

    public ShortVideoLogDayEntity() {}

    public ShortVideoLogDayEntity(ShortVideoLogDayEntity value) {
        this.memberName = value.memberName;
        this.memberId = value.memberId;
        this.unit = value.unit;
        this.organizationId = value.organizationId;
        this.job = value.job;
        this.line = value.line;
        this.memberStatus = value.memberStatus;
        this.shortVideoName = value.shortVideoName;
        this.shortVideoCode = value.shortVideoCode;
        this.studyTime = value.studyTime;
        this.day = value.day;
        this.month = value.month;
        this.year = value.year;
        this.modifyDate = value.modifyDate;
    }

    public ShortVideoLogDayEntity(
        String    id,
        String    memberName,
        String    memberId,
        String    unit,
        String    organizationId,
        String    job,
        String    line,
        String    memberStatus,
        String    shortVideoName,
        String    shortVideoCode,
        Integer   studyTime,
        Integer   day,
        Integer   month,
        Integer   year,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.memberName = memberName;
        this.memberId = memberId;
        this.unit = unit;
        this.organizationId = organizationId;
        this.job = job;
        this.line = line;
        this.memberStatus = memberStatus;
        this.shortVideoName = shortVideoName;
        this.shortVideoCode = shortVideoCode;
        this.studyTime = studyTime;
        this.day = day;
        this.month = month;
        this.year = year;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberName() {
        return this.memberName;
    }

    @Override
    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getUnit() {
        return this.unit;
    }

    @Override
    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getJob() {
        return this.job;
    }

    @Override
    public void setJob(String job) {
        this.job = job;
    }

    @Override
    public String getLine() {
        return this.line;
    }

    @Override
    public void setLine(String line) {
        this.line = line;
    }

    @Override
    public String getMemberStatus() {
        return this.memberStatus;
    }

    @Override
    public void setMemberStatus(String memberStatus) {
        this.memberStatus = memberStatus;
    }

    @Override
    public String getShortVideoName() {
        return this.shortVideoName;
    }

    @Override
    public void setShortVideoName(String shortVideoName) {
        this.shortVideoName = shortVideoName;
    }

    @Override
    public String getShortVideoCode() {
        return this.shortVideoCode;
    }

    @Override
    public void setShortVideoCode(String shortVideoCode) {
        this.shortVideoCode = shortVideoCode;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ShortVideoLogDayEntity (");

        sb.append(getId());
        sb.append(", ").append(memberName);
        sb.append(", ").append(memberId);
        sb.append(", ").append(unit);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(job);
        sb.append(", ").append(line);
        sb.append(", ").append(memberStatus);
        sb.append(", ").append(shortVideoName);
        sb.append(", ").append(shortVideoCode);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(day);
        sb.append(", ").append(month);
        sb.append(", ").append(year);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IShortVideoLogDay from) {
        setId(from.getId());
        setMemberName(from.getMemberName());
        setMemberId(from.getMemberId());
        setUnit(from.getUnit());
        setOrganizationId(from.getOrganizationId());
        setJob(from.getJob());
        setLine(from.getLine());
        setMemberStatus(from.getMemberStatus());
        setShortVideoName(from.getShortVideoName());
        setShortVideoCode(from.getShortVideoCode());
        setStudyTime(from.getStudyTime());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IShortVideoLogDay> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ShortVideoLogDayEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.ShortVideoLogDayRecord r = new com.zxy.product.course.jooq.tables.records.ShortVideoLogDayRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ID, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_NAME, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.UNIT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.UNIT, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.UNIT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ORGANIZATION_ID, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.JOB) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.JOB, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.JOB));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.LINE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.LINE, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.LINE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_STATUS, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MEMBER_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_NAME, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_CODE, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.SHORT_VIDEO_CODE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.DAY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.DAY, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.DAY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MONTH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MONTH, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.YEAR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.YEAR, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
