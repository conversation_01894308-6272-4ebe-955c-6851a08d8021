/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.RemodelingRoleDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 重塑培训计划-角色信息表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RemodelingRoleDetail extends TableImpl<RemodelingRoleDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_remodeling_role_detail</code>
     */
    public static final RemodelingRoleDetail REMODELING_ROLE_DETAIL = new RemodelingRoleDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RemodelingRoleDetailRecord> getRecordType() {
        return RemodelingRoleDetailRecord.class;
    }

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_id</code>. id
     */
    public final TableField<RemodelingRoleDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_subject_id</code>. 专题id
     */
    public final TableField<RemodelingRoleDetailRecord, String> SUBJECT_ID = createField("f_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专题id");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_role_name</code>. 角色名称
     */
    public final TableField<RemodelingRoleDetailRecord, String> ROLE_NAME = createField("f_role_name", org.jooq.impl.SQLDataType.VARCHAR.length(128).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "角色名称");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_chapter_type</code>. 章类型，用于首页区分展示（1.5G核心技术人才技能重塑项目；2.“云改”核心技术人次技能重塑项目;3.“ 安全核心技术人才技能重塑” ; 4.“软件开发核心技术人才技能重塑”
     */
    public final TableField<RemodelingRoleDetailRecord, String> CHAPTER_TYPE = createField("f_chapter_type", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "章类型，用于首页区分展示（1.5G核心技术人才技能重塑项目；2.“云改”核心技术人次技能重塑项目;3.“ 安全核心技术人才技能重塑” ; 4.“软件开发核心技术人才技能重塑”");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_section_type</code>. 节类型，用于首页区分展示（1.5G无线网；2.5G核心网；3.5G传输网；4.大数据和人工智能；5.云服务；6.云计算；7.安全核心技术人才技能重塑；8.软件开发核心技术人才技能重塑）
     */
    public final TableField<RemodelingRoleDetailRecord, String> SECTION_TYPE = createField("f_section_type", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "节类型，用于首页区分展示（1.5G无线网；2.5G核心网；3.5G传输网；4.大数据和人工智能；5.云服务；6.云计算；7.安全核心技术人才技能重塑；8.软件开发核心技术人才技能重塑）");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_role_type</code>. 角色类型（1.关键角色；2.非关键角色）
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> ROLE_TYPE = createField("f_role_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "角色类型（1.关键角色；2.非关键角色）");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_role_level</code>. 角色等级（1.初级；2.中级；3.高级）
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> ROLE_LEVEL = createField("f_role_level", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "角色等级（1.初级；2.中级；3.高级）");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_business_type</code>. 专题类型（1.内部，2.外部）
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "专题类型（1.内部，2.外部）");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_url</code>. 课程链接
     */
    public final TableField<RemodelingRoleDetailRecord, String> URL = createField("f_url", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程链接");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_certificate_image</code>. 角色对应的证书缩略图路径
     */
    public final TableField<RemodelingRoleDetailRecord, String> CERTIFICATE_IMAGE = createField("f_certificate_image", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "角色对应的证书缩略图路径");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_icon_path</code>. 角色对应图标路径
     */
    public final TableField<RemodelingRoleDetailRecord, String> ICON_PATH = createField("f_icon_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "角色对应图标路径");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_home_sort</code>. 首页排序字段
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> HOME_SORT = createField("f_home_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "首页排序字段");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_sign_sort</code>. 报名页排序字段
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> SIGN_SORT = createField("f_sign_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "报名页排序字段");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_is_parent</code>. 是否为父级角色：0.否，1.是
     */
    public final TableField<RemodelingRoleDetailRecord, Integer> IS_PARENT = createField("f_is_parent", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为父级角色：0.否，1.是");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_parent_id</code>. 父级角色id
     */
    public final TableField<RemodelingRoleDetailRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "父级角色id");

    /**
     * The column <code>course-study.t_remodeling_role_detail.f_level_icon_path</code>. 首页各个角色等级图标
     */
    public final TableField<RemodelingRoleDetailRecord, String> LEVEL_ICON_PATH = createField("f_level_icon_path", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "首页各个角色等级图标");

    /**
     * Create a <code>course-study.t_remodeling_role_detail</code> table reference
     */
    public RemodelingRoleDetail() {
        this("t_remodeling_role_detail", null);
    }

    /**
     * Create an aliased <code>course-study.t_remodeling_role_detail</code> table reference
     */
    public RemodelingRoleDetail(String alias) {
        this(alias, REMODELING_ROLE_DETAIL);
    }

    private RemodelingRoleDetail(String alias, Table<RemodelingRoleDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private RemodelingRoleDetail(String alias, Table<RemodelingRoleDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "重塑培训计划-角色信息表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RemodelingRoleDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_REMODELING_ROLE_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RemodelingRoleDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<RemodelingRoleDetailRecord>>asList(Keys.KEY_T_REMODELING_ROLE_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingRoleDetail as(String alias) {
        return new RemodelingRoleDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RemodelingRoleDetail rename(String name) {
        return new RemodelingRoleDetail(name, null);
    }
}
