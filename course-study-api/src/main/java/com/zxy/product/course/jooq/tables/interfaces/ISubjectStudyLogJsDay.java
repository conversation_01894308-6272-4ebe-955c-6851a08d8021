/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubjectStudyLogJsDay extends Serializable {

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_subject_id</code>. 专题id
     */
    public void setSubjectId(String value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_subject_id</code>. 专题id
     */
    public String getSubjectId();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_study_time</code>. 当天学习总时长
     */
    public void setStudyTime(Integer value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_study_time</code>. 当天学习总时长
     */
    public Integer getStudyTime();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_day</code>. 日
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_day</code>. 日
     */
    public Integer getDay();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_month</code>. 月
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_month</code>. 月
     */
    public Integer getMonth();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_year</code>. 年
     */
    public void setYear(Integer value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_year</code>. 年
     */
    public Integer getYear();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_subject_study_log_js_day.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_subject_study_log_js_day.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubjectStudyLogJsDay
     */
    public void from(ISubjectStudyLogJsDay from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubjectStudyLogJsDay
     */
    public <E extends ISubjectStudyLogJsDay> E into(E into);
}
