package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.StudyReportAnalysisManagersEntity;

import java.util.List;

/**
 * 学情分析报告管理员表
 */
public class StudyReportAnalysisManagers extends StudyReportAnalysisManagersEntity {
    private static final long serialVersionUID = 3153093191797398370L;

    /** 数据查看范围: 全集团 */
    public static final int VIEW_SCOPE_ORG_ALL = 0;

    /** 数据查看范围: 本机构 */
    public static final int VIEW_SCOPE_ORG_CURRENT = 1;

    private String CourseName;

    private Organization sponsoringOrganization;

    private List<Organization> viewScopeOrganization;

    private Member member;

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public String getCourseName() {
        return CourseName;
    }

    public void setCourseName(String courseName) {
        CourseName = courseName;
    }

    public Organization getSponsoringOrganization() {
        return sponsoringOrganization;
    }

    public void setSponsoringOrganization(Organization sponsoringOrganization) {
        this.sponsoringOrganization = sponsoringOrganization;
    }

    public List<Organization> getViewScopeOrganization() {
        return viewScopeOrganization;
    }

    public void setViewScopeOrganization(List<Organization> viewScopeOrganization) {
        this.viewScopeOrganization = viewScopeOrganization;
    }
}
