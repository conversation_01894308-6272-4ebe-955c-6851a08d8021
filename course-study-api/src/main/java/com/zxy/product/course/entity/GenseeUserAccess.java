package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeUserAccessEntity;
import org.springframework.util.StringUtils;

import java.util.Random;

/**
 * Created by <PERSON> on 2017/3/9.
 */
public class GenseeUserAccess extends GenseeUserAccessEntity {

    /**
	 *
	 */
	private static final long serialVersionUID = 742896552602954776L;
	private Member member;
	private GenseeWebCast genseeWebCast;
	private Integer status;// 辅助字段，状态1未完成  2未开始 ，主要给我的任务和个人中心接口使用

	public static final Integer STATUS_UNFINISH = 1; // 未完成（已参与）
	public static final Integer STATUS_UNSTART = 2; // 未开始
	public static final Integer STATUS_NOJOIN = 3; // 未参与

    //添加新字段 观看时长
    private Long watchTime;

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public GenseeWebCast getGenseeWebCast() {
        return genseeWebCast;
    }

    public void setGenseeWebCast(GenseeWebCast genseeWebCast) {
        this.genseeWebCast = genseeWebCast;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getWatchTime() {
        return watchTime;
    }

    public void setWatchTime(Long watchTime) {
        this.watchTime = watchTime;
    }

    /**
     * 根据memberId转换uid
     * @param memberId
     * @return uid
     */
    public static long buildUid(String memberId) {
        /*
         * 1.展示互动要求同一场直播中，参加的用户的uid不同，uid为Long类型。
         * 2.历史版本uid是通过“System.currentTimeMillis()+1000000000+cache.increment("uid")”生成，
         * 不同的用户ID有很大概率转换出相同的uid，导致参与直播的用户有概率出现被误判为多地登录，
         * 从而被踢下线的问题。
         * 3.为保证逻辑与基础版本相似，使用“用户ID的hashCode + 3位随机数”来尽量保证转换出唯一的uid。
         */
        // hashCode最大长度为10位 + 3位随机数
        StringBuilder str = new StringBuilder(13);
        if(!StringUtils.isEmpty(memberId)){
            str.append(Math.abs(memberId.replace("-", "").hashCode()));
        }
        str.append(Math.abs(new Random().nextInt(1000)));
        return Long.valueOf(str.toString());
    }




}
