package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.DjLog;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> on 2024/4/17
 */
@RemoteService(timeout = 100000)
public interface DjLogService {

    @Transactional
    DjLog insertLog(String ihrcode, String content, int state);
}
