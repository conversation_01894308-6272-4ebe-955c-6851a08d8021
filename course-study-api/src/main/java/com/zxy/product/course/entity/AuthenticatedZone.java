package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AuthenticatedZoneEntity;

import java.util.List;

public class AuthenticatedZone extends AuthenticatedZoneEntity{
    public final static String URI = "";
    public static final Integer PUBLISH = 1;
    public static final Integer NOT_PUBLISHED = 0;
    private String orgName;
    private String  coverPath;
    private String  bannerPath;
    private List<AuthenticatedGroup> groups;
    public String getOrgName() {
        return orgName;
    }

    public AuthenticatedZone setOrgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    public List<AuthenticatedGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<AuthenticatedGroup> groups) {
        this.groups = groups;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public String getBannerPath() {
        return bannerPath;
    }

    public void setBannerPath(String bannerPath) {
        this.bannerPath = bannerPath;
    }
}
