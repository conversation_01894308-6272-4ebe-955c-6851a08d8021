/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 课程节学习进度sd
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseSectionStudyProgressSd extends Serializable {

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_section_id</code>. 课程节ID
     */
    public void setSectionId(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_section_id</code>. 课程节ID
     */
    public String getSectionId();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_begin_time</code>. 学习开始时间
     */
    public void setBeginTime(Long value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_begin_time</code>. 学习开始时间
     */
    public Long getBeginTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_finish_time</code>. 完成时间
     */
    public void setFinishTime(Long value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_finish_time</code>. 完成时间
     */
    public Long getFinishTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_completed_rate</code>. 完成进度(百分比)
     */
    public void setCompletedRate(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_completed_rate</code>. 完成进度(百分比)
     */
    public Integer getCompletedRate();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public void setStudyTotalTime(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public Integer getStudyTotalTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_last_access_time</code>. 最后访问时间
     */
    public void setLastAccessTime(Long value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_last_access_time</code>. 最后访问时间
     */
    public Long getLastAccessTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_exam_status</code>. 考试状态 0 未通过 1通过 2待评卷 3.待考试
     */
    public void setExamStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_exam_status</code>. 考试状态 0 未通过 1通过 2待评卷 3.待考试
     */
    public Integer getExamStatus();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_lesson_location</code>. 最后学习退出的位置
     */
    public void setLessonLocation(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_lesson_location</code>. 最后学习退出的位置
     */
    public String getLessonLocation();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_commit_time</code>. 提交时间
     */
    public void setCommitTime(Long value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_commit_time</code>. 提交时间
     */
    public Long getCommitTime();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_submit_text</code>. 提交内容
     */
    public void setSubmitText(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_submit_text</code>. 提交内容
     */
    public String getSubmitText();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_audit_member_id</code>. 审核人ID
     */
    public void setAuditMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_audit_member_id</code>. 审核人ID
     */
    public String getAuditMemberId();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_score</code>. 评分，用于存储考试、评估、作业评分
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_score</code>. 评分，用于存储考试、评估、作业评分
     */
    public Integer getScore();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_comments</code>. 作业审核评语
     */
    public void setComments(String value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_comments</code>. 作业审核评语
     */
    public String getComments();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_audit_pass</code>. 作业审核是否通过，1-通过；2-打回重新提交
     */
    public void setAuditPass(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_audit_pass</code>. 作业审核是否通过，1-通过；2-打回重新提交
     */
    public Integer getAuditPass();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_visits</code>. 访问知识的次数（当前章节为知识时使用）
     */
    public void setVisits(Integer value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_visits</code>. 访问知识的次数（当前章节为知识时使用）
     */
    public Integer getVisits();

    /**
     * Setter for <code>course-study.t_course_section_study_progress_sd.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_course_section_study_progress_sd.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseSectionStudyProgressSd
     */
    public void from(ICourseSectionStudyProgressSd from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseSectionStudyProgressSd
     */
    public <E extends ICourseSectionStudyProgressSd> E into(E into);
}
