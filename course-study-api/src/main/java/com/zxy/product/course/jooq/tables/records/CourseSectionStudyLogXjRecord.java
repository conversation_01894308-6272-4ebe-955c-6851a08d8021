/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseSectionStudyLogXj;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyLogXj;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogXjRecord extends UpdatableRecordImpl<CourseSectionStudyLogXjRecord> implements Record17<String, String, String, String, String, Integer, Integer, Integer, Integer, Long, Long, String, String, Integer, String, Integer, String>, ICourseSectionStudyLogXj {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_member_id</code>.
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_member_id</code>.
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_course_id</code>.
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_course_id</code>.
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_section_id</code>.
     */
    @Override
    public void setSectionId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_section_id</code>.
     */
    @Override
    public String getSectionId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_section_scrom_id</code>.
     */
    @Override
    public void setSectionScromId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_section_scrom_id</code>.
     */
    @Override
    public String getSectionScromId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_client_type</code>.
     */
    @Override
    public void setClientType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_client_type</code>.
     */
    @Override
    public Integer getClientType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_completed_rate</code>.
     */
    @Override
    public void setCompletedRate(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_completed_rate</code>.
     */
    @Override
    public Integer getCompletedRate() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_finish_status</code>.
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_finish_status</code>.
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_study_time</code>.
     */
    @Override
    public void setStudyTime(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_study_time</code>.
     */
    @Override
    public Integer getStudyTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_commit_time</code>.
     */
    @Override
    public void setCommitTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_commit_time</code>.
     */
    @Override
    public Long getCommitTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_submit_text</code>.
     */
    @Override
    public void setSubmitText(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_submit_text</code>.
     */
    @Override
    public String getSubmitText() {
        return (String) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_audit_member_id</code>.
     */
    @Override
    public void setAuditMemberId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_audit_member_id</code>.
     */
    @Override
    public String getAuditMemberId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_score</code>.
     */
    @Override
    public void setScore(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_score</code>.
     */
    @Override
    public Integer getScore() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_comments</code>.
     */
    @Override
    public void setComments(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_comments</code>.
     */
    @Override
    public String getComments() {
        return (String) get(14);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_exam_status</code>.
     */
    @Override
    public void setExamStatus(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_exam_status</code>.
     */
    @Override
    public Integer getExamStatus() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_xj.f_lesson_location</code>.
     */
    @Override
    public void setLessonLocation(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_xj.f_lesson_location</code>.
     */
    @Override
    public String getLessonLocation() {
        return (String) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, String, Integer, Integer, Integer, Integer, Long, Long, String, String, Integer, String, Integer, String> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, String, Integer, Integer, Integer, Integer, Long, Long, String, String, Integer, String, Integer, String> valuesRow() {
        return (Row17) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.SECTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.SECTION_SCROM_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.CLIENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.COMPLETED_RATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.FINISH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.COMMIT_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.SUBMIT_TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.AUDIT_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field14() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.COMMENTS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field16() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.EXAM_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field17() {
        return CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.LESSON_LOCATION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getSectionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getSectionScromId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getClientType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getCompletedRate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getFinishStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getCommitTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getSubmitText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getAuditMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value14() {
        return getScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getComments();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value16() {
        return getExamStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value17() {
        return getLessonLocation();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value4(String value) {
        setSectionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value5(String value) {
        setSectionScromId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value6(Integer value) {
        setClientType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value7(Integer value) {
        setCompletedRate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value8(Integer value) {
        setFinishStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value9(Integer value) {
        setStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value11(Long value) {
        setCommitTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value12(String value) {
        setSubmitText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value13(String value) {
        setAuditMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value14(Integer value) {
        setScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value15(String value) {
        setComments(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value16(Integer value) {
        setExamStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord value17(String value) {
        setLessonLocation(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXjRecord values(String value1, String value2, String value3, String value4, String value5, Integer value6, Integer value7, Integer value8, Integer value9, Long value10, Long value11, String value12, String value13, Integer value14, String value15, Integer value16, String value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyLogXj from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setSectionId(from.getSectionId());
        setSectionScromId(from.getSectionScromId());
        setClientType(from.getClientType());
        setCompletedRate(from.getCompletedRate());
        setFinishStatus(from.getFinishStatus());
        setStudyTime(from.getStudyTime());
        setCreateTime(from.getCreateTime());
        setCommitTime(from.getCommitTime());
        setSubmitText(from.getSubmitText());
        setAuditMemberId(from.getAuditMemberId());
        setScore(from.getScore());
        setComments(from.getComments());
        setExamStatus(from.getExamStatus());
        setLessonLocation(from.getLessonLocation());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyLogXj> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseSectionStudyLogXjRecord
     */
    public CourseSectionStudyLogXjRecord() {
        super(CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ);
    }

    /**
     * Create a detached, initialised CourseSectionStudyLogXjRecord
     */
    public CourseSectionStudyLogXjRecord(String id, String memberId, String courseId, String sectionId, String sectionScromId, Integer clientType, Integer completedRate, Integer finishStatus, Integer studyTime, Long createTime, Long commitTime, String submitText, String auditMemberId, Integer score, String comments, Integer examStatus, String lessonLocation) {
        super(CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, sectionId);
        set(4, sectionScromId);
        set(5, clientType);
        set(6, completedRate);
        set(7, finishStatus);
        set(8, studyTime);
        set(9, createTime);
        set(10, commitTime);
        set(11, submitText);
        set(12, auditMemberId);
        set(13, score);
        set(14, comments);
        set(15, examStatus);
        set(16, lessonLocation);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogXjEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogXjEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogXjEntity)source;
        pojo.into(this);
        return true;
    }
}
