/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 课程举报表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseInform extends Serializable {

    /**
     * Setter for <code>course-study.t_course_inform.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_inform.f_course_id</code>. 课程id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_course_id</code>. 课程id
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_inform.f_details</code>. 举报内容
     */
    public void setDetails(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_details</code>. 举报内容
     */
    public String getDetails();

    /**
     * Setter for <code>course-study.t_course_inform.f_state</code>. 审核状态(0=未审核,1=举报成功,2=举报未通过)
     */
    public void setState(Integer value);

    /**
     * Getter for <code>course-study.t_course_inform.f_state</code>. 审核状态(0=未审核,1=举报成功,2=举报未通过)
     */
    public Integer getState();

    /**
     * Setter for <code>course-study.t_course_inform.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_inform.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_inform.f_inform_time</code>. 举报时间
     */
    public void setInformTime(Long value);

    /**
     * Getter for <code>course-study.t_course_inform.f_inform_time</code>. 举报时间
     */
    public Long getInformTime();

    /**
     * Setter for <code>course-study.t_course_inform.f_member_id</code>. 举报人id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_member_id</code>. 举报人id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_course_inform.f_audit_time</code>. 审核时间
     */
    public void setAuditTime(Long value);

    /**
     * Getter for <code>course-study.t_course_inform.f_audit_time</code>. 审核时间
     */
    public Long getAuditTime();

    /**
     * Setter for <code>course-study.t_course_inform.f_audit_member_id</code>. 审核人id
     */
    public void setAuditMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_audit_member_id</code>. 审核人id
     */
    public String getAuditMemberId();

    /**
     * Setter for <code>course-study.t_course_inform.f_attachment_id</code>. 附件id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_attachment_id</code>. 附件id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>course-study.t_course_inform.f_reply</code>. 回复学员
     */
    public void setReply(String value);

    /**
     * Getter for <code>course-study.t_course_inform.f_reply</code>. 回复学员
     */
    public String getReply();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseInform
     */
    public void from(ICourseInform from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseInform
     */
    public <E extends ICourseInform> E into(E into);
}
