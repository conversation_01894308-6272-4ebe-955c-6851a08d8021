/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.MultidimensionalScoringSubject;
import com.zxy.product.course.jooq.tables.interfaces.IMultidimensionalScoringSubject;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 多维度评分专题关系表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MultidimensionalScoringSubjectRecord extends UpdatableRecordImpl<MultidimensionalScoringSubjectRecord> implements Record8<String, String, String, String, String, Long, String, Long>, IMultidimensionalScoringSubject {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_multidimensional_scoring_id</code>. 评分表ID
     */
    @Override
    public void setMultidimensionalScoringId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_multidimensional_scoring_id</code>. 评分表ID
     */
    @Override
    public String getMultidimensionalScoringId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_topic_id</code>. 主题ID
     */
    @Override
    public void setTopicId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_topic_id</code>. 主题ID
     */
    @Override
    public String getTopicId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_subject_id</code>. 专题ID
     */
    @Override
    public void setSubjectId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_subject_id</code>. 专题ID
     */
    @Override
    public String getSubjectId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_modify_member</code>. 创建人
     */
    @Override
    public void setModifyMember(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_modify_member</code>. 创建人
     */
    @Override
    public String getModifyMember() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_multidimensional_scoring_subject.f_modify_time</code>. 修改时间
     */
    @Override
    public void setModifyTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_multidimensional_scoring_subject.f_modify_time</code>. 修改时间
     */
    @Override
    public Long getModifyTime() {
        return (Long) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, Long, String, Long> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, Long, String, Long> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.MULTIDIMENSIONAL_SCORING_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.TOPIC_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.SUBJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.MODIFY_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.MODIFY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMultidimensionalScoringId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getTopicId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getSubjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getModifyMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getModifyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value2(String value) {
        setMultidimensionalScoringId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value3(String value) {
        setTopicId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value4(String value) {
        setSubjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value5(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value7(String value) {
        setModifyMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord value8(Long value) {
        setModifyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalScoringSubjectRecord values(String value1, String value2, String value3, String value4, String value5, Long value6, String value7, Long value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMultidimensionalScoringSubject from) {
        setId(from.getId());
        setMultidimensionalScoringId(from.getMultidimensionalScoringId());
        setTopicId(from.getTopicId());
        setSubjectId(from.getSubjectId());
        setCreateMember(from.getCreateMember());
        setCreateTime(from.getCreateTime());
        setModifyMember(from.getModifyMember());
        setModifyTime(from.getModifyTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMultidimensionalScoringSubject> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MultidimensionalScoringSubjectRecord
     */
    public MultidimensionalScoringSubjectRecord() {
        super(MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT);
    }

    /**
     * Create a detached, initialised MultidimensionalScoringSubjectRecord
     */
    public MultidimensionalScoringSubjectRecord(String id, String multidimensionalScoringId, String topicId, String subjectId, String createMember, Long createTime, String modifyMember, Long modifyTime) {
        super(MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT);

        set(0, id);
        set(1, multidimensionalScoringId);
        set(2, topicId);
        set(3, subjectId);
        set(4, createMember);
        set(5, createTime);
        set(6, modifyMember);
        set(7, modifyTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.MultidimensionalScoringSubjectEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.MultidimensionalScoringSubjectEntity pojo = (com.zxy.product.course.jooq.tables.pojos.MultidimensionalScoringSubjectEntity)source;
        pojo.into(this);
        return true;
    }
}
