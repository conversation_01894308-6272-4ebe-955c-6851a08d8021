/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubjectRoleCommentRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 新动能专题角色默认评论表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectRoleComment extends TableImpl<SubjectRoleCommentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_subject_role_comment</code>
     */
    public static final SubjectRoleComment SUBJECT_ROLE_COMMENT = new SubjectRoleComment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubjectRoleCommentRecord> getRecordType() {
        return SubjectRoleCommentRecord.class;
    }

    /**
     * The column <code>course-study.t_subject_role_comment.f_id</code>. 系统ID
     */
    public final TableField<SubjectRoleCommentRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>course-study.t_subject_role_comment.f_course_id</code>. 课程ID
     */
    public final TableField<SubjectRoleCommentRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "课程ID");

    /**
     * The column <code>course-study.t_subject_role_comment.f_head_img</code>. 评论默认头像
     */
    public final TableField<SubjectRoleCommentRecord, String> HEAD_IMG = createField("f_head_img", org.jooq.impl.SQLDataType.VARCHAR.length(64).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评论默认头像");

    /**
     * The column <code>course-study.t_subject_role_comment.f_level</code>. 评论等级
     */
    public final TableField<SubjectRoleCommentRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("5", org.jooq.impl.SQLDataType.INTEGER)), this, "评论等级");

    /**
     * The column <code>course-study.t_subject_role_comment.f_content</code>. 评论内容
     */
    public final TableField<SubjectRoleCommentRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(256).nullable(false), this, "评论内容");

    /**
     * The column <code>course-study.t_subject_role_comment.f_create_time</code>. 评论时间
     */
    public final TableField<SubjectRoleCommentRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "评论时间");

    /**
     * Create a <code>course-study.t_subject_role_comment</code> table reference
     */
    public SubjectRoleComment() {
        this("t_subject_role_comment", null);
    }

    /**
     * Create an aliased <code>course-study.t_subject_role_comment</code> table reference
     */
    public SubjectRoleComment(String alias) {
        this(alias, SUBJECT_ROLE_COMMENT);
    }

    private SubjectRoleComment(String alias, Table<SubjectRoleCommentRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubjectRoleComment(String alias, Table<SubjectRoleCommentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "新动能专题角色默认评论表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubjectRoleCommentRecord> getPrimaryKey() {
        return Keys.KEY_T_SUBJECT_ROLE_COMMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubjectRoleCommentRecord>> getKeys() {
        return Arrays.<UniqueKey<SubjectRoleCommentRecord>>asList(Keys.KEY_T_SUBJECT_ROLE_COMMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectRoleComment as(String alias) {
        return new SubjectRoleComment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubjectRoleComment rename(String name) {
        return new SubjectRoleComment(name, null);
    }
}
