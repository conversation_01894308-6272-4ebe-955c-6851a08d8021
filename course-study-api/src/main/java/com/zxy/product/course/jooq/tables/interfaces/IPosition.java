/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPosition extends Serializable {

    /**
     * Setter for <code>course-study.t_position.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_position.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_position.f_name</code>. 岗位名称 
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_position.f_name</code>. 岗位名称 
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_position.f_code</code>. 岗位编码 
     */
    public void setCode(String value);

    /**
     * Getter for <code>course-study.t_position.f_code</code>. 岗位编码 
     */
    public String getCode();

    /**
     * Setter for <code>course-study.t_position.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>course-study.t_position.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>course-study.t_position.f_status</code>. 岗位状态 1:活动  2：禁用
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_position.f_status</code>. 岗位状态 1:活动  2：禁用
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_position.f_numbers</code>. 岗位人数
     */
    public void setNumbers(Integer value);

    /**
     * Getter for <code>course-study.t_position.f_numbers</code>. 岗位人数
     */
    public Integer getNumbers();

    /**
     * Setter for <code>course-study.t_position.f_job_id</code>. 职务ID
     */
    public void setJobId(String value);

    /**
     * Getter for <code>course-study.t_position.f_job_id</code>. 职务ID
     */
    public String getJobId();

    /**
     * Setter for <code>course-study.t_position.f_instruction_id</code>. 岗位说明书id(附件id)
     */
    public void setInstructionId(String value);

    /**
     * Getter for <code>course-study.t_position.f_instruction_id</code>. 岗位说明书id(附件id)
     */
    public String getInstructionId();

    /**
     * Setter for <code>course-study.t_position.f_desc</code>. 专业能力标准/资质模型
     */
    public void setDesc(String value);

    /**
     * Getter for <code>course-study.t_position.f_desc</code>. 专业能力标准/资质模型
     */
    public String getDesc();

    /**
     * Setter for <code>course-study.t_position.f_root_name</code>. 主族
     */
    public void setRootName(String value);

    /**
     * Getter for <code>course-study.t_position.f_root_name</code>. 主族
     */
    public String getRootName();

    /**
     * Setter for <code>course-study.t_position.f_sub_name</code>. 子族
     */
    public void setSubName(String value);

    /**
     * Getter for <code>course-study.t_position.f_sub_name</code>. 子族
     */
    public String getSubName();

    /**
     * Setter for <code>course-study.t_position.f_alias_id</code>. 条线(t_member_config)
     */
    public void setAliasId(String value);

    /**
     * Getter for <code>course-study.t_position.f_alias_id</code>. 条线(t_member_config)
     */
    public String getAliasId();

    /**
     * Setter for <code>course-study.t_position.f_level_id</code>. 职级(t_member_config)
     */
    public void setLevelId(String value);

    /**
     * Getter for <code>course-study.t_position.f_level_id</code>. 职级(t_member_config)
     */
    public String getLevelId();

    /**
     * Setter for <code>course-study.t_position.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_position.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_position.f_mis_code</code>. MIS省份简称
     */
    public void setMisCode(String value);

    /**
     * Getter for <code>course-study.t_position.f_mis_code</code>. MIS省份简称
     */
    public String getMisCode();

    /**
     * Setter for <code>course-study.t_position.f_mis_id</code>. MIS同步岗位ID
     */
    public void setMisId(String value);

    /**
     * Getter for <code>course-study.t_position.f_mis_id</code>. MIS同步岗位ID
     */
    public String getMisId();

    /**
     * Setter for <code>course-study.t_position.f_type</code>. 岗位类型 1细分岗位，2自设岗位，3集团标准岗位 4其他
     */
    public void setType(String value);

    /**
     * Getter for <code>course-study.t_position.f_type</code>. 岗位类型 1细分岗位，2自设岗位，3集团标准岗位 4其他
     */
    public String getType();

    /**
     * Setter for <code>course-study.t_position.f_parent_id</code>. 集团标准岗位编码
     */
    public void setParentId(String value);

    /**
     * Getter for <code>course-study.t_position.f_parent_id</code>. 集团标准岗位编码
     */
    public String getParentId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPosition
     */
    public void from(IPosition from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPosition
     */
    public <E extends IPosition> E into(E into);
}
