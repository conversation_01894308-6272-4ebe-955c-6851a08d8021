/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.SubAuthenticatedCourseProgress;
import com.zxy.product.course.jooq.tables.interfaces.ISubAuthenticatedCourseProgress;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 子认证-学员学习进度表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedCourseProgressRecord extends UpdatableRecordImpl<SubAuthenticatedCourseProgressRecord> implements Record7<String, Long, String, String, Integer, Integer, Long>, ISubAuthenticatedCourseProgress {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_id</code>. 进度表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_id</code>. 进度表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public void setSubAuthenticatedId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public String getSubAuthenticatedId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_member_id</code>. 学员id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_member_id</code>. 学员id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_finish_status</code>. 学习状态，所有学习组的状态是已完成，此字段才是已完成，0-未开始，1-学习中，2-已完成
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_finish_status</code>. 学习状态，所有学习组的状态是已完成，此字段才是已完成，0-未开始，1-学习中，2-已完成
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_study_total_time</code>. 学习组学习总时长，单位秒
     */
    @Override
    public void setStudyTotalTime(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_study_total_time</code>. 学习组学习总时长，单位秒
     */
    @Override
    public Integer getStudyTotalTime() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_course_progress.f_register_time</code>. 注册时间
     */
    @Override
    public void setRegisterTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_course_progress.f_register_time</code>. 注册时间
     */
    @Override
    public Long getRegisterTime() {
        return (Long) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, Long, String, String, Integer, Integer, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, Long, String, String, Integer, Integer, Long> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.SUB_AUTHENTICATED_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.FINISH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.STUDY_TOTAL_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.REGISTER_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSubAuthenticatedId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getFinishStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getStudyTotalTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getRegisterTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value3(String value) {
        setSubAuthenticatedId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value5(Integer value) {
        setFinishStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value6(Integer value) {
        setStudyTotalTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord value7(Long value) {
        setRegisterTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCourseProgressRecord values(String value1, Long value2, String value3, String value4, Integer value5, Integer value6, Long value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubAuthenticatedCourseProgress from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setSubAuthenticatedId(from.getSubAuthenticatedId());
        setMemberId(from.getMemberId());
        setFinishStatus(from.getFinishStatus());
        setStudyTotalTime(from.getStudyTotalTime());
        setRegisterTime(from.getRegisterTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubAuthenticatedCourseProgress> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SubAuthenticatedCourseProgressRecord
     */
    public SubAuthenticatedCourseProgressRecord() {
        super(SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS);
    }

    /**
     * Create a detached, initialised SubAuthenticatedCourseProgressRecord
     */
    public SubAuthenticatedCourseProgressRecord(String id, Long createTime, String subAuthenticatedId, String memberId, Integer finishStatus, Integer studyTotalTime, Long registerTime) {
        super(SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS);

        set(0, id);
        set(1, createTime);
        set(2, subAuthenticatedId);
        set(3, memberId);
        set(4, finishStatus);
        set(5, studyTotalTime);
        set(6, registerTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCourseProgressEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCourseProgressEntity pojo = (com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCourseProgressEntity)source;
        pojo.into(this);
        return true;
    }
}
