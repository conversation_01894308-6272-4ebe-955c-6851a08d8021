/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.AnnualBillAskbarcommentRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnnualBillAskbarcomment extends TableImpl<AnnualBillAskbarcommentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_annual_bill_askbarcomment</code>
     */
    public static final AnnualBillAskbarcomment ANNUAL_BILL_ASKBARCOMMENT = new AnnualBillAskbarcomment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnnualBillAskbarcommentRecord> getRecordType() {
        return AnnualBillAskbarcommentRecord.class;
    }

    /**
     * The column <code>course-study.t_annual_bill_askbarcomment.f_id</code>.
     */
    public final TableField<AnnualBillAskbarcommentRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_annual_bill_askbarcomment.f_member_id</code>.
     */
    public final TableField<AnnualBillAskbarcommentRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_annual_bill_askbarcomment.f_askbar_comment</code>. 问吧参与话题讨论和回复的数量
     */
    public final TableField<AnnualBillAskbarcommentRecord, Integer> ASKBAR_COMMENT = createField("f_askbar_comment", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "问吧参与话题讨论和回复的数量");

    /**
     * Create a <code>course-study.t_annual_bill_askbarcomment</code> table reference
     */
    public AnnualBillAskbarcomment() {
        this("t_annual_bill_askbarcomment", null);
    }

    /**
     * Create an aliased <code>course-study.t_annual_bill_askbarcomment</code> table reference
     */
    public AnnualBillAskbarcomment(String alias) {
        this(alias, ANNUAL_BILL_ASKBARCOMMENT);
    }

    private AnnualBillAskbarcomment(String alias, Table<AnnualBillAskbarcommentRecord> aliased) {
        this(alias, aliased, null);
    }

    private AnnualBillAskbarcomment(String alias, Table<AnnualBillAskbarcommentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnnualBillAskbarcommentRecord> getPrimaryKey() {
        return Keys.KEY_T_ANNUAL_BILL_ASKBARCOMMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnnualBillAskbarcommentRecord>> getKeys() {
        return Arrays.<UniqueKey<AnnualBillAskbarcommentRecord>>asList(Keys.KEY_T_ANNUAL_BILL_ASKBARCOMMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnnualBillAskbarcomment as(String alias) {
        return new AnnualBillAskbarcomment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnnualBillAskbarcomment rename(String name) {
        return new AnnualBillAskbarcomment(name, null);
    }
}
