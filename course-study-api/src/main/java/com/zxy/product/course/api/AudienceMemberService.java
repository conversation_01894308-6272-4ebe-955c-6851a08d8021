package com.zxy.product.course.api;

import java.util.List;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.AudienceMember;

@RemoteService(timeout = 100000)
public interface AudienceMemberService {

    @Transactional
    List<String> deleteByItemIds(List<String> ids);

    @Transactional
    List<AudienceMember> insertList(List<AudienceMember> audienceMember);

    @Transactional
    void deleteByMemberId(String memberId);

    /**
     * 删除某个 Item 类型下的 受众人
     *
     * @param memberId
     * @param itemId
     */
    @Transactional
    void deleteByMemberIdAndItemId(String memberId, String itemId);

    /**
     * 删除多个个 Item 类型下的 受众人
     *
     * @param memberId
     * @param itemIds
     */
    @Transactional
    void deleteByMemberIdAndItemIds(String memberId, List<String> itemIds);

    /**
     * 批量删除受众人
     *
     * @param memberIds
     */
    @Transactional
    void deleteByMemberIds(String[] memberIds);

    /**
     * 批量删除受众人
     *
     * @param memberIds
     *            受众成员ids
     * @param itemIds
     *            受众项ids
     */
    @Transactional
    void deleteByMemberIdsAndItemIds(List<String> memberIds, List<String> itemIds);
    /**
     * 通过业务id和业务类型获取该业务下的受众人列表
     * @param businessId
     * @param businessType
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<AudienceMember> findByBusinessIdIdType(String businessId, int businessType);

    /**
     * 通过用户查询指定类型的所有受众
     * @param memberId
     * @param businessType
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findBussinessIdByMemberId(String memberId, int businessType);

}
