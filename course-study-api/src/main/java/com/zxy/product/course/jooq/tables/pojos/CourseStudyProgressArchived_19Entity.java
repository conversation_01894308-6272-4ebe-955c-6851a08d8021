/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressArchived_19;

import javax.annotation.Generated;


/**
 * course_study_progress已归档数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressArchived_19Entity extends BaseEntity implements ICourseStudyProgressArchived_19 {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  courseId;
    private Long    beginTime;
    private Integer finishStatus;

    public CourseStudyProgressArchived_19Entity() {}

    public CourseStudyProgressArchived_19Entity(CourseStudyProgressArchived_19Entity value) {
        this.memberId = value.memberId;
        this.courseId = value.courseId;
        this.beginTime = value.beginTime;
        this.finishStatus = value.finishStatus;
    }

    public CourseStudyProgressArchived_19Entity(
        String  id,
        String  memberId,
        String  courseId,
        Long    beginTime,
        Integer finishStatus
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.courseId = courseId;
        this.beginTime = beginTime;
        this.finishStatus = finishStatus;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public Long getBeginTime() {
        return this.beginTime;
    }

    @Override
    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseStudyProgressArchived_19Entity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(courseId);
        sb.append(", ").append(beginTime);
        sb.append(", ").append(finishStatus);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProgressArchived_19 from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setBeginTime(from.getBeginTime());
        setFinishStatus(from.getFinishStatus());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProgressArchived_19> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseStudyProgressArchived_19Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseStudyProgressArchived_19Record r = new com.zxy.product.course.jooq.tables.records.CourseStudyProgressArchived_19Record();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.BEGIN_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.BEGIN_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.BEGIN_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.FINISH_STATUS));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
