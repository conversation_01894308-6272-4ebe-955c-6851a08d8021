/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDbaAnalyzeTableIndex extends Serializable {

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.tid</code>.
     */
    public void setTid(Long value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.tid</code>.
     */
    public Long getTid();

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.table_schema</code>.
     */
    public void setTableSchema(String value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.table_schema</code>.
     */
    public String getTableSchema();

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.table_name</code>.
     */
    public void setTableName(String value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.table_name</code>.
     */
    public String getTableName();

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.index_name</code>.
     */
    public void setIndexName(String value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.index_name</code>.
     */
    public String getIndexName();

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.column_names</code>.
     */
    public void setColumnNames(String value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.column_names</code>.
     */
    public String getColumnNames();

    /**
     * Setter for <code>course-study.t_dba_analyze_table_index.table_length</code>.
     */
    public void setTableLength(String value);

    /**
     * Getter for <code>course-study.t_dba_analyze_table_index.table_length</code>.
     */
    public String getTableLength();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDbaAnalyzeTableIndex
     */
    public void from(IDbaAnalyzeTableIndex from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDbaAnalyzeTableIndex
     */
    public <E extends IDbaAnalyzeTableIndex> E into(E into);
}
