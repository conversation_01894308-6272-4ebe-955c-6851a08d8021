/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseChapterQuestionnaireRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 课程章节-满意度问卷模板关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseChapterQuestionnaire extends TableImpl<CourseChapterQuestionnaireRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_chapter_questionnaire</code>
     */
    public static final CourseChapterQuestionnaire COURSE_CHAPTER_QUESTIONNAIRE = new CourseChapterQuestionnaire();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseChapterQuestionnaireRecord> getRecordType() {
        return CourseChapterQuestionnaireRecord.class;
    }

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_id</code>. 主键ID
     */
    public final TableField<CourseChapterQuestionnaireRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键ID");

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_reference_id</code>. 线上课是章节资源id 线下课为线下课问卷id
     */
    public final TableField<CourseChapterQuestionnaireRecord, String> REFERENCE_ID = createField("f_reference_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "线上课是章节资源id 线下课为线下课问卷id");

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_type</code>. 课程分类 1-线上课 2-线下课
     */
    public final TableField<CourseChapterQuestionnaireRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "课程分类 1-线上课 2-线下课");

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_mould_id</code>. 模板ID
     */
    public final TableField<CourseChapterQuestionnaireRecord, String> MOULD_ID = createField("f_mould_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "模板ID");

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_status</code>. 状态 0-失效 1-有效
     */
    public final TableField<CourseChapterQuestionnaireRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "状态 0-失效 1-有效");

    /**
     * The column <code>course-study.t_course_chapter_questionnaire.f_create_time</code>. 创建时间
     */
    public final TableField<CourseChapterQuestionnaireRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>course-study.t_course_chapter_questionnaire</code> table reference
     */
    public CourseChapterQuestionnaire() {
        this("t_course_chapter_questionnaire", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_chapter_questionnaire</code> table reference
     */
    public CourseChapterQuestionnaire(String alias) {
        this(alias, COURSE_CHAPTER_QUESTIONNAIRE);
    }

    private CourseChapterQuestionnaire(String alias, Table<CourseChapterQuestionnaireRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseChapterQuestionnaire(String alias, Table<CourseChapterQuestionnaireRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "课程章节-满意度问卷模板关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseChapterQuestionnaireRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_CHAPTER_QUESTIONNAIRE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseChapterQuestionnaireRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseChapterQuestionnaireRecord>>asList(Keys.KEY_T_COURSE_CHAPTER_QUESTIONNAIRE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseChapterQuestionnaire as(String alias) {
        return new CourseChapterQuestionnaire(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseChapterQuestionnaire rename(String name) {
        return new CourseChapterQuestionnaire(name, null);
    }
}
