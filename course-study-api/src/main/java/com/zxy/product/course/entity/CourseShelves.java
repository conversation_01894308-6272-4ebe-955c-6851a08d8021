package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseShelvesEntity;

/**
 * Created by keeley on 16/11/17.
 */
public class CourseShelves  extends CourseShelvesEntity{

    /**
     *
     */
    private static final long serialVersionUID = 192842671587871593L;

    /**
     * 是否通知  -- 是
     */
    public static final int NOTICE_YES = 1;
    /**
     * 是否通知  -- 否
     */
    public static final int NOTICE_NO = 0;

    /**
     * 状态 --有效
     */
    public static final int STATUS_OK = 1;
    /**
     * 状态 --无效
     */
    public static final int STATUS_FAILURE = 0;

    /**
     * 上架规则 -- 不影响
     */
    public static final int RULE_NOT_AFFECT = 0;

    /**
     * 上架规则 -- 影响
     */
    public static final int RULE_AFFECT = 1;

    /**
     * 是否首次发布(测试中、第一次正式发布为首次) -- 是
     */
    public static final int IS_FIRST_YES = 0;

    /**
     * 是否首次发布(测试中、第一次正式发布为首次) -- 否
     */
    public static final int IS_FIRST_NO = 1;
}
