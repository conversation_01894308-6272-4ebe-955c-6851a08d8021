package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.CourseSectionStudyLogAhDay;
import com.zxy.product.course.entity.Topic;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService
public interface StudyPortraitPositionAtlasService {

    /**
     *  岗位学时画像
     * @param day    多少天 ，默认为30天
     * @param pageSize    多少条
     * @param currentUserId   用户id
     * @param memberList    查询人员ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Map<String, Object>>  findRoleSubject(Optional<String> day, Optional<Integer> pageSize, String currentUserId, List<String> positionTagsList, Map<String, List<String>> sons, List<String> memberList);

    /**
     *  职位知识图谱
     * @param currentUserId   用户id
     * @param PositionTagsList   查询标签id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseSectionStudyLogAhDay> findPositionKnowledgeTopic(Optional<Integer> pageSize, String currentUserId, List<String> PositionTagsList);


    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findInTopic(List<String> topicIds);
}
