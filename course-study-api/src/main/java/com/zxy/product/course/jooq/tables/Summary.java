/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SummaryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Summary extends TableImpl<SummaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_summary</code>
     */
    public static final Summary SUMMARY = new Summary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SummaryRecord> getRecordType() {
        return SummaryRecord.class;
    }

    /**
     * The column <code>course-study.t_summary.f_member_id</code>. 人员ID
     */
    public final TableField<SummaryRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员ID");

    /**
     * The column <code>course-study.t_summary.f_company_id</code>. 机构ID
     */
    public final TableField<SummaryRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "机构ID");

    /**
     * The column <code>course-study.t_summary.f_study_time</code>. 学习时长
     */
    public final TableField<SummaryRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习时长");

    /**
     * Create a <code>course-study.t_summary</code> table reference
     */
    public Summary() {
        this("t_summary", null);
    }

    /**
     * Create an aliased <code>course-study.t_summary</code> table reference
     */
    public Summary(String alias) {
        this(alias, SUMMARY);
    }

    private Summary(String alias, Table<SummaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private Summary(String alias, Table<SummaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SummaryRecord> getPrimaryKey() {
        return Keys.KEY_T_SUMMARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SummaryRecord>> getKeys() {
        return Arrays.<UniqueKey<SummaryRecord>>asList(Keys.KEY_T_SUMMARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Summary as(String alias) {
        return new Summary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Summary rename(String name) {
        return new Summary(name, null);
    }
}
