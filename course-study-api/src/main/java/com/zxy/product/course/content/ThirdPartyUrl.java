package com.zxy.product.course.content;

/**
 * 第三方接口路径配置
 */
public enum ThirdPartyUrl {

    //https://www.qingxuetang.com/open/api/corpData/getStatData
    GET_STUDY_MEMBER(1,"/open/api/corpData/getStatData"),//分批获取员工学习数据
    //https://www.qingxuetang.com/open/api/corpData/getUserLearntCourses
    GET_STUDY_MEMBER_COURSE(2,"/open/api/corpData/getUserLearntCourses"),//获取员工已学课程列表
    //https://www.qingxuetang.com/open/api/corpData/getUserLearntBooks
    GET_STUDY_MEMBER_BOOK(3,"/open/api/corpData/getUserLearntBooks");//获取员工已学书籍列表


    // 成员变量

    private int index;//

    private String url;//




    // 构造方法
    private ThirdPartyUrl(int index, String url) {
        this.index = index;
        this.url = url;
    }
    // 普通方法
    public static String getName(int index) {
        for (ThirdPartyUrl c : ThirdPartyUrl.values()) {
            if (c.getIndex() == index) {
                return c.url;
            }
        }
        return null;
    }

    // get set 方法
    public String getUrl() {
        return url;
    }
    public void setUrl(String url) {
        this.url = url;
    }
    public int getIndex() {
        return index;
    }
    public void setIndex(int index) {
        this.index = index;
    }
}
