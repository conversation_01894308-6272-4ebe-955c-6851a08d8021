/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.IshowSdkRequest;
import com.zxy.product.course.jooq.tables.interfaces.IIshowSdkRequest;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * ishow外部请求记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IshowSdkRequestRecord extends UpdatableRecordImpl<IshowSdkRequestRecord> implements Record10<String, String, String, String, String, Integer, String, Long, Timestamp, String>, IIshowSdkRequest {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_req_time</code>. 请求日期，年-月-日
     */
    @Override
    public void setReqTime(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_req_time</code>. 请求日期，年-月-日
     */
    @Override
    public String getReqTime() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_req_body</code>. 请求参数
     */
    @Override
    public void setReqBody(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_req_body</code>. 请求参数
     */
    @Override
    public String getReqBody() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_res_body</code>. 返回参数
     */
    @Override
    public void setResBody(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_res_body</code>. 返回参数
     */
    @Override
    public String getResBody() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_ext_info</code>. 请求扩展信息（处理回调）
     */
    @Override
    public void setExtInfo(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_ext_info</code>. 请求扩展信息（处理回调）
     */
    @Override
    public String getExtInfo() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_status</code>. 请求状态，1-处理中，2-已成功，3-已失败
     */
    @Override
    public void setStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_status</code>. 请求状态，1-处理中，2-已成功，3-已失败
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_sdk_client_api</code>. sdk对应api编号
     */
    @Override
    public void setSdkClientApi(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_sdk_client_api</code>. sdk对应api编号
     */
    @Override
    public String getSdkClientApi() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>course-study.t_ishow_sdk_request.f_company_id</code>. 企业id
     */
    @Override
    public void setCompanyId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_ishow_sdk_request.f_company_id</code>. 企业id
     */
    @Override
    public String getCompanyId() {
        return (String) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, String, Long, Timestamp, String> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, String, Long, Timestamp, String> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.REQ_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.REQ_BODY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.RES_BODY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.EXT_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.SDK_CLIENT_API;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field9() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return IshowSdkRequest.ISHOW_SDK_REQUEST.COMPANY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getReqTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getReqBody();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getResBody();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getExtInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getSdkClientApi();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value9() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value2(String value) {
        setReqTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value3(String value) {
        setReqBody(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value4(String value) {
        setResBody(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value5(String value) {
        setExtInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value6(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value7(String value) {
        setSdkClientApi(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value9(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord value10(String value) {
        setCompanyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IshowSdkRequestRecord values(String value1, String value2, String value3, String value4, String value5, Integer value6, String value7, Long value8, Timestamp value9, String value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IIshowSdkRequest from) {
        setId(from.getId());
        setReqTime(from.getReqTime());
        setReqBody(from.getReqBody());
        setResBody(from.getResBody());
        setExtInfo(from.getExtInfo());
        setStatus(from.getStatus());
        setSdkClientApi(from.getSdkClientApi());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IIshowSdkRequest> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IshowSdkRequestRecord
     */
    public IshowSdkRequestRecord() {
        super(IshowSdkRequest.ISHOW_SDK_REQUEST);
    }

    /**
     * Create a detached, initialised IshowSdkRequestRecord
     */
    public IshowSdkRequestRecord(String id, String reqTime, String reqBody, String resBody, String extInfo, Integer status, String sdkClientApi, Long createTime, Timestamp modifyDate, String companyId) {
        super(IshowSdkRequest.ISHOW_SDK_REQUEST);

        set(0, id);
        set(1, reqTime);
        set(2, reqBody);
        set(3, resBody);
        set(4, extInfo);
        set(5, status);
        set(6, sdkClientApi);
        set(7, createTime);
        set(8, modifyDate);
        set(9, companyId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.IshowSdkRequestEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.IshowSdkRequestEntity pojo = (com.zxy.product.course.jooq.tables.pojos.IshowSdkRequestEntity)source;
        pojo.into(this);
        return true;
    }
}
