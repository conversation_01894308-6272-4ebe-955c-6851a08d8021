/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubjectRank extends Serializable {

    /**
     * Setter for <code>course-study.t_subject_rank.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_subject_rank.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_subject_rank.f_course_id</code>. 课程/专题 id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_course_id</code>. 课程/专题 id
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_subject_rank.f_study_total_time</code>. 学习总时长
     */
    public void setStudyTotalTime(Integer value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_study_total_time</code>. 学习总时长
     */
    public Integer getStudyTotalTime();

    /**
     * Setter for <code>course-study.t_subject_rank.f_rank</code>. 排名
     */
    public void setRank(Integer value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_rank</code>. 排名
     */
    public Integer getRank();

    /**
     * Setter for <code>course-study.t_subject_rank.f_day</code>. 日期 yyyyMMdd
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_day</code>. 日期 yyyyMMdd
     */
    public Integer getDay();

    /**
     * Setter for <code>course-study.t_subject_rank.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_subject_rank.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubjectRank
     */
    public void from(ISubjectRank from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubjectRank
     */
    public <E extends ISubjectRank> E into(E into);
}
