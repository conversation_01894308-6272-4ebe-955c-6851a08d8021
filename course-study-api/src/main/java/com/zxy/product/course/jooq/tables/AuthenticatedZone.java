/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.AuthenticatedZoneRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 认证专区表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AuthenticatedZone extends TableImpl<AuthenticatedZoneRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_authenticated_zone</code>
     */
    public static final AuthenticatedZone AUTHENTICATED_ZONE = new AuthenticatedZone();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AuthenticatedZoneRecord> getRecordType() {
        return AuthenticatedZoneRecord.class;
    }

    /**
     * The column <code>course-study.t_authenticated_zone.f_id</code>. 认证id
     */
    public final TableField<AuthenticatedZoneRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "认证id");

    /**
     * The column <code>course-study.t_authenticated_zone.f_create_time</code>. 创建时间
     */
    public final TableField<AuthenticatedZoneRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_authenticated_zone.f_name</code>. 认证专区名称
     */
    public final TableField<AuthenticatedZoneRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(64).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "认证专区名称");

    /**
     * The column <code>course-study.t_authenticated_zone.f_code</code>. 认证专区编码
     */
    public final TableField<AuthenticatedZoneRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "认证专区编码");

    /**
     * The column <code>course-study.t_authenticated_zone.f_organization_id</code>. 归属部门id
     */
    public final TableField<AuthenticatedZoneRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "归属部门id");

    /**
     * The column <code>course-study.t_authenticated_zone.f_notice</code>. 通知公告
     */
    public final TableField<AuthenticatedZoneRecord, String> NOTICE = createField("f_notice", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "通知公告");

    /**
     * The column <code>course-study.t_authenticated_zone.f_banner_id</code>. banner图片地址
     */
    public final TableField<AuthenticatedZoneRecord, String> BANNER_ID = createField("f_banner_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "banner图片地址");

    /**
     * The column <code>course-study.t_authenticated_zone.f_description</code>. 认证专区描述
     */
    public final TableField<AuthenticatedZoneRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "认证专区描述");

    /**
     * The column <code>course-study.t_authenticated_zone.f_is_publish</code>. 1:发布 0：未发布
     */
    public final TableField<AuthenticatedZoneRecord, Integer> IS_PUBLISH = createField("f_is_publish", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:发布 0：未发布");

    /**
     * The column <code>course-study.t_authenticated_zone.f_publish_time</code>. 最新发布时间
     */
    public final TableField<AuthenticatedZoneRecord, Long> PUBLISH_TIME = createField("f_publish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最新发布时间");

    /**
     * The column <code>course-study.t_authenticated_zone.f_first_publish_time</code>. 首次发布时间
     */
    public final TableField<AuthenticatedZoneRecord, Long> FIRST_PUBLISH_TIME = createField("f_first_publish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "首次发布时间");

    /**
     * The column <code>course-study.t_authenticated_zone.f_cover</code>. 封面图id
     */
    public final TableField<AuthenticatedZoneRecord, String> COVER = createField("f_cover", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面图id");

    /**
     * Create a <code>course-study.t_authenticated_zone</code> table reference
     */
    public AuthenticatedZone() {
        this("t_authenticated_zone", null);
    }

    /**
     * Create an aliased <code>course-study.t_authenticated_zone</code> table reference
     */
    public AuthenticatedZone(String alias) {
        this(alias, AUTHENTICATED_ZONE);
    }

    private AuthenticatedZone(String alias, Table<AuthenticatedZoneRecord> aliased) {
        this(alias, aliased, null);
    }

    private AuthenticatedZone(String alias, Table<AuthenticatedZoneRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "认证专区表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AuthenticatedZoneRecord> getPrimaryKey() {
        return Keys.KEY_T_AUTHENTICATED_ZONE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AuthenticatedZoneRecord>> getKeys() {
        return Arrays.<UniqueKey<AuthenticatedZoneRecord>>asList(Keys.KEY_T_AUTHENTICATED_ZONE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuthenticatedZone as(String alias) {
        return new AuthenticatedZone(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AuthenticatedZone rename(String name) {
        return new AuthenticatedZone(name, null);
    }
}
