/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IStudyPushRecord;

import javax.annotation.Generated;


/**
 * 推送记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyPushRecordEntity extends BaseEntity implements IStudyPushRecord {

    private static final long serialVersionUID = 1L;

    private String  pushId;
    private String  memberId;
    private Integer pushStatus;
    private Integer taskStatus;
    private Long    pushTime;

    public StudyPushRecordEntity() {}

    public StudyPushRecordEntity(StudyPushRecordEntity value) {
        this.pushId = value.pushId;
        this.memberId = value.memberId;
        this.pushStatus = value.pushStatus;
        this.taskStatus = value.taskStatus;
        this.pushTime = value.pushTime;
    }

    public StudyPushRecordEntity(
        String  id,
        String  pushId,
        String  memberId,
        Integer pushStatus,
        Integer taskStatus,
        Long    pushTime,
        Long    createTime
    ) {
        super.setId(id);
        this.pushId = pushId;
        this.memberId = memberId;
        this.pushStatus = pushStatus;
        this.taskStatus = taskStatus;
        this.pushTime = pushTime;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getPushId() {
        return this.pushId;
    }

    @Override
    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getPushStatus() {
        return this.pushStatus;
    }

    @Override
    public void setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
    }

    @Override
    public Integer getTaskStatus() {
        return this.taskStatus;
    }

    @Override
    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    @Override
    public Long getPushTime() {
        return this.pushTime;
    }

    @Override
    public void setPushTime(Long pushTime) {
        this.pushTime = pushTime;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("StudyPushRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(pushId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(pushStatus);
        sb.append(", ").append(taskStatus);
        sb.append(", ").append(pushTime);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyPushRecord from) {
        setId(from.getId());
        setPushId(from.getPushId());
        setMemberId(from.getMemberId());
        setPushStatus(from.getPushStatus());
        setTaskStatus(from.getTaskStatus());
        setPushTime(from.getPushTime());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyPushRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends StudyPushRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.StudyPushRecordRecord r = new com.zxy.product.course.jooq.tables.records.StudyPushRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.ID, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_ID, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.TASK_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.TASK_STATUS, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.TASK_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_TIME, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.PUSH_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.StudyPushRecord.STUDY_PUSH_RECORD.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
