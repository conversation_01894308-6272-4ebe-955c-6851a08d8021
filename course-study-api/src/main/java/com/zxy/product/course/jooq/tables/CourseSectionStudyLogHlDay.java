/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogHlDayRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogHlDay extends TableImpl<CourseSectionStudyLogHlDayRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_section_study_log_hl_day</code>
     */
    public static final CourseSectionStudyLogHlDay COURSE_SECTION_STUDY_LOG_HL_DAY = new CourseSectionStudyLogHlDay();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSectionStudyLogHlDayRecord> getRecordType() {
        return CourseSectionStudyLogHlDayRecord.class;
    }

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_id</code>. ID
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_member_id</code>. 用户id
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户id");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_course_id</code>. 课程/专题id
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程/专题id");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_app_study_time</code>. app课程学习时长
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> APP_STUDY_TIME = createField("f_app_study_time", org.jooq.impl.SQLDataType.INTEGER, this, "app课程学习时长");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_pc_study_time</code>. pc课程学习时长
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> PC_STUDY_TIME = createField("f_pc_study_time", org.jooq.impl.SQLDataType.INTEGER, this, "pc课程学习时长");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_study_time</code>. 当天学习总时长
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER, this, "当天学习总时长");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_day</code>. 日
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "日");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_month</code>. 月
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_year</code>. 年
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "年");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_create_time</code>. 创建时间
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_modify_date</code>. 修改时间
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>course-study.t_course_section_study_log_hl_day.f_study_num</code>. 学习次数
     */
    public final TableField<CourseSectionStudyLogHlDayRecord, Integer> STUDY_NUM = createField("f_study_num", org.jooq.impl.SQLDataType.INTEGER, this, "学习次数");

    /**
     * Create a <code>course-study.t_course_section_study_log_hl_day</code> table reference
     */
    public CourseSectionStudyLogHlDay() {
        this("t_course_section_study_log_hl_day", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_section_study_log_hl_day</code> table reference
     */
    public CourseSectionStudyLogHlDay(String alias) {
        this(alias, COURSE_SECTION_STUDY_LOG_HL_DAY);
    }

    private CourseSectionStudyLogHlDay(String alias, Table<CourseSectionStudyLogHlDayRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSectionStudyLogHlDay(String alias, Table<CourseSectionStudyLogHlDayRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSectionStudyLogHlDayRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SECTION_STUDY_LOG_HL_DAY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSectionStudyLogHlDayRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSectionStudyLogHlDayRecord>>asList(Keys.KEY_T_COURSE_SECTION_STUDY_LOG_HL_DAY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogHlDay as(String alias) {
        return new CourseSectionStudyLogHlDay(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSectionStudyLogHlDay rename(String name) {
        return new CourseSectionStudyLogHlDay(name, null);
    }
}
