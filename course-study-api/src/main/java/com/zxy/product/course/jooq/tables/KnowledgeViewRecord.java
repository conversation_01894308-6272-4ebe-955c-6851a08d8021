/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.KnowledgeViewRecordRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 知识查看记录，用于统计浏览人数
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class KnowledgeViewRecord extends TableImpl<KnowledgeViewRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_knowledge_view_record</code>
     */
    public static final KnowledgeViewRecord KNOWLEDGE_VIEW_RECORD = new KnowledgeViewRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<KnowledgeViewRecordRecord> getRecordType() {
        return KnowledgeViewRecordRecord.class;
    }

    /**
     * The column <code>course-study.t_knowledge_view_record.f_id</code>.
     */
    public final TableField<KnowledgeViewRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_knowledge_view_record.f_type</code>. 知识类型 0-视频 1-音频 2-文档 3-电子书
     */
    public final TableField<KnowledgeViewRecordRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "知识类型 0-视频 1-音频 2-文档 3-电子书");

    /**
     * The column <code>course-study.t_knowledge_view_record.f_knowledge_id</code>. 知识id
     */
    public final TableField<KnowledgeViewRecordRecord, String> KNOWLEDGE_ID = createField("f_knowledge_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "知识id");

    /**
     * The column <code>course-study.t_knowledge_view_record.f_create_time</code>. 创建时间
     */
    public final TableField<KnowledgeViewRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_knowledge_view_record.f_create_member_id</code>. 创建人
     */
    public final TableField<KnowledgeViewRecordRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>course-study.t_knowledge_view_record.f_modify_date</code>. 修改时间
     */
    public final TableField<KnowledgeViewRecordRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>course-study.t_knowledge_view_record</code> table reference
     */
    public KnowledgeViewRecord() {
        this("t_knowledge_view_record", null);
    }

    /**
     * Create an aliased <code>course-study.t_knowledge_view_record</code> table reference
     */
    public KnowledgeViewRecord(String alias) {
        this(alias, KNOWLEDGE_VIEW_RECORD);
    }

    private KnowledgeViewRecord(String alias, Table<KnowledgeViewRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private KnowledgeViewRecord(String alias, Table<KnowledgeViewRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "知识查看记录，用于统计浏览人数");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<KnowledgeViewRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_KNOWLEDGE_VIEW_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<KnowledgeViewRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<KnowledgeViewRecordRecord>>asList(Keys.KEY_T_KNOWLEDGE_VIEW_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public KnowledgeViewRecord as(String alias) {
        return new KnowledgeViewRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public KnowledgeViewRecord rename(String name) {
        return new KnowledgeViewRecord(name, null);
    }
}
