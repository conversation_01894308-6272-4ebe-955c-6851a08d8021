/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.StudyActivityConfigRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习活动配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyActivityConfig extends TableImpl<StudyActivityConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_study_activity_config</code>
     */
    public static final StudyActivityConfig STUDY_ACTIVITY_CONFIG = new StudyActivityConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyActivityConfigRecord> getRecordType() {
        return StudyActivityConfigRecord.class;
    }

    /**
     * The column <code>course-study.t_study_activity_config.f_id</code>.
     */
    public final TableField<StudyActivityConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_study_activity_config.f_title</code>. 活动标题
     */
    public final TableField<StudyActivityConfigRecord, String> TITLE = createField("f_title", org.jooq.impl.SQLDataType.VARCHAR.length(256).nullable(false), this, "活动标题");

    /**
     * The column <code>course-study.t_study_activity_config.f_type</code>. 活动类型,1-课程，2-专题
     */
    public final TableField<StudyActivityConfigRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "活动类型,1-课程，2-专题");

    /**
     * The column <code>course-study.t_study_activity_config.f_course_name</code>. 课程/专题名称
     */
    public final TableField<StudyActivityConfigRecord, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(256).nullable(false), this, "课程/专题名称");

    /**
     * The column <code>course-study.t_study_activity_config.f_course_id</code>. 课程|专题ID
     */
    public final TableField<StudyActivityConfigRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "课程|专题ID");

    /**
     * The column <code>course-study.t_study_activity_config.f_status</code>. 状态:0-未发布,1-已发布
     */
    public final TableField<StudyActivityConfigRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "状态:0-未发布,1-已发布");

    /**
     * The column <code>course-study.t_study_activity_config.f_cache_key</code>. 数据缓存键
     */
    public final TableField<StudyActivityConfigRecord, String> CACHE_KEY = createField("f_cache_key", org.jooq.impl.SQLDataType.VARCHAR.length(512).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "数据缓存键");

    /**
     * The column <code>course-study.t_study_activity_config.f_sequence</code>. 序列号
     */
    public final TableField<StudyActivityConfigRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "序列号");

    /**
     * The column <code>course-study.t_study_activity_config.f_create_time</code>. 创建时间
     */
    public final TableField<StudyActivityConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>course-study.t_study_activity_config</code> table reference
     */
    public StudyActivityConfig() {
        this("t_study_activity_config", null);
    }

    /**
     * Create an aliased <code>course-study.t_study_activity_config</code> table reference
     */
    public StudyActivityConfig(String alias) {
        this(alias, STUDY_ACTIVITY_CONFIG);
    }

    private StudyActivityConfig(String alias, Table<StudyActivityConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyActivityConfig(String alias, Table<StudyActivityConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习活动配置表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyActivityConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_ACTIVITY_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyActivityConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyActivityConfigRecord>>asList(Keys.KEY_T_STUDY_ACTIVITY_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyActivityConfig as(String alias) {
        return new StudyActivityConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyActivityConfig rename(String name) {
        return new StudyActivityConfig(name, null);
    }
}
