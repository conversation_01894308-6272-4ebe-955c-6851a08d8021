/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IPersonYearBill;

import javax.annotation.Generated;


/**
 * 个人年度学习账单
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PersonYearBillEntity extends BaseEntity implements IPersonYearBill {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private Integer studyTotalTime;
    private String  companyId;
    private Integer sortOfCompany;
    private Integer companyMemberNum;
    private Integer finishedCourseNum;
    private Integer pcStudyTime;
    private Integer appStudyTime;
    private String  favoriteCourse;
    private String  favoriteCourseCategory;
    private String  favoriteSubjectId;
    private Integer studyMaxDay;
    private Integer studyMaxTime;
    private Integer loginMaxDay;
    private Integer loginMaxTimes;
    private Integer year;

    public PersonYearBillEntity() {}

    public PersonYearBillEntity(PersonYearBillEntity value) {
        this.memberId = value.memberId;
        this.studyTotalTime = value.studyTotalTime;
        this.companyId = value.companyId;
        this.sortOfCompany = value.sortOfCompany;
        this.companyMemberNum = value.companyMemberNum;
        this.finishedCourseNum = value.finishedCourseNum;
        this.pcStudyTime = value.pcStudyTime;
        this.appStudyTime = value.appStudyTime;
        this.favoriteCourse = value.favoriteCourse;
        this.favoriteCourseCategory = value.favoriteCourseCategory;
        this.favoriteSubjectId = value.favoriteSubjectId;
        this.studyMaxDay = value.studyMaxDay;
        this.studyMaxTime = value.studyMaxTime;
        this.loginMaxDay = value.loginMaxDay;
        this.loginMaxTimes = value.loginMaxTimes;
        this.year = value.year;
    }

    public PersonYearBillEntity(
        String  id,
        String  memberId,
        Integer studyTotalTime,
        String  companyId,
        Integer sortOfCompany,
        Integer companyMemberNum,
        Integer finishedCourseNum,
        Integer pcStudyTime,
        Integer appStudyTime,
        String  favoriteCourse,
        String  favoriteCourseCategory,
        String  favoriteSubjectId,
        Integer studyMaxDay,
        Integer studyMaxTime,
        Integer loginMaxDay,
        Integer loginMaxTimes,
        Integer year,
        Long    createTime
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.studyTotalTime = studyTotalTime;
        this.companyId = companyId;
        this.sortOfCompany = sortOfCompany;
        this.companyMemberNum = companyMemberNum;
        this.finishedCourseNum = finishedCourseNum;
        this.pcStudyTime = pcStudyTime;
        this.appStudyTime = appStudyTime;
        this.favoriteCourse = favoriteCourse;
        this.favoriteCourseCategory = favoriteCourseCategory;
        this.favoriteSubjectId = favoriteSubjectId;
        this.studyMaxDay = studyMaxDay;
        this.studyMaxTime = studyMaxTime;
        this.loginMaxDay = loginMaxDay;
        this.loginMaxTimes = loginMaxTimes;
        this.year = year;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getStudyTotalTime() {
        return this.studyTotalTime;
    }

    @Override
    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    @Override
    public String getCompanyId() {
        return this.companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    @Override
    public Integer getSortOfCompany() {
        return this.sortOfCompany;
    }

    @Override
    public void setSortOfCompany(Integer sortOfCompany) {
        this.sortOfCompany = sortOfCompany;
    }

    @Override
    public Integer getCompanyMemberNum() {
        return this.companyMemberNum;
    }

    @Override
    public void setCompanyMemberNum(Integer companyMemberNum) {
        this.companyMemberNum = companyMemberNum;
    }

    @Override
    public Integer getFinishedCourseNum() {
        return this.finishedCourseNum;
    }

    @Override
    public void setFinishedCourseNum(Integer finishedCourseNum) {
        this.finishedCourseNum = finishedCourseNum;
    }

    @Override
    public Integer getPcStudyTime() {
        return this.pcStudyTime;
    }

    @Override
    public void setPcStudyTime(Integer pcStudyTime) {
        this.pcStudyTime = pcStudyTime;
    }

    @Override
    public Integer getAppStudyTime() {
        return this.appStudyTime;
    }

    @Override
    public void setAppStudyTime(Integer appStudyTime) {
        this.appStudyTime = appStudyTime;
    }

    @Override
    public String getFavoriteCourse() {
        return this.favoriteCourse;
    }

    @Override
    public void setFavoriteCourse(String favoriteCourse) {
        this.favoriteCourse = favoriteCourse;
    }

    @Override
    public String getFavoriteCourseCategory() {
        return this.favoriteCourseCategory;
    }

    @Override
    public void setFavoriteCourseCategory(String favoriteCourseCategory) {
        this.favoriteCourseCategory = favoriteCourseCategory;
    }

    @Override
    public String getFavoriteSubjectId() {
        return this.favoriteSubjectId;
    }

    @Override
    public void setFavoriteSubjectId(String favoriteSubjectId) {
        this.favoriteSubjectId = favoriteSubjectId;
    }

    @Override
    public Integer getStudyMaxDay() {
        return this.studyMaxDay;
    }

    @Override
    public void setStudyMaxDay(Integer studyMaxDay) {
        this.studyMaxDay = studyMaxDay;
    }

    @Override
    public Integer getStudyMaxTime() {
        return this.studyMaxTime;
    }

    @Override
    public void setStudyMaxTime(Integer studyMaxTime) {
        this.studyMaxTime = studyMaxTime;
    }

    @Override
    public Integer getLoginMaxDay() {
        return this.loginMaxDay;
    }

    @Override
    public void setLoginMaxDay(Integer loginMaxDay) {
        this.loginMaxDay = loginMaxDay;
    }

    @Override
    public Integer getLoginMaxTimes() {
        return this.loginMaxTimes;
    }

    @Override
    public void setLoginMaxTimes(Integer loginMaxTimes) {
        this.loginMaxTimes = loginMaxTimes;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PersonYearBillEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(studyTotalTime);
        sb.append(", ").append(companyId);
        sb.append(", ").append(sortOfCompany);
        sb.append(", ").append(companyMemberNum);
        sb.append(", ").append(finishedCourseNum);
        sb.append(", ").append(pcStudyTime);
        sb.append(", ").append(appStudyTime);
        sb.append(", ").append(favoriteCourse);
        sb.append(", ").append(favoriteCourseCategory);
        sb.append(", ").append(favoriteSubjectId);
        sb.append(", ").append(studyMaxDay);
        sb.append(", ").append(studyMaxTime);
        sb.append(", ").append(loginMaxDay);
        sb.append(", ").append(loginMaxTimes);
        sb.append(", ").append(year);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPersonYearBill from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setStudyTotalTime(from.getStudyTotalTime());
        setCompanyId(from.getCompanyId());
        setSortOfCompany(from.getSortOfCompany());
        setCompanyMemberNum(from.getCompanyMemberNum());
        setFinishedCourseNum(from.getFinishedCourseNum());
        setPcStudyTime(from.getPcStudyTime());
        setAppStudyTime(from.getAppStudyTime());
        setFavoriteCourse(from.getFavoriteCourse());
        setFavoriteCourseCategory(from.getFavoriteCourseCategory());
        setFavoriteSubjectId(from.getFavoriteSubjectId());
        setStudyMaxDay(from.getStudyMaxDay());
        setStudyMaxTime(from.getStudyMaxTime());
        setLoginMaxDay(from.getLoginMaxDay());
        setLoginMaxTimes(from.getLoginMaxTimes());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPersonYearBill> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PersonYearBillEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.PersonYearBillRecord r = new com.zxy.product.course.jooq.tables.records.PersonYearBillRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.ID, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_TOTAL_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_TOTAL_TIME, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_TOTAL_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_ID, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.SORT_OF_COMPANY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.SORT_OF_COMPANY, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.SORT_OF_COMPANY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_MEMBER_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_MEMBER_NUM, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.COMPANY_MEMBER_NUM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FINISHED_COURSE_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FINISHED_COURSE_NUM, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FINISHED_COURSE_NUM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.PC_STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.PC_STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.PC_STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.APP_STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.APP_STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.APP_STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE_CATEGORY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE_CATEGORY, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_COURSE_CATEGORY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_SUBJECT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.FAVORITE_SUBJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_DAY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_DAY, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_DAY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_TIME, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.STUDY_MAX_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_DAY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_DAY, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_DAY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_TIMES) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_TIMES, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.LOGIN_MAX_TIMES));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.YEAR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.YEAR, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.PersonYearBill.PERSON_YEAR_BILL.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
