package com.zxy.product.course.dto.model.mentor;

import java.io.Serializable;

/**
 * 远程调用消费者：三方传输课件笔记|课程笔记|猜你想问|知识点集合相关原始报文DTO
 * <AUTHOR>
 * @date 2024年10月14日 9:24
 */
public class SyncDTO implements Serializable {
    private static final long serialVersionUID = 269595399023938322L;

    /**响应消息*/
    private String msg;

    /**响应码*/
    private Integer code;

    /**消息体数据*/
    private SyncDataDTO data;

    public String getMsg() { return msg; }

    public void setMsg(String msg) { this.msg = msg; }

    public Integer getCode() { return code; }

    public void setCode(Integer code) { this.code = code; }

    public SyncDataDTO getData() { return data; }

    public void setData(SyncDataDTO data) { this.data = data; }

    @Override
    public String toString() {
        return "SyncDTO{" +
                "msg='" + msg + '\'' +
                ", code=" + code +
                ", data=" + data +
                '}';
    }
}
