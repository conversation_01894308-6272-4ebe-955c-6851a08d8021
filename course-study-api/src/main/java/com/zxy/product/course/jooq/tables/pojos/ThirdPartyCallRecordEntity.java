/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IThirdPartyCallRecord;

import javax.annotation.Generated;


/**
 * 第三方接口调用记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ThirdPartyCallRecordEntity extends BaseEntity implements IThirdPartyCallRecord {

    private static final long serialVersionUID = 1L;

    private String  url;
    private String  param;
    private Integer result;
    private String  returnMsg;
    private Integer callType;
    private Integer callSize;
    private Long    updateTime;

    public ThirdPartyCallRecordEntity() {}

    public ThirdPartyCallRecordEntity(ThirdPartyCallRecordEntity value) {
        this.url = value.url;
        this.param = value.param;
        this.result = value.result;
        this.returnMsg = value.returnMsg;
        this.callType = value.callType;
        this.callSize = value.callSize;
        this.updateTime = value.updateTime;
    }

    public ThirdPartyCallRecordEntity(
        String  id,
        String  url,
        String  param,
        Integer result,
        String  returnMsg,
        Integer callType,
        Integer callSize,
        Long    createTime,
        Long    updateTime
    ) {
        super.setId(id);
        this.url = url;
        this.param = param;
        this.result = result;
        this.returnMsg = returnMsg;
        this.callType = callType;
        this.callSize = callSize;
        super.setCreateTime(createTime);
        this.updateTime = updateTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getUrl() {
        return this.url;
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String getParam() {
        return this.param;
    }

    @Override
    public void setParam(String param) {
        this.param = param;
    }

    @Override
    public Integer getResult() {
        return this.result;
    }

    @Override
    public void setResult(Integer result) {
        this.result = result;
    }

    @Override
    public String getReturnMsg() {
        return this.returnMsg;
    }

    @Override
    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    @Override
    public Integer getCallType() {
        return this.callType;
    }

    @Override
    public void setCallType(Integer callType) {
        this.callType = callType;
    }

    @Override
    public Integer getCallSize() {
        return this.callSize;
    }

    @Override
    public void setCallSize(Integer callSize) {
        this.callSize = callSize;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getUpdateTime() {
        return this.updateTime;
    }

    @Override
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ThirdPartyCallRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(url);
        sb.append(", ").append(param);
        sb.append(", ").append(result);
        sb.append(", ").append(returnMsg);
        sb.append(", ").append(callType);
        sb.append(", ").append(callSize);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(updateTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IThirdPartyCallRecord from) {
        setId(from.getId());
        setUrl(from.getUrl());
        setParam(from.getParam());
        setResult(from.getResult());
        setReturnMsg(from.getReturnMsg());
        setCallType(from.getCallType());
        setCallSize(from.getCallSize());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IThirdPartyCallRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ThirdPartyCallRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.ThirdPartyCallRecordRecord r = new com.zxy.product.course.jooq.tables.records.ThirdPartyCallRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.ID, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.URL) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.URL, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.URL));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.PARAM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.PARAM, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.PARAM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RESULT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RESULT, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RESULT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RETURN_MSG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RETURN_MSG, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.RETURN_MSG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_TYPE, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_SIZE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_SIZE, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CALL_SIZE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.UPDATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.UPDATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.UPDATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
