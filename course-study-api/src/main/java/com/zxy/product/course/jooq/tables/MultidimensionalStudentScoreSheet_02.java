/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.MultidimensionalStudentScoreSheet_02Record;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学员评分表2
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MultidimensionalStudentScoreSheet_02 extends TableImpl<MultidimensionalStudentScoreSheet_02Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_multidimensional_student_score_sheet_02</code>
     */
    public static final MultidimensionalStudentScoreSheet_02 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02 = new MultidimensionalStudentScoreSheet_02();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MultidimensionalStudentScoreSheet_02Record> getRecordType() {
        return MultidimensionalStudentScoreSheet_02Record.class;
    }

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_id</code>.
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_course_id</code>. 评分课程ID
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评分课程ID");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_scoring_subject_id</code>. 评分表专题关系表ID
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, String> SCORING_SUBJECT_ID = createField("f_scoring_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评分表专题关系表ID");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_first_latitude_score</code>. 第一纬度得分
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Integer> FIRST_LATITUDE_SCORE = createField("f_first_latitude_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第一纬度得分");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_second_latitude_score</code>. 第二纬度得分
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Integer> SECOND_LATITUDE_SCORE = createField("f_second_latitude_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第二纬度得分");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_third_latitude_score</code>. 第三纬度得分
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Integer> THIRD_LATITUDE_SCORE = createField("f_third_latitude_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第三纬度得分");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_fourth_latitude_score</code>. 第四纬度得分
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Integer> FOURTH_LATITUDE_SCORE = createField("f_fourth_latitude_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第四纬度得分");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_fifth_latitude_score</code>. 第五纬度得分
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Integer> FIFTH_LATITUDE_SCORE = createField("f_fifth_latitude_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第五纬度得分");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_content</code>. 评分更多交流
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "评分更多交流");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_member_id</code>. 评分学员ID/创建人ID
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评分学员ID/创建人ID");

    /**
     * The column <code>course-study.t_multidimensional_student_score_sheet_02.f_create_time</code>. 创建时间
     */
    public final TableField<MultidimensionalStudentScoreSheet_02Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>course-study.t_multidimensional_student_score_sheet_02</code> table reference
     */
    public MultidimensionalStudentScoreSheet_02() {
        this("t_multidimensional_student_score_sheet_02", null);
    }

    /**
     * Create an aliased <code>course-study.t_multidimensional_student_score_sheet_02</code> table reference
     */
    public MultidimensionalStudentScoreSheet_02(String alias) {
        this(alias, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02);
    }

    private MultidimensionalStudentScoreSheet_02(String alias, Table<MultidimensionalStudentScoreSheet_02Record> aliased) {
        this(alias, aliased, null);
    }

    private MultidimensionalStudentScoreSheet_02(String alias, Table<MultidimensionalStudentScoreSheet_02Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学员评分表2");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MultidimensionalStudentScoreSheet_02Record> getPrimaryKey() {
        return Keys.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MultidimensionalStudentScoreSheet_02Record>> getKeys() {
        return Arrays.<UniqueKey<MultidimensionalStudentScoreSheet_02Record>>asList(Keys.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultidimensionalStudentScoreSheet_02 as(String alias) {
        return new MultidimensionalStudentScoreSheet_02(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MultidimensionalStudentScoreSheet_02 rename(String name) {
        return new MultidimensionalStudentScoreSheet_02(name, null);
    }
}
