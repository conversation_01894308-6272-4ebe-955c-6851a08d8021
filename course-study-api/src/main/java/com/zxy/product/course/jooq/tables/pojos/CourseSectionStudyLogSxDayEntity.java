/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyLogSxDay;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogSxDayEntity extends BaseEntity implements ICourseSectionStudyLogSxDay {

    private static final long serialVersionUID = 1L;

    private String    memberId;
    private String    courseId;
    private Integer   appStudyTime;
    private Integer   pcStudyTime;
    private Integer   studyTime;
    private Integer   day;
    private Integer   month;
    private Integer   year;
    private Timestamp modifyDate;
    private Integer   studyNum;

    public CourseSectionStudyLogSxDayEntity() {}

    public CourseSectionStudyLogSxDayEntity(CourseSectionStudyLogSxDayEntity value) {
        this.memberId = value.memberId;
        this.courseId = value.courseId;
        this.appStudyTime = value.appStudyTime;
        this.pcStudyTime = value.pcStudyTime;
        this.studyTime = value.studyTime;
        this.day = value.day;
        this.month = value.month;
        this.year = value.year;
        this.modifyDate = value.modifyDate;
        this.studyNum = value.studyNum;
    }

    public CourseSectionStudyLogSxDayEntity(
        String    id,
        String    memberId,
        String    courseId,
        Integer   appStudyTime,
        Integer   pcStudyTime,
        Integer   studyTime,
        Integer   day,
        Integer   month,
        Integer   year,
        Long      createTime,
        Timestamp modifyDate,
        Integer   studyNum
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.courseId = courseId;
        this.appStudyTime = appStudyTime;
        this.pcStudyTime = pcStudyTime;
        this.studyTime = studyTime;
        this.day = day;
        this.month = month;
        this.year = year;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
        this.studyNum = studyNum;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public Integer getAppStudyTime() {
        return this.appStudyTime;
    }

    @Override
    public void setAppStudyTime(Integer appStudyTime) {
        this.appStudyTime = appStudyTime;
    }

    @Override
    public Integer getPcStudyTime() {
        return this.pcStudyTime;
    }

    @Override
    public void setPcStudyTime(Integer pcStudyTime) {
        this.pcStudyTime = pcStudyTime;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public Integer getStudyNum() {
        return this.studyNum;
    }

    @Override
    public void setStudyNum(Integer studyNum) {
        this.studyNum = studyNum;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseSectionStudyLogSxDayEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(courseId);
        sb.append(", ").append(appStudyTime);
        sb.append(", ").append(pcStudyTime);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(day);
        sb.append(", ").append(month);
        sb.append(", ").append(year);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(studyNum);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyLogSxDay from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setAppStudyTime(from.getAppStudyTime());
        setPcStudyTime(from.getPcStudyTime());
        setStudyTime(from.getStudyTime());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setStudyNum(from.getStudyNum());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyLogSxDay> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseSectionStudyLogSxDayEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogSxDayRecord r = new com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogSxDayRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.APP_STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.APP_STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.APP_STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.PC_STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.PC_STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.PC_STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.DAY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.DAY, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.DAY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MONTH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MONTH, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.YEAR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.YEAR, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MODIFY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_NUM, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_NUM));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
