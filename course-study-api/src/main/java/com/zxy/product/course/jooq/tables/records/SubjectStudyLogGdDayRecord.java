/*
 * This file is generated by jO<PERSON>Q.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.SubjectStudyLogGdDay;
import com.zxy.product.course.jooq.tables.interfaces.ISubjectStudyLogGdDay;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectStudyLogGdDayRecord extends UpdatableRecordImpl<SubjectStudyLogGdDayRecord> implements Record9<String, String, String, Integer, Integer, Inte<PERSON>, Inte<PERSON>, <PERSON>, Timestamp>, ISubjectStudyLogGdDay {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_subject_id</code>. 专题id
     */
    @Override
    public void setSubjectId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_subject_id</code>. 专题id
     */
    @Override
    public String getSubjectId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_study_time</code>. 当天学习总时长
     */
    @Override
    public void setStudyTime(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_study_time</code>. 当天学习总时长
     */
    @Override
    public Integer getStudyTime() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_day</code>. 日
     */
    @Override
    public void setDay(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_day</code>. 日
     */
    @Override
    public Integer getDay() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_month</code>. 月
     */
    @Override
    public void setMonth(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_month</code>. 月
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_year</code>. 年
     */
    @Override
    public void setYear(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_year</code>. 年
     */
    @Override
    public Integer getYear() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_subject_study_log_gd_day.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_subject_study_log_gd_day.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Integer, Integer, Integer, Integer, Long, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Integer, Integer, Integer, Integer, Long, Timestamp> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.SUBJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field9() {
        return SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSubjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value9() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value3(String value) {
        setSubjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value4(Integer value) {
        setStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value5(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value6(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value7(Integer value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord value9(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectStudyLogGdDayRecord values(String value1, String value2, String value3, Integer value4, Integer value5, Integer value6, Integer value7, Long value8, Timestamp value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubjectStudyLogGdDay from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setSubjectId(from.getSubjectId());
        setStudyTime(from.getStudyTime());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubjectStudyLogGdDay> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SubjectStudyLogGdDayRecord
     */
    public SubjectStudyLogGdDayRecord() {
        super(SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY);
    }

    /**
     * Create a detached, initialised SubjectStudyLogGdDayRecord
     */
    public SubjectStudyLogGdDayRecord(String id, String memberId, String subjectId, Integer studyTime, Integer day, Integer month, Integer year, Long createTime, Timestamp modifyDate) {
        super(SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY);

        set(0, id);
        set(1, memberId);
        set(2, subjectId);
        set(3, studyTime);
        set(4, day);
        set(5, month);
        set(6, year);
        set(7, createTime);
        set(8, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.SubjectStudyLogGdDayEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.SubjectStudyLogGdDayEntity pojo = (com.zxy.product.course.jooq.tables.pojos.SubjectStudyLogGdDayEntity)source;
        pojo.into(this);
        return true;
    }
}
