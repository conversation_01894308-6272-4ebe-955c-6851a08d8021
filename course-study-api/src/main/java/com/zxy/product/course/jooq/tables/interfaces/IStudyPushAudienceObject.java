/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习推送受众对象表(连接推送表和受众项表)
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyPushAudienceObject extends Serializable {

    /**
     * Setter for <code>course-study.t_study_push_audience_object.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_study_push_audience_object.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_study_push_audience_object.f_push_id</code>. 推送id
     */
    public void setPushId(String value);

    /**
     * Getter for <code>course-study.t_study_push_audience_object.f_push_id</code>. 推送id
     */
    public String getPushId();

    /**
     * Setter for <code>course-study.t_study_push_audience_object.f_item_id</code>. 受众项id
     */
    public void setItemId(String value);

    /**
     * Getter for <code>course-study.t_study_push_audience_object.f_item_id</code>. 受众项id
     */
    public String getItemId();

    /**
     * Setter for <code>course-study.t_study_push_audience_object.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_study_push_audience_object.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyPushAudienceObject
     */
    public void from(IStudyPushAudienceObject from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyPushAudienceObject
     */
    public <E extends IStudyPushAudienceObject> E into(E into);
}
