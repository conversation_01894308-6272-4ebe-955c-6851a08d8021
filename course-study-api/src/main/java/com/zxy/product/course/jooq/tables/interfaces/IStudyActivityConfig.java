/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习活动配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyActivityConfig extends Serializable {

    /**
     * Setter for <code>course-study.t_study_activity_config.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_title</code>. 活动标题
     */
    public void setTitle(String value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_title</code>. 活动标题
     */
    public String getTitle();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_type</code>. 活动类型,1-课程，2-专题
     */
    public void setType(Integer value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_type</code>. 活动类型,1-课程，2-专题
     */
    public Integer getType();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_course_name</code>. 课程/专题名称
     */
    public void setCourseName(String value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_course_name</code>. 课程/专题名称
     */
    public String getCourseName();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_course_id</code>. 课程|专题ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_course_id</code>. 课程|专题ID
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_status</code>. 状态:0-未发布,1-已发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_status</code>. 状态:0-未发布,1-已发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_cache_key</code>. 数据缓存键
     */
    public void setCacheKey(String value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_cache_key</code>. 数据缓存键
     */
    public String getCacheKey();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_sequence</code>. 序列号
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_sequence</code>. 序列号
     */
    public Integer getSequence();

    /**
     * Setter for <code>course-study.t_study_activity_config.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_study_activity_config.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyActivityConfig
     */
    public void from(IStudyActivityConfig from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyActivityConfig
     */
    public <E extends IStudyActivityConfig> E into(E into);
}
