package com.zxy.product.system.service.support.operation;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.operation.PointDailyQuestService;
import com.zxy.product.system.api.operation.PointHistoryQuestService;
import com.zxy.product.system.api.operation.PointRuleService;
import com.zxy.product.system.entity.PointDailyQuest;
import com.zxy.product.system.entity.PointDailyQuestVo;
import com.zxy.product.system.entity.PointHistoryQuest;
import com.zxy.product.system.entity.PointRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.zxy.product.system.jooq.Tables.POINT_DAILY_QUEST;
import static com.zxy.product.system.jooq.Tables.POINT_RULE;

/**
 * <AUTHOR>
 * @create 2023/12/7 18:02
 */
@Service
public class PointDailyQuestServiceSupport implements PointDailyQuestService {

    private CommonDao<PointDailyQuest> pointDailyQuestDao;

    private CommonDao<PointRule> pointRuleDao;
    private PointRuleService pointRuleService;
    private PointHistoryQuestService pointHistoryQuestService;


    @Autowired
    public void setPointDailyQuestDao(CommonDao<PointDailyQuest> pointDailyQuestDao) {
        this.pointDailyQuestDao = pointDailyQuestDao;
    }

    @Autowired
    public void setPointRuleDao(CommonDao<PointRule> pointRuleDao) {
        this.pointRuleDao = pointRuleDao;
    }

    @Autowired
    public void setPointRuleService(PointRuleService pointRuleService) {
        this.pointRuleService = pointRuleService;
    }

    @Autowired
    public void setPointHistoryQuestService(PointHistoryQuestService pointHistoryQuestService) {
        this.pointHistoryQuestService = pointHistoryQuestService;
    }


    /**
     * 校验用户是否满足加分或者减分条件
     *
     * @param memberId
     * @param ruleKey
     * @param totalNumber 当日用户的规则总值
     * @return
     */
    @Override
    public Boolean verifyAddOrSubPoint(String memberId, String ruleKey, Integer totalNumber) {

        Boolean flag = Boolean.FALSE;

        //1.校验任务类型
        if (PointDailyQuest.dailyInfoByConditionSetTotal.contains(ruleKey)||
                PointDailyQuest.dailyInfoByConditionSetOnce.contains(ruleKey)){

            //1.1任务属于-每日任务
            flag = this.verifyDailyAddOrSubPoint(memberId,ruleKey,totalNumber);


        }else if (PointHistoryQuest.pointTriggerByConditionSetTotal.contains(ruleKey)||
                PointHistoryQuest.pointTriggerByConditionSetOnce.contains(ruleKey)){

            //1.2任务属于-历史累计任务
            flag = pointHistoryQuestService.verifyHistoryAddOrSubPoint(memberId,ruleKey,totalNumber);
        }

        return flag;
    }

    /**
     * 每日任务-校验并增加扣减每日任务记录/历史任务-带ruleId
     * @param memberId
     * @param pointRuleId
     * @param rulePoint
     * @param ruleCount
     * @param ruleMaxCount
     * @param ruleType 配置参数个数
     * @param ruleKey
     * @param totalNumber
     * @return
     */
    @Override
    public Integer verifyAndAddOrSubPoint(String memberId, String pointRuleId, Integer rulePoint, Integer ruleCount, Integer ruleMaxCount, Integer ruleType, String ruleKey, Integer totalNumber) {

        //1.任务属于-每日任务
        Integer triggerCount = this.verifyDailyAndAddOrSubPoint(memberId,pointRuleId,rulePoint,ruleCount,ruleMaxCount,ruleType,ruleKey,totalNumber);

        return triggerCount;
    }

    /**
     * 获取当前用的户每日任务列表及完成进度
     *
     * @param memberId
     * @return
     */
    @Override
    public List<PointDailyQuestVo> listByMemberId(String memberId) {

        Map<String, String> pointDailyQuestMap = new HashMap<>();

        //1.以当前用户为维度查询每日任务
        pointDailyQuestDao.execute(dao -> dao
                .select(POINT_DAILY_QUEST.FINISH_COUNT,POINT_DAILY_QUEST.POINT_RULE_ID)
                .from(POINT_DAILY_QUEST)
                .where(POINT_DAILY_QUEST.MEMBER_ID.eq(memberId)))
                .fetch( r -> {

                    //查询个人每日任务封装
                    String ruleId = r.get(POINT_DAILY_QUEST.POINT_RULE_ID);
                    Integer finishCount = r.get(POINT_DAILY_QUEST.FINISH_COUNT);
                    pointDailyQuestMap.put(ruleId,finishCount.toString());
                    return null;
                });

        //2.查询固定格式的每日任务
        List<PointDailyQuestVo> reultList = pointRuleDao.execute(dao -> dao
                .select(POINT_RULE.RULE_POINT, POINT_RULE.RULE_COUNT, POINT_RULE.RULE_MAX_COUNT,POINT_RULE.RULE_SOURCE,POINT_RULE.ID)
                .from(POINT_RULE)
                .where(POINT_RULE.DAILY_QUEST_SEQ.isNotNull())
                .orderBy(POINT_RULE.DAILY_QUEST_SEQ)
                .fetch(r -> {

                    PointDailyQuestVo resultEntity = new PointDailyQuestVo();

                    Integer rulePoint = r.get(POINT_RULE.RULE_POINT);
                    Integer ruleCount = r.get(POINT_RULE.RULE_COUNT);
                    Integer ruleMaxCount = r.get(POINT_RULE.RULE_MAX_COUNT);
                    String ruleSource = r.get(POINT_RULE.RULE_SOURCE);
                    String pointRuleId = r.get(POINT_RULE.ID);
                    String finishCount = pointDailyQuestMap.get(pointRuleId);

                    //2. 积分值或者次数为空-不添加积分
                    if (rulePoint == null || ruleCount == null || ruleMaxCount == null) {

                        resultEntity.setRuleSource(ruleSource);
                        resultEntity.setRulePoint("-");
                        resultEntity.setRuleCount("-");
                        resultEntity.setRuleMaxCount("-");
                        resultEntity.setFinishCount("-");
                    } else {

                        resultEntity.setRuleSource(ruleSource);
                        resultEntity.setRulePoint(rulePoint.toString());
                        resultEntity.setRuleCount(ruleCount.toString());
                        resultEntity.setRuleMaxCount(ruleMaxCount.toString());
                        resultEntity.setFinishCount(finishCount == null ? "0" : finishCount);
                    }

                    return resultEntity;
                }));

        return reultList;
    }

    /**
     * 积分定时任务每日任务清零
     *
     * @return
     */
    @Override
    public Boolean deleteAll() {

        //清除每日任务表
        pointDailyQuestDao.delete(POINT_DAILY_QUEST.MEMBER_ID.isNotNull());
        return true;
    }


    /**
     * 校验用户每日任务是否满足加分或者减分条件
     * @param memberId
     * @param ruleKey
     * @param totalNumber
     */
    private Boolean verifyDailyAddOrSubPoint(String memberId, String ruleKey, Integer totalNumber) {

        //1.获取规则实体
        PointRule pointRuleByRuleKey = pointRuleService.getPointRuleByRuleKey(ruleKey);

        String pointRuleId = pointRuleByRuleKey.getId();
        Integer rulePoint = pointRuleByRuleKey.getRulePoint();
        Integer ruleCount = pointRuleByRuleKey.getRuleCount();
        Integer ruleMaxCount = pointRuleByRuleKey.getRuleMaxCount();
        Integer ruleType = pointRuleByRuleKey.getRuleType();


        //2. 积分值或者次数为空-不添加积分
        if (rulePoint == null || ruleCount == null){
            return false;
        }

        //3.有次数限制但是没值-不添加积分
        if (ruleType == 3 && ruleMaxCount == null){
            return false;
        }

        //4.没有阈值时可以加分,但不参与每日任务
        if (ruleType == 2){
            return true;
        }

        //5.查询每日任务对象是否存在
        Optional<PointDailyQuest> pointDailyQuest = pointDailyQuestDao.fetchOne(
                POINT_DAILY_QUEST.MEMBER_ID.eq(memberId),
                POINT_DAILY_QUEST.POINT_RULE_ID.eq(pointRuleId));

        //6.任务不存在时可以添加
        if (!pointDailyQuest.isPresent()){

            //6.1特殊触发条件校验(仅用于course_required_hour,course_optional_hour,
            // =watch_live_hour,watch_short_video_minute,cloud_study_course 校验)
            // 完成次数 不为null
            // 总次数/配置的触发次数 > 0
            if (PointDailyQuest.dailyInfoByConditionSetTotal.contains(ruleKey)){

                //每日任务总次数满足触发
                if (totalNumber != null && totalNumber/ruleCount > 0){
                    return true;
                }else {
                    return false;
                }
            }

            //每日任务次数累加满足触发
            return true;
        }

        PointDailyQuest entity = pointDailyQuest.get();

        //每日任务的值
        Integer dailyFinishCount = entity.getFinishCount();

        //7.特殊触发条件校验(仅用于course_required_hour, watch_live_hour,watch_short_video_minute,cloud_study_course 校验)
        if (PointDailyQuest.dailyInfoByConditionSetTotal.contains(ruleKey) && totalNumber != null){

            //当前任务的值
            Integer currentFinishCount = totalNumber/ruleCount;
            //当前任务的值> 每日任务的值 && 每日任务的值 <每日任务的最大次数
            if (currentFinishCount > dailyFinishCount && dailyFinishCount < ruleMaxCount){

                //7.1 每日任务总次数-任务满足
                return true;
            }else {

                //7.2 每日任务总次数-任务不满足
                return false;
            }

        }

        //8.每日最高次数校验.(当日完成次数 < 每日最大完成次数)
        if (dailyFinishCount < ruleMaxCount){
            return true;
        }

        //9.任务达到的最大阈值不进行操作
        return false;
    }


    /**
     * 每日任务校验并增加扣减每日任务记录-带ruleId
     * @param memberId
     * @param pointRuleId
     * @param rulePoint
     * @param ruleCount
     * @param ruleMaxCount
     * @param ruleType
     * @param ruleKey
     * @param totalNumber
     * @return
     */
    private Integer verifyDailyAndAddOrSubPoint(String memberId, String pointRuleId, Integer rulePoint, Integer ruleCount, Integer ruleMaxCount, Integer ruleType, String ruleKey, Integer totalNumber) {

        //1. 积分值或者次数为空-不添加积分
        if (rulePoint == null || ruleCount == null){
            return 0;
        }

        //2.有次数限制但是没值-不添加积分
        if (ruleType == 3 && ruleMaxCount == null){
            return 0;
        }

        //3.没有阈值时可以加分,但不参与每日任务
        if (ruleType == 2){
            return 1;
        }

        //4.查询每日任务对象是否存在
        Optional<PointDailyQuest> pointDailyQuest = pointDailyQuestDao.fetchOne(
                POINT_DAILY_QUEST.MEMBER_ID.eq(memberId),
                POINT_DAILY_QUEST.POINT_RULE_ID.eq(pointRuleId));

        //5.任务不存在时可以添加,此时任务均正常可用
        if (!pointDailyQuest.isPresent()){

            //新增每日任务
            PointDailyQuest insertEntity = new PointDailyQuest();
            insertEntity.forInsert();
            insertEntity.setPointRuleId(pointRuleId);
            insertEntity.setMemberId(memberId);

            //包含特殊key时, triggerNumber 初始 1
            if (PointDailyQuest.dailyInfoByConditionSetOnce.contains(ruleKey)){

                //如果规则配置一次就触发,那么本次就触发加分
                if ( ruleCount == 1 ){

                    //累计次数+1 ,完成次数+1  并 新增每日任务
                    insertEntity.setFinishCount(1);
                    insertEntity.setTriggerCount(1);

                    pointDailyQuestDao.insert(insertEntity);
                    return 1;

                }else {

                    //累计次数+1 并 新增每日任务
                    insertEntity.setFinishCount(0);
                    insertEntity.setTriggerCount(1);
                    pointDailyQuestDao.insert(insertEntity);
                    return 0;
                }

                //包含特殊key时,存在一次会触发多次加分
            }else if (PointDailyQuest.dailyInfoByConditionSetTotal.contains(ruleKey)){

                Integer currentCount = totalNumber/ruleCount;

                //特殊任务满足触发
                if (totalNumber != null && currentCount > 0){

                    //阈值校验
                    currentCount = currentCount < ruleMaxCount ? currentCount : ruleMaxCount;

                    //完成次数 并 新增每日任务
                    insertEntity.setFinishCount(currentCount);

                    pointDailyQuestDao.insert(insertEntity);

                    return currentCount;

                }else {
                    return 0;
                }

            } else {

                //完成次数+1 并 新增每日任务
                insertEntity.setFinishCount(1);

                pointDailyQuestDao.insert(insertEntity);
                return 1;
            }
        }

        PointDailyQuest entity = pointDailyQuest.get();

        //6.存在时,校验是否达到触发条件
        Integer finishCount = entity.getFinishCount();

        //包含指定任务y时, 且 完成次数 >= 0时,需要校验是否会被触发
        if (PointDailyQuest.dailyInfoByConditionSetOnce.contains(ruleKey)){

            //triggerCount +1 为当前触发次数
            Integer triggerCount = entity.getTriggerCount() +1;

            //获取当前完成次数.无需精度计算取整即可
            Integer currentFinishCount = triggerCount/ruleCount;

            //更新触发次数
            entity.setTriggerCount(triggerCount);

            //当前完成次数大于数据库完成次数且满足加分条件
            if (currentFinishCount > finishCount && finishCount < ruleMaxCount){

                //加分,且更新触发次数,完成次数
                entity.setFinishCount(entity.getFinishCount()+1);
                pointDailyQuestDao.update(entity);
                return 1;
            }else {

                //不加分,需要更新触发次数
                pointDailyQuestDao.update(entity);
                return 0;
            }

            //包含特殊key时,存在一次会触发多次加分
        }else if (PointDailyQuest.dailyInfoByConditionSetTotal.contains(ruleKey)){

            Integer currentCount = totalNumber/ruleCount;
            Integer addCount = currentCount - finishCount;

            //特殊任务满足触发
            if (totalNumber != null && addCount > 0){

                //阈值校验
                addCount = currentCount < ruleMaxCount ? addCount : ruleMaxCount - finishCount;
                Integer totalCount = finishCount + addCount;
                //完成次数 并 新增每日任务
                entity.setFinishCount(totalCount);

                pointDailyQuestDao.update(entity);

                return addCount;

            }else {
                return 0;
            }

        }else {

            //非指定任务的每日最高次数校验.(当日完成次数 < 每日最大完成次数)
            if (finishCount < ruleMaxCount){

                //修改每日任务完成次数+1
                entity.setFinishCount(entity.getFinishCount()+1);
                pointDailyQuestDao.update(entity);
                return 1;
            }
        }

        //7.任务达到的最大阈值不进行操作
        return 0;

    }


}
