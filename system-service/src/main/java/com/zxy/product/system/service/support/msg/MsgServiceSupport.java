package com.zxy.product.system.service.support.msg;

import static com.zxy.product.system.jooq.Tables.MEMBER;
import static com.zxy.product.system.jooq.Tables.MSG;
import static com.zxy.product.system.jooq.Tables.MSG_DETAIL;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.api.audience.AudienceItemService;
import com.zxy.product.system.api.audience.AudienceMemberService;
import com.zxy.product.system.api.audience.AudienceObjectService;
import com.zxy.product.system.api.msg.MsgService;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.AudienceItem;
import com.zxy.product.system.entity.AudienceObject;
import com.zxy.product.system.entity.Msg;
import com.zxy.product.system.entity.MsgDetail;

/**
 * Created by keeley on 16/12/13.
 */
@Service
public class MsgServiceSupport implements MsgService{
    private CommonDao<Msg> msgCommonDao;
    private CommonDao<MsgDetail> msgDetailDao;
    @Autowired
    public void setAudienceObjectService(AudienceObjectService audienceObjectService) {
        this.audienceObjectService = audienceObjectService;
    }
    @Autowired
    public void setAudienceItemService(AudienceItemService audienceItemService) {
        this.audienceItemService = audienceItemService;
    }

    private AudienceObjectService audienceObjectService;

    private AudienceItemService audienceItemService;

    private AudienceMemberService audienceMemberService;

    private MessageSender messageSender;

    @Autowired
    public void setMsgCommonDao(CommonDao<Msg> msgCommonDao) {
        this.msgCommonDao = msgCommonDao;
    }

    @Autowired
    public void setAudienceMemberService(AudienceMemberService audienceMemberService) {
        this.audienceMemberService = audienceMemberService;
    }

    @Autowired
    public void setMsgDetailDao(CommonDao<MsgDetail> msgDetailDao) {
        this.msgDetailDao = msgDetailDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    public Msg get(String id) {
        Msg msg =  msgCommonDao.get(id);
        List<AudienceItem> audiences = audienceItemService.findByTargetId(msg.getId());
        msg.setAudiences(audiences);
        return msg;
    }


    @Override
    public PagedResult<Msg> page(int page, int pageSize, String memberId, String organizationId,Optional<String> title,
            Optional<Integer> type, Optional<Integer> status, Optional<String> createMemberName, Optional<Long> startTime,Optional<Long> endTime) {
        Field<String> memberName = MEMBER.FULL_NAME.as("member_name");
        SelectOnConditionStep<Record> step = msgCommonDao.execute(e ->
        e.select(Fields.start()
                .add(MSG)
                .add(memberName)
                .end())
        .from(MSG))
        .leftJoin(MEMBER)
        .on(MEMBER.ID.eq(MSG.MEMBER_ID));
        List<Msg> items = new ArrayList<Msg>();
        Stream<Optional<Condition>> conditions = Stream.of(
                type.map(MSG.TYPE::eq),
                status.map(MSG.STATUS::eq),
                title.map(MSG.TITLE::contains),
                createMemberName.map(MSG.MEMBER_ID::eq),
                startTime.map(MSG.CREATE_TIME::ge),
                endTime.map(MSG.CREATE_TIME::le)
                );
        Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                .orElse(DSL.trueCondition());
        int firstResult = (page - 1) * pageSize;
        Integer count = msgCommonDao.execute(e -> e.fetchCount(step.where(c).and(MSG.ORGANIZATION_ID.eq(organizationId))));
        items = step.where(c).and(MSG.ORGANIZATION_ID.eq(organizationId))
                .orderBy(MSG.CREATE_TIME.desc()).
                limit(firstResult,pageSize).fetch(item -> {
                    Msg msg = new Msg();
                    msg.setCreateMemberName(item.getValue(memberName));
                    msg.setId(item.getValue(MSG.ID));
                    msg.setTitle(item.getValue(MSG.TITLE));
                    msg.setContent(item.getValue(MSG.CONTENT));
                    msg.setTextContent(item.getValue(MSG.TEXT_CONTENT));
                    msg.setType(item.getValue(MSG.TYPE));
                    msg.setStatus(item.getValue(MSG.STATUS));
                    msg.setCreateTime(item.getValue(MSG.CREATE_TIME));
                    msg.setOrganizationId(item.getValue(MSG.ORGANIZATION_ID));
                    msg.setMemberId(item.getValue(MSG.MEMBER_ID));
                    return msg;
                });
        return PagedResult.create(count, items);
    }

    @Override
    public PagedResult<Msg> pageFront(int pageNum, int pageSize, String memberId) {

        Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc =
                    a -> a.from(MSG).innerJoin(MSG_DETAIL).on(MSG_DETAIL.MSG_ID.eq(MSG.ID)).where(MSG_DETAIL.MEMBERID.eq(memberId));
        return msgCommonDao.execute(x -> {
            List<Msg> msgs = stepFunc.apply(x.select(Fields.start().add(MSG).end()))
                    .limit((pageNum - 1) * pageSize, pageSize).fetchInto(Msg.class);
            int count = stepFunc.apply(x.select(Fields.start().add(MSG.ID.count()).end()))
                    .fetchOne().getValue(0, Integer.class);

            return PagedResult.create(count, msgs);
        });

    }

    @Override
    public int remove(String id) {
        return msgCommonDao.delete(id);
    }

    @Override
    public Msg update(String id, Optional<String> title, Optional<String> msgContent,Optional<String> textContent,Integer status,List<AudienceItem> items) {
        Msg msg = msgCommonDao.get(id);
        title.ifPresent(msg::setTitle);
        msgContent.ifPresent(msg::setContent);
        textContent.ifPresent(msg::setTextContent);
        msg.setStatus(status);
        msgCommonDao.update(msg);
        audienceObjectService.editList(AudienceObject.MSG, msg.getId(), items);
//        this.pushMsg(msg);
        return msg;
    }


    @Override
    public Msg install(String title,Optional<String> url, String content,String textContent, Integer type, Integer status,String currentUserId,
            String organizationId, List<AudienceItem> items) {
        Msg msg = new Msg();
        msg.forInsert();
        msg.setTitle(title);
        url.ifPresent(x -> msg.setUrl(x));
        msg.setContent(content);
        msg.setTextContent(textContent);
        msg.setStatus(status);
        msg.setType(type);
        msg.setOrganizationId(organizationId);
        msg.setMemberId(currentUserId);
        msgCommonDao.insert(msg);
        audienceObjectService.insertList(AudienceObject.MSG, msg.getId(), items);
//        this.pushMsg(msg);
        return msg;
    }

    /**
     * 发布  （弃用）
     * @param msg
     */
    private void pushMsg(Msg msg){
        //如果是发布状态
        if(STATUS_PUSH == msg.getStatus()){
            List<String> memberIdList = audienceMemberService.findMemberIds(AudienceObject.MSG, msg.getId());
            if(memberIdList != null){
                List<MsgDetail> msgDetailList = new ArrayList<MsgDetail>();
                StringBuffer idsJoin=  new StringBuffer("");
                memberIdList.forEach(memberId -> {
                    MsgDetail detail = new MsgDetail();
                    detail.forInsert();
                    detail.setMsgId(msg.getId());
                    detail.setMemberid(memberId);
                    detail.setType(msg.getType());
                    detail.setStatus(PUSH_STATUS_ING);
                    detail.setUpdateTime(detail.getCreateTime());
                    msgDetailList.add(detail);
                    idsJoin.append(detail.getId()).append(",");
                });
                //保存发送详情
                msgDetailDao.insert(msgDetailList);
                if(idsJoin.length() > 0){
                    //调用MessageSendListener
                    messageSender.send(MessageTypeContent.SEND_MESSAGE_PUSH,
                            MessageHeaderContent.RECEIVERIDS,idsJoin.toString(),
                            MessageHeaderContent.SUBJECT,msg.getTitle(),
                            MessageHeaderContent.CONTENT,msg.getContent(),
                            MessageHeaderContent.TEXTCONTENT,msg.getTextContent(),
                            MessageHeaderContent.MSG_TYPE,msg.getType().toString(),
                            MessageHeaderContent.ORGANIZATION_ID,msg.getOrganizationId(),
                            MessageHeaderContent.STYLE,""
                            );
                }
            }
        }
    }

    /**
     * 重新发送或发送
     * @param id
     */
    @Override
    public Msg send(String id){
        Msg msg = msgCommonDao.get(id);
        if ( msg != null && msg.getStatus() == MsgService.STATUS_FAIL){
//            msg.setStatus(MsgService.STATUS_PUSH);
//            msgCommonDao.update(msg);

            messageSender.send(MessageTypeContent.SEND_MESSAGE_PUSH,MessageHeaderContent.ID,id);
        }
        return msg;
    }


}
