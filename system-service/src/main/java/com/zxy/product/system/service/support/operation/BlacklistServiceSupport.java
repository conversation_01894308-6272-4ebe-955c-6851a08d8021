package com.zxy.product.system.service.support.operation;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.operation.BlacklistService;
import com.zxy.product.system.entity.Blacklist;
import com.zxy.product.system.util.DesensitizationUtil;
import com.zxy.product.system.util.EncryptUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.system.jooq.Tables.*;

/**
 * 言论黑名单
 * <AUTHOR>
 */
@Service
public class BlacklistServiceSupport implements BlacklistService {

    private CommonDao<Blacklist> blacklistCommonDao;

    @Autowired
    public void setBlacklistCommonDao(CommonDao<Blacklist> blacklistCommonDao) {
        this.blacklistCommonDao = blacklistCommonDao;
    }

    @Override
    public void insert(String[] memberIds) {
        List<Blacklist> list = Arrays.stream(memberIds).map(memberId->{
            Blacklist blacklist = new Blacklist();
            blacklist.forInsert();
            blacklist.setMemberId(memberId);
            return blacklist;
        }).collect(Collectors.toList());
        blacklistCommonDao.insert(list);
    }

    @Override
    public PagedResult<Blacklist> find(Integer page, Integer pageSize, Optional<String> name, Optional<String> code, Optional<String> department) {
        SelectOnConditionStep<Record> stepSelect = blacklistCommonDao.execute(e->
                e.select(Fields.start().add(BLACKLIST).add(MEMBER.FULL_NAME).add(MEMBER.NAME).add(ORGANIZATION.NAME).end()))
                .from(BLACKLIST)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(BLACKLIST.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID));
        SelectOnConditionStep<Record> stepCount = blacklistCommonDao.execute(e->
                        e.select(Fields.start().add(BLACKLIST).end()))
                .from(BLACKLIST)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(BLACKLIST.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID));
        Stream<Optional<Condition>> conditions = Stream.of(
                name.map(MEMBER.FULL_NAME::contains),
                code.map(MEMBER.NAME::eq),
                department.map(ORGANIZATION.ID::eq)
        );
        Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                .orElse(DSL.trueCondition());
        int firstResult = (page - 1) * pageSize;
        Integer count = blacklistCommonDao.execute(e->e.fetchCount(stepCount.where(c)));
        List<Blacklist> items = new ArrayList<>();
        items = stepSelect.where(c).orderBy(BLACKLIST.CREATE_TIME.desc())
                .limit(firstResult,pageSize)
                .fetch(item->{
                    Blacklist blacklist =new Blacklist();
                    blacklist.setId(item.getValue(BLACKLIST.ID));
                    blacklist.setCreateTime(item.getValue(BLACKLIST.CREATE_TIME));
                    blacklist.setMemberId(item.getValue(BLACKLIST.MEMBER_ID));
                    blacklist.setCode(DesensitizationUtil.desensitizeEmployeeId(item.getValue(MEMBER.NAME)));
                    blacklist.setDepartment(item.getValue(ORGANIZATION.NAME));
                    blacklist.setName(EncryptUtil.aesEncrypt(item.getValue(MEMBER.FULL_NAME), null));
                    return blacklist;
                });
        return PagedResult.create(count, items);
    }

    public List<String> findMemberIds() {
        List<Blacklist> blacklists = blacklistCommonDao.fetch();
        return blacklists.stream().map(item->item.getMemberId()).collect(Collectors.toList());
    }
    @Override
    public int delete(String id) {
        return blacklistCommonDao.delete(id);
    }

    @Override
    public int isExistBlackList(String currentUserId) {
        String id = blacklistCommonDao.execute(dsl -> dsl
                .select(BLACKLIST.ID)
                .from(BLACKLIST)
                .where(BLACKLIST.MEMBER_ID.eq(currentUserId))
                .limit(1)
                .fetchOne(Record1::value1));
        return Objects.isNull(id) ? 0 : 1;
    }
}
