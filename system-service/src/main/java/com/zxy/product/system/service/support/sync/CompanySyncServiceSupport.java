package com.zxy.product.system.service.support.sync;

import static com.zxy.product.system.jooq.Tables.MENU;

import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.api.sync.CompanySyncService;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.Grant;
import com.zxy.product.system.entity.Menu;
import com.zxy.product.system.entity.Role;
import com.zxy.product.system.entity.RoleMenu;

@Service
public class CompanySyncServiceSupport implements CompanySyncService {

	private static final String SUPER_ADMIN_ROLE_NAME = "超级管理员";

	private MessageSender sender;
	private CommonDao<Role> roleDao;
	private CommonDao<Menu> menuDao;
	private CommonDao<RoleMenu> roleMenuDao;
	private CommonDao<Grant> grantDao;

	@Autowired
	public void setSender(MessageSender sender) {
		this.sender = sender;
	}
	@Autowired
	public void setRoleDao(CommonDao<Role> roleDao) {
		this.roleDao = roleDao;
	}
	@Autowired
	public void setMenuDao(CommonDao<Menu> menuDao) {
		this.menuDao = menuDao;
	}
	@Autowired
	public void setRoleMenuDao(CommonDao<RoleMenu> roleMenuDao) {
		this.roleMenuDao = roleMenuDao;
	}
	@Autowired
	public void setGrantDao(CommonDao<Grant> grantDao) {
		this.grantDao = grantDao;
	}

	@Override
	public void addGrantForFirstSync(String organizationId, String memberId) {
		// 新增角色-超级管理员
		Role r = createCompanySuperAdminRole(SUPER_ADMIN_ROLE_NAME, organizationId);
		// 为超级管理员授权
		createCompanySuperAdminGrant(r.getId(), memberId, organizationId);
	}

	/** 创建超级管理员角色 */
	private Role createCompanySuperAdminRole(String name, String organizationId) {
		Role r = new Role();
		r.forInsert();
		r.setName(name);
		r.setOrganizationId(organizationId);
        r.setParentId(null);
        r.setInit(Role.INIT_YES);
		roleDao.insert(r);

		roleMenuDao.insert(menuDao.fetch(MENU.INIT.eq(Menu.INIT_YES)).stream().map(m -> {
			RoleMenu rm = new RoleMenu();
			rm.forInsert();
			rm.setMenuId(m.getId());
			rm.setRoleId(r.getId());

			return rm;
		}).collect(Collectors.toList()));

		sender.send(MessageTypeContent.SYSTEM_ROLE_INSERT, MessageHeaderContent.ID, r.getId());
		return r;
	}

	/** 为超级管理员授权 */
	private void createCompanySuperAdminGrant(String roleId, String memberId, String organizationId) {
        // 添加授权基本表
        Grant g = new Grant();
        g.forInsert();
        g.setOrganizationId(organizationId);
        g.setRoleId(roleId);
        g.setOperatorTypes(OPERATOR_TYPES);
        g = grantDao.insert(g);
	}

}
