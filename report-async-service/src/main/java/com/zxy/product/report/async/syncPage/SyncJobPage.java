package com.zxy.product.report.async.syncPage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springside.modules.utils.collection.ListUtil;
import org.springside.modules.utils.collection.MapUtil;

import com.zxy.product.human.entity.Job;
import com.zxy.product.report.api.JobService;
import com.zxy.product.report.api.OrganizationService;
import com.zxy.product.report.api.SyncItemJobService;
import com.zxy.product.report.entity.Organization;
import com.zxy.product.report.entity.SyncItemJob;
import com.zxy.product.report.entity.SyncLog;

@Service
public class SyncJobPage extends SyncPage<Job>{

	private Integer pageSize = 5000;
	private JobService jobService;
	private OrganizationService organizationService;
	private SyncItemJobService syncItemJobService;

	@Autowired
	public void setOrganizationService(OrganizationService organizationService) {
		this.organizationService = organizationService;
	}
	
	@Autowired
	public void setJobService(JobService jobService) {
		this.jobService = jobService;
	}
	
	@Autowired
	public void setSyncItemJobService(SyncItemJobService syncItemJobService) {
		this.syncItemJobService = syncItemJobService;
	}
	
	/**
	 * 分页
	 */
	@Override
	public Map<String, List<Job>> syncPage(String fileName) {
		Map<String, List<Job>> jobHashMap = new HashMap<String, List<Job>>();
		Map<String, Integer> syncItemDataCount = syncItemJobService.syncItemDataCount(fileName, SyncLog.STATE_SUCCESS);
		syncItemDataCount.forEach((t, tatol) -> {
			if (SyncItemJob.ACTION_TYPE_INSERT.equals(t)) {
				getPage(fileName, jobHashMap, t, tatol);
			} else if (SyncItemJob.ACTION_TYPE_UPDATE.equals(t)) {
				getPage(fileName, jobHashMap, t, tatol);
				filterData(jobHashMap, fileName);
			}
		});
		return jobHashMap;
	}

	/**
	 * 分类分页
	 */
	@Override
	public void getPage(String fileName, Map<String, List<Job>> dataMap, String type, Integer tatol) {
		if (tatol < pageSize) {
			List<com.zxy.product.report.entity.Job> syncDatas = syncItemJobService.findSyncItemJobPage(SyncLog.STATE_SUCCESS, pageSize, Optional.of(type), Optional.of(SyncLog.STATE_SUCCESS), Optional.of(fileName));
			List<Job> humanJobList = changeEntity(syncDatas, fileName);
			// 新增有效职务过滤
			humanJobList = doFilter(type, humanJobList);
			dataMap.put(type + "-1", humanJobList);
		} else if (tatol >= pageSize){ 
			Integer pages = (tatol + pageSize -1) / pageSize;
			for (int i = 1; i < pages + 1; i++) {
				List<com.zxy.product.report.entity.Job> syncDatas = syncItemJobService.findSyncItemJobPage(i, pageSize, Optional.of(type), Optional.of(SyncLog.STATE_SUCCESS), Optional.of(fileName));
				List<Job> humanJobList = changeEntity(syncDatas, fileName);
				// 新增有效职务过滤
				humanJobList = doFilter(type, humanJobList);
				dataMap.put(type + "-" + i, humanJobList);
			}
		}		
	}

	/**
	 * // 新增有效职务
	 * @param type
	 * @param humanJobList
	 * @return
	 */
	private List<Job> doFilter(String type, List<Job> humanJobList) {
		if (SyncItemJob.ACTION_TYPE_INSERT.equals(type)) { // 新增有效职务
			humanJobList = humanJobList.stream().filter(j -> j.getStatus() == 1).collect(Collectors.toList());
		}
		return humanJobList;
	}

	/**
	 * 过滤要更新的数据
	 * @param jobMap 元数据
	 * @param fileName 文件名
	 */
	public void filterData(Map<String, List<Job>> jobMap, String fileName) {
		Map<String, List<Job>> filterDataMap = new HashMap<String, List<Job>>();
		jobMap.forEach((o, list) -> {
			String syncType = o.split("-")[0];
			if (SyncItemJob.ACTION_TYPE_UPDATE.equals(syncType)) {
				List<Job> fillerDatas = new ArrayList<Job>();
				Set<String> codes = list.stream().map(Job :: getCode).collect(Collectors.toSet());
				List<com.zxy.product.report.entity.Job> jobs = jobService.getJobByCode(codes);
				// 已有 miscode，misId
				Map<String, com.zxy.product.report.entity.Job> reportJobMap  = MapUtil.newHashMap();
			    jobs.stream().filter(job -> fileName.split("_")[1].equals(job.getMisCode())).forEach(j -> {
			    	reportJobMap.putIfAbsent(j.getMisId(), j);
				});			
//			    Map<String, com.zxy.product.report.entity.Job> reportJobMap = jobs.stream().filter(p -> fileName.split("_")[1].equals(p.getMisCode())).collect(Collectors.toMap(com.zxy.product.report.entity.Job :: getMisId, j -> j));
			    // 首次更新初始化 miscode，misId
			    Map<String, com.zxy.product.report.entity.Job> fillerMap  = MapUtil.newHashMap();
			    jobs.stream().filter(p -> null == p.getMisCode()).forEach(j -> {
			    	reportJobMap.putIfAbsent(j.getCode(), j);
				});	
//			    List<com.zxy.product.report.entity.Job> filterJobList = jobs.stream().filter(p -> null == p.getMisCode()).collect(Collectors.toList());	    
//			    List<String> filtercodeList = filterJobList.stream().map(com.zxy.product.report.entity.Job :: getCode).collect(Collectors.toList());
//				Map<String, com.zxy.product.report.entity.Job> fillerMap = MapUtil.newHashMap(filtercodeList, filterJobList);
				list.forEach(l -> {
					//同一 misId + misCode下  职务名称 状态 是否发生变化
					com.zxy.product.report.entity.Job  job = reportJobMap.get(l.getMisId());
					com.zxy.product.report.entity.Job fillerjob = fillerMap.get(l.getCode());
					if (null != job && (!l.getName().equals(job.getName()) || !l.getStatus().equals(job.getStatus()))) {
						l.setJobId(job.getId());
						fillerDatas.add(l);
					} else if (null != fillerjob) { // 更新初始化 miscode，misId
						l.setJobId(fillerjob.getId());
						fillerDatas.add(l);
					}
				});
				filterDataMap.put(o, fillerDatas);
			} else {
				filterDataMap.put(o, list);
			}
		});
		jobMap.clear();
		jobMap.putAll(filterDataMap);
		
	}

	
	/**
	 * 实体转化
	 * @param syncDatas
	 * @return
	 */
	private List<Job> changeEntity(List<com.zxy.product.report.entity.Job> syncDatas, String fileName) {
		String misCode = fileName.split("_")[1];
		Optional<Organization> organization = organizationService.getByMisCodeAndDepth(misCode, Organization.DEPTH);
		String organizationId = organization.get().getId();
		List<Job> jobList = ListUtil.newArrayList();
		syncDatas.forEach(r -> {
			Job job = new Job();
			job.setId(r.getId());
		    job.setCode(r.getCode());
			job.setName(r.getName());
			job.setStatus(r.getStatus());
			job.setJobTypeId(r.getJobTypeId());
			job.setOrganizationId(organizationId);
			job.setMisId(r.getMisId());
			job.setMisCode(misCode);
			jobList.add(job);
		});
		return jobList;
	}
	
}
