package com.zxy.product.report.async.result;

import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.SyncDocumentDetailService;
import com.zxy.product.report.api.SyncLogService;
import com.zxy.product.report.entity.SyncDocumentDetail;
import com.zxy.product.report.entity.SyncLog;

@Service
public class ResultExtracted {

	private static final Logger LOGGER = LoggerFactory.getLogger(ResultExtracted.class);
	private SyncLogService syncLogService;
	private CommonDao<SyncLog> syncLogDao;
	private CycleMessage cycleMessage;
	private SyncDocumentDetailService syncDocumentDetailService;
	private CommonDao<SyncDocumentDetail> syncDocumentDetailDao;
	
	@Autowired
	public void setCycleMessage(CycleMessage cycleMessage) {
		this.cycleMessage = cycleMessage;
	}
	
	@Autowired
	public void setSyncDocumentDetailDao(CommonDao<SyncDocumentDetail> syncDocumentDetailDao) {
		this.syncDocumentDetailDao = syncDocumentDetailDao;
	}
	
	@Autowired
	public void setSyncLogDao(CommonDao<SyncLog> syncLogDao) {
		this.syncLogDao = syncLogDao;
	}
	
	@Autowired
	public void setSyncLogService(SyncLogService syncLogService) {
		this.syncLogService = syncLogService;
	}
	
	@Autowired
	public void setSyncDocumentDetailService(SyncDocumentDetailService syncDocumentDetailService) {
		this.syncDocumentDetailService = syncDocumentDetailService;
	}
	
    /**
     * 
     * 更新 documentDetail
     * 同类型同步总数是否相同  相同进入下一级
     * 数据回传
     * @param version
     * @param result
     */
	public void resultExtracted(String version, Map<String, Integer> result, String type) {
		Optional<SyncLog> syncLog = syncLogService.getLogBySyncType(version);
        syncLog.ifPresent(x -> {
        	Integer totalCount = x.getCount();
				Integer success = result.get("1") == null ? 0 : result.get("1");
				Integer failure = result.get("2") == null ? 0 : result.get("2");
				LOGGER.info("hr-sync-lisener  totalCount : " + totalCount + "  successCount :" + success + " failureCount : " + failure);
				if (success + failure == totalCount) {
					x.setFailureCount(failure);
					x.setEndTime(System.currentTimeMillis());
					x.setStatus(failure > 0 ? SyncLog.STATE_FAILURE : SyncLog.STATE_SUCCESS);
					syncLogDao.update(x);
					Optional<SyncDocumentDetail> syncDocumentDetail = syncDocumentDetailService.get(version);
					syncDocumentDetail.ifPresent(y ->{
						y.setStatus(SyncLog.STATE_SUCCESS);
						syncDocumentDetailDao.update(y);
						//同类型同步总数是否相同  相同进入下一级
						cycleMessage.cycleMessage(type, syncLog, y);
					});
				}
        });
	}
}
