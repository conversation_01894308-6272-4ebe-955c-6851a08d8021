package com.zxy.product.report.async.listener;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.report.async.csv.CourseStudy;
import com.zxy.product.report.async.csv.CsvUpload;
import com.zxy.product.report.async.util.DateUtil;
import com.zxy.product.report.async.util.SFTPChannel;
import com.zxy.product.report.entity.CsvTime;
import com.zxy.product.system.content.MessageTypeContent;

@Service
public class StudyRecordListener  extends AbstractMessageListener{

	private static final Logger LOGGER = LoggerFactory.getLogger(StudyRecordListener.class);

	private CourseStudy courseStudy;
	private CsvUpload  csvUpload;
	private MessageSender sender;
	private Cache scheduleCache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.scheduleCache = cacheService.create(CsvTime.CSV_DAY_TO_REDISE);
    }

	@Autowired
	public void setSender(MessageSender sender) {
		this.sender = sender;
	}

	@Autowired
	public void setCsvUpload(CsvUpload csvUpload) {
		this.csvUpload = csvUpload;
	}

	@Autowired
	public void setCourseStudy(CourseStudy courseStudy) {
		this.courseStudy = courseStudy;
	}

	@Override
	protected void onMessage(Message message) {
		if (MessageTypeContent.SYSTEM_SCHEDULE_MESSAGE == message.getType() &&
			SFTPChannel.getSFTPSetting("csv.ftp.time.start").equals(DateUtil.getDay())){
			String cacheDate = scheduleCache.get(CsvTime.CSV_DAY_TO_REDISE, String.class);
			if(cacheDate == null){
				String firstDay = csvUpload.firstDay(CsvTime.CSV_STUDY);
				scheduleCache.set(CsvTime.CSV_DAY_TO_REDISE, firstDay, 60 * 60 * 24);
				LOGGER.info("StudyRecordListener  onMessage " + DateUtil.getDay());

				Map<String, List<String>> courseStudyInfo = courseStudy.getCourseStudy();
				String fileName = csvUpload.getFileName(CsvTime.STUDY_RECORD);
				String localDir = csvUpload.batchCreate(courseStudyInfo, fileName, CsvTime.STUDY_RECORD_HEADER);
				csvUpload.upload(fileName, localDir, "csv.ftp.dir.put");
	            sender.send(com.zxy.product.report.content.MessageTypeContent.CSV_RECORD_TO_COURSE);
			}
		}
	}

	@Override
	public int[] getTypes() {
		return new int[]{MessageTypeContent.SYSTEM_SCHEDULE_MESSAGE };
	}


}
