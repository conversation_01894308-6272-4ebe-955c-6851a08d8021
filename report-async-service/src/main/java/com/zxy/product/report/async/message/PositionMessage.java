package com.zxy.product.report.async.message;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.report.api.SyncDocumentDetailService;
import com.zxy.product.report.api.SyncLogService;
import com.zxy.product.report.content.MessageHeaderContent;
import com.zxy.product.report.content.MessageTypeContent;
import com.zxy.product.report.entity.SyncLog;

@Service
public class PositionMessage extends sendMessage{

	private MessageSender sender;
	private SyncLogService syncLogService;
	private SyncDocumentDetailService syncDocumentDetailService;
	
	@Autowired
	public void setSyncDocumentDetailService(SyncDocumentDetailService syncDocumentDetailService) {
		this.syncDocumentDetailService = syncDocumentDetailService;
	}
	
	
    @Autowired
    public void setSender(MessageSender sender) {
		this.sender = sender;
	}
    
    @Autowired
    public void setSyncLogService(SyncLogService syncLogService) {
    	this.syncLogService = syncLogService;
    }
    
    /**
     * 发送通知 该文件的同步完成
     */
    @Override
	public void dataMessage(String fileName, String documentId){
    	String[] fileNames = fileName.split("_");
		syncDocumentDetailService.insert(Optional.of(fileNames[1]), Optional.of(SyncLog.TYPE_ZHIWEI), Optional.of(fileNames[3]), Optional.of(documentId), Optional.of(SyncLog.STATE_RUNING), Optional.of(fileName));
		syncLogService.insert(SyncLog.TYPE_ZHIWEI, fileNames[1], fileNames[3], 0, SyncLog.STATE_RUNING, fileName, -1);
		sender.send(MessageTypeContent.SYNC_POSITION_TO_HUMAN, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "S",
				MessageHeaderContent.VERSION, fileName);
	}

    /**
     * 发送通知 该文件的同步完成
     */
    @Override
	public void checkMessage(String fileName, Integer tatol){
		syncLogService.insert(SyncLog.TYPE_ZHIWEI, fileName.split("_")[1], fileName.split("_")[3], tatol, SyncLog.STATE_RUNING, fileName, 1);
		sender.send(MessageTypeContent.SYNC_POSITION_TO_HUMAN, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "S",
				MessageHeaderContent.VERSION, fileName);
	}
	
}
