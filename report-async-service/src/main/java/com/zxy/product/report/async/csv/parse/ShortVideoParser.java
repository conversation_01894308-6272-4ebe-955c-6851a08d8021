package com.zxy.product.report.async.csv.parse;

import com.zxy.product.course.entity.ShortVideoReport;
import com.zxy.product.report.async.csv.util.FilterEmojiUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2023/5/12
 * @description ：
 */
@Service
public class ShortVideoParser extends ShortVideoAbstractParser<ShortVideoReport>{
    private static final Logger LOGGER = LoggerFactory.getLogger(ShortVideoParser.class);

    @Override
    public List<ShortVideoReport> parseShortVideoReportArray(InputStream inputStream) {
        InputStreamReader isr = new InputStreamReader(inputStream);
        List<ShortVideoReport> shortVideoReportList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(isr)) {
            String line;
            int i = 1;
            while ((line = br.readLine()) != null) {
                if (i >= 2){
                    List<String> collect = Arrays.stream(line.split(",")).collect(Collectors.toList());
                    LOGGER.error("短视频报表拉取数据 解析操作 ShortVideoReport 第{}行，={}", i, collect);
                    ShortVideoReport shortVideoReport = new ShortVideoReport();
                    shortVideoReport.forInsert();
                    shortVideoReport.setShortVideoName(StringUtils.isNotEmpty(collect.get(1)) ? FilterEmojiUtil.filterEmoji(collect.get(1)) : collect.get(1));
                    shortVideoReport.setShortVideoCode(collect.get(2));
                    shortVideoReport.setCourseId(collect.get(3));
                    shortVideoReport.setCourseName(collect.get(4));
                    shortVideoReport.setShortVideoStstus(Integer.valueOf(collect.get(5)));
                    shortVideoReport.setLatestReleaseTime(Long.valueOf(collect.get(8)));
                    shortVideoReport.setTotalLearningTime(collect.get(10));
                    shortVideoReport.setTotalLearningTimeNumber(Long.valueOf(collect.get(10)));
                    shortVideoReport.setIndividualShortVideoViewCounts(Integer.valueOf(collect.get(11)));
                    shortVideoReport.setIndividualShortVideoLikes(Integer.valueOf(collect.get(12)));
                    shortVideoReport.setIndividualShortVideoShareVolume(Integer.valueOf(collect.get(13)));
                    shortVideoReport.setIndividualShortVideoCommentVolume(Integer.valueOf(collect.get(14)));
                    shortVideoReport.setFirstReleaseTime(Long.valueOf(collect.get(15)));
                    shortVideoReport.setNumberStudents(Integer.valueOf(collect.get(21)));
                    shortVideoReport.setNumberFinish(Integer.valueOf(collect.get(22)));
                    shortVideoReport.setCompletionRate(collect.get(23));
                    shortVideoReport.setFinishedSeeding(Integer.valueOf(collect.get(24)));
                    shortVideoReport.setTotalShortVideoDuration(collect.get(25));
                    shortVideoReport.setTotalShortVideoDurationNumber(Long.valueOf(collect.get(25)));
                    shortVideoReport.setActivityFlag("1".equals(collect.get(26)) ? true : false);
                    shortVideoReport.setIndividualShortVideoFavoriteVolume(Integer.valueOf(collect.get(27)));
                    shortVideoReportList.add(shortVideoReport);
                }
                i++;
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return shortVideoReportList;
    }

    @Override
    protected List<ShortVideoReport> parseShortVideoCreateDetailsArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<ShortVideoReport> parsePersonalShortVideoLearningDetailsArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<ShortVideoReport> parseIndividualShortVideoDetailsArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<ShortVideoReport> parseIndividualShortVideoSummaryArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<ShortVideoReport> parseShortVideoLogDayArray(InputStream inputStream) {
        return null;
    }
}
