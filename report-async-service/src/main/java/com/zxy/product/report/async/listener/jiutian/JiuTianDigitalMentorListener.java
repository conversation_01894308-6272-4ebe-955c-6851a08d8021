package com.zxy.product.report.async.listener.jiutian;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.product.course.api.BusinessTopicService;
import com.zxy.product.course.api.CourseChapterInfoService;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.model.mentor.ModelMentorService;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.report.async.intelligence.jiutian.JiuTianConstant;
import com.zxy.product.report.async.intelligence.jiutian.util.CourseUploadForFTP;
import com.zxy.product.report.async.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 数智导师监听器
 */
@Service
public class JiuTianDigitalMentorListener extends AbstractMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(JiuTianDigitalMentorListener.class);

    private CourseInfoService courseInfoService;
    private AttachmentResolver attachmentResolver;
    private CourseChapterInfoService courseChapterInfoService;
    private FileService fileService;
    private BusinessTopicService businessTopicService;
    private CourseUploadForFTP courseUploadForFTP;
    private MessageSender messageSender;
    private ModelMentorService mentorService;

    private static final List<String> SUFFIX = Arrays.asList("txt", "doc", "docx");
    private static final int MAX_LENGTH = 30000;
    // 字段的分隔符
    private final static String DELIMITER = "\u0001";
    private final static String DELIMITER1 = "#";
    private static final int TYPE_COURSE = 1;
    private static final int TYPE_SUBJECT = 2;
    private  static final int FILE_NUM = 0;
    private static final String COMMA = ",";


    @Autowired
    public void setMentorService(ModelMentorService mentorService) {
        this.mentorService = mentorService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCourseUploadForFTP(CourseUploadForFTP courseUploadForFTP) {
        this.courseUploadForFTP = courseUploadForFTP;
    }
    @Autowired
    public void setCourseChapterInfoService(CourseChapterInfoService courseChapterInfoService) {
        this.courseChapterInfoService = courseChapterInfoService;
    }
    @Autowired
    public void setBusinessTopicService(BusinessTopicService businessTopicService) {
        this.businessTopicService = businessTopicService;
    }


    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }


    @Override
    protected void onMessage(Message message) {
        int type = message.getType();
        switch (type) {
            case MessageTypeContent.DIGITAL_MENTOR_TOPIC:
                String courseIds = message.getHeader(MessageHeaderContent.IDS);
                smartMentorSubmitsData(courseIds);
                break;
            case MessageTypeContent.DIGITAL_MENTOR_GET_SUMMARY:
                String ids = message.getHeader(MessageHeaderContent.IDS);
                getSummary(ids);
                break;
            default:
                break;
        }
    }

    private void getSummary(String ids){
        LOGGER.error("获取摘要,ids：{}" , ids);
        List<String> idList = Arrays.stream(ids.split(",")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)){
            LOGGER.error("获取摘要失败，ids为空");
            return;
        }
        idList.forEach(id -> messageSender.send(MessageTypeContent.DIGITAL_MENTOR_GET_SUMMARY_NEW, MessageHeaderContent.ID, id));
    }

    private void smartMentorSubmitsData(String courseIds) {
        LOGGER.info("收到数智导师消息,课程ID为：{}", courseIds);
        String[] list = courseIds.split(",");
        StringBuilder sb = new StringBuilder();
        for (String courseId : list) {
            dealWithSubject(courseId, sb);
        }

        if (sb.length() == 0){
            LOGGER.info("没有符合的专题数据，停止推送");
            return;
        }
     /*   String data = "";
        if (sb.length() > 2) {
            data = sb.delete(sb.length() - 2, sb.length()).toString().replaceAll("null", "");
        }*/
        LOGGER.info("数据：{}", sb);
        courseUploadForFTP.upload(sb.toString(), JiuTianConstant.SYNC_TYPE_CONTENT_TOPIC_INFO, FILE_NUM, list.length, true);
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.DIGITAL_MENTOR_TOPIC,
                MessageTypeContent.DIGITAL_MENTOR_GET_SUMMARY
        };
    }

    /**
     * 处理专题
     * @param courseId 专题id
     */
    void dealWithSubject(String courseId, StringBuilder sb) {
        //查询专题信息
        CourseInfo courseInfo = courseInfoService.getCourseInfo(courseId);

        if (!ObjectUtils.isEmpty(courseInfo) && Objects.equals(courseInfo.getBusinessType(), CourseInfo.BUSINESS_TYPE_COURSE)){
            LOGGER.error("数智导师不符合推送条件，为课程");
            return;
        }

        if (ObjectUtils.isEmpty(courseInfo) || !Objects.equals(courseInfo.getStatus(), CourseInfo.STATUS_SHELVES)) {
            LOGGER.error("数智导师不符合推送条件，为空或者状态不为发布");
            return;
        }

        List<CourseChapterSection> courseChapterSections = null;
        if (Objects.equals(courseInfo.getBusinessType(), CourseInfo.BUSINESS_TYPE_SUBJECT)) {
            //查询专题下所有课程名,id
            courseChapterSections = courseChapterInfoService.getCourseNames(courseId);
            LOGGER.info("专题下课程/专题：{}", courseChapterSections);
            boolean isOK = mentorService.findCourseMainNoteIsThereAny(courseChapterSections.stream().map(CourseChapterSection::getResourceId).collect(Collectors.toList()));
            if (!isOK) {
                LOGGER.error("专题不符合推送条件,没有有摘要");
                return;
            }
        }

        //获取专题通知公告
        LOGGER.error("通知公告解析前：{}", courseInfo.getStyles());
        String notices = noticesAndAnnouncements(courseInfo.getStyles());
        LOGGER.error("通知公告：{}", notices);
        courseInfo.setNotify(notices);
        //附件文本
        StringBuilder attachmentText = getAttachmentText(courseId);
        String removeWhitespace = removeWhitespace(attachmentText.toString());
        LOGGER.info("附件文本：{}", removeWhitespace);

        //查询专题标签
        List<String> topicNames = businessTopicService.getTopicNames(courseId);
        LOGGER.info("专题标签：{}", topicNames);
        //信息
        sb.append(courseId)
                .append(DELIMITER)
                .append(courseInfo.getName())
                .append(DELIMITER)
                .append(ObjectUtils.isEmpty(courseInfo.getBeginDate()) ? "" : DateUtil.dateTransition(courseInfo.getBeginDate()))
                .append(DELIMITER)
                .append(ObjectUtils.isEmpty(courseInfo.getEndDate()) ? "" : DateUtil.dateTransition(courseInfo.getEndDate()))
                .append(DELIMITER)
                .append(courseInfo.getDescription());

        sb.append(DELIMITER);
        if (!CollectionUtils.isEmpty(topicNames)) {
            sb.append(String.join("|", topicNames));
        }

        sb.append(DELIMITER);
        //专题下课程/专题
        if (!CollectionUtils.isEmpty(courseChapterSections)) {
            StringJoiner joiner = new StringJoiner(COMMA);
            courseChapterSections.forEach(courseChapterSection ->
                    joiner.add(getType(courseChapterSection.getSectionType()) +
                            DELIMITER1 +
                            courseChapterSection.getResourceId())
            );
            sb.append(joiner);
        }
        //通知/附件
        sb.append(DELIMITER).append(notices).append(DELIMITER).append(removeWhitespace).append("\n");
    }


    /**
     * 获取附件文本
     */
    private StringBuilder getAttachmentText(String courseId) {
        StringBuilder stringBuilder = new StringBuilder();
        //获取附件id
        List<String> attachmentIds = courseInfoService.getAttachmentIds(courseId);
        if (CollectionUtils.isEmpty(attachmentIds)) {
            LOGGER.error("专题附件信息不存在,课程ID为：{}", courseId);
            return stringBuilder;
        }
        //获取附件内容
        List<Attachment> attachmentList = fileService.findByIds(attachmentIds);
        for (Attachment attachment : attachmentList) {
            if (!ObjectUtils.isEmpty(attachment.getExtention()) && SUFFIX.contains(attachment.getExtention())){
                com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
                fastDFSAttachment.setPath(attachment.getPath());
                byte[] bytes = attachmentResolver.downloadFile(fastDFSAttachment);
                try (InputStream is = new ByteArrayInputStream(bytes);
                     BufferedReader reader = new BufferedReader(
                             new InputStreamReader(is, StandardCharsets.UTF_8))) {

                    StringBuilder contentBuilder = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        contentBuilder.append(line).append("\n");
                    }
                    String content = contentBuilder.toString();
                    stringBuilder.append(content);

                    //超过3万截取
                    if (stringBuilder.length() >= MAX_LENGTH){
                        stringBuilder.setLength(MAX_LENGTH);
                        return stringBuilder;
                    }
                } catch (IOException e) {
                    LOGGER.error("文件解析失败");
                }
            }
        }
        return stringBuilder;
    }

    public static String removeWhitespace(String text) {
        return text.chars()
                .filter(c -> !Character.isWhitespace(c))
                .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append)
                .toString();
    }


    /**
     * 通知公告json处理
     */
    private String noticesAndAnnouncements(String style) {
        if (StringUtils.isEmpty(style)) {
            LOGGER.error("专题通知公告为空");
            return "";
        }

        JSONObject jsonObject = JSON.parseObject(style);
        JSONArray regions = jsonObject.getJSONArray("regions");

        Optional<String> contentValue = regions.stream()
                .map(region -> (JSONObject) region)
                .map(region -> region.getJSONObject("regionModule"))
                .filter(regionModule -> "announcement".equals(regionModule.getString("code")))
                .map(regionModule -> regionModule.getString("contentValue"))
                .filter(Objects::nonNull)
                .findFirst();
        return contentValue.orElse("");
    }


    private int getType(int type){
        return type == CourseChapterSection.SECTION_TYPE_COURSE ? TYPE_COURSE : TYPE_SUBJECT;
    }
}
