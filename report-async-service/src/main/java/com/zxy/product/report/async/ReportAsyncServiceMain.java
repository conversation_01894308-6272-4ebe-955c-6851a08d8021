package com.zxy.product.report.async;

import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.report.async.config.*;
import com.zxy.product.report.jooq.Report;
import org.jooq.Schema;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {MongoDataAutoConfiguration.class, MongoAutoConfiguration.class})
@EnableScheduling
@Import({ RPCClientConfig.class, CommonDaoConfig.class, MessageConfig.class, MetricsMonitorConfig.class , RestTemplateConfig.class, MongoConfig.class})
public class ReportAsyncServiceMain {
    @Bean
    public Schema schema() {
        return Report.REPORT_SCHEMA; // jOOQ生成代码的根目录下与数据库同名的类
    }

    public static void main(String[] args) throws Exception {
        SpringApplication.run(ReportAsyncServiceMain.class, args);
    }
}
