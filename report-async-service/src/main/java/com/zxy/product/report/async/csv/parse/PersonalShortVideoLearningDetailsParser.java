package com.zxy.product.report.async.csv.parse;

import com.zxy.product.course.entity.PersonalShortVideoLearningDetails;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.report.async.csv.util.FilterEmojiUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2023/5/13
 * @description ：
 */
@Service
public class PersonalShortVideoLearningDetailsParser extends ShortVideoAbstractParser<PersonalShortVideoLearningDetails>{
    private static final Logger LOGGER = LoggerFactory.getLogger(PersonalShortVideoLearningDetailsParser.class);

    private MemberService memberService;

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    protected List<PersonalShortVideoLearningDetails> parseShortVideoReportArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<PersonalShortVideoLearningDetails> parseShortVideoCreateDetailsArray(InputStream inputStream) {
        return null;
    }

    @Override
    public List<PersonalShortVideoLearningDetails> parsePersonalShortVideoLearningDetailsArray(InputStream inputStream) {
        InputStreamReader isr = new InputStreamReader(inputStream);
        List<PersonalShortVideoLearningDetails> personalShortVideoLearningDetailsList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(isr)) {
            String line;
            int i = 1;
            while ((line = br.readLine()) != null) {
                if (i >= 2){
                    List<String> collect = Arrays.stream(line.split(",")).collect(Collectors.toList());
                    LOGGER.error("短视频报表拉取数据 解析操作 personalShortVideoLearningDetails 第{}行，={}", i, collect);
                    String memberId = collect.get(0);
                    Optional<Member> optionalMember = memberService.getOptional(memberId);
                    PersonalShortVideoLearningDetails personalShortVideoLearningDetails = new PersonalShortVideoLearningDetails();
                    personalShortVideoLearningDetails.forInsert();
                    personalShortVideoLearningDetails.setMemberId(memberId);
                    if (optionalMember.isPresent()){
                        Member member = optionalMember.get();
                        personalShortVideoLearningDetails.setMemberName(member.getName());
                        personalShortVideoLearningDetails.setUnit(member.getCompOrganization().getName());
                        personalShortVideoLearningDetails.setOrganizationId(member.getOrganizationId());
                        personalShortVideoLearningDetails.setJob(member.getMajoyPosition().getJob().getId());
                        personalShortVideoLearningDetails.setLine(member.getMajoyPosition().getAliasId());
                        personalShortVideoLearningDetails.setMemberStatus(String.valueOf(member.getMajoyPosition().getStatus()));
                    }
                    personalShortVideoLearningDetails.setShortVideoName(StringUtils.isNotEmpty(collect.get(1)) ? FilterEmojiUtil.filterEmoji(collect.get(1)) : collect.get(1));
                    personalShortVideoLearningDetails.setShortVideoCode(collect.get(2));
                    personalShortVideoLearningDetails.setFirstBroadcastTime(Long.valueOf(collect.get(3)));
                    personalShortVideoLearningDetails.setPlayCompletionTime(Long.valueOf(collect.get(4)));
                    personalShortVideoLearningDetails.setTotalLearningTime(collect.get(5));
                    personalShortVideoLearningDetails.setTotalLearningTimeNumber(Long.valueOf(collect.get(5)));
                    personalShortVideoLearningDetails.setIsFinish(Integer.valueOf(collect.get(6)));
                    personalShortVideoLearningDetailsList.add(personalShortVideoLearningDetails);
                }
                i++;
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return personalShortVideoLearningDetailsList;
    }

    @Override
    protected List<PersonalShortVideoLearningDetails> parseIndividualShortVideoDetailsArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<PersonalShortVideoLearningDetails> parseIndividualShortVideoSummaryArray(InputStream inputStream) {
        return null;
    }

    @Override
    protected List<PersonalShortVideoLearningDetails> parseShortVideoLogDayArray(InputStream inputStream) {
        return null;
    }
}
