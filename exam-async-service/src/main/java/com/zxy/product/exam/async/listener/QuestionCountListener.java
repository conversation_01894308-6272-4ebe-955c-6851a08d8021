package com.zxy.product.exam.async.listener;




import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.async.helper.QuestionCountListenerHelper;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;

/**
 * <AUTHOR>
 * 用于随机组卷
 * 试题数目统计
 *
 */
@Component
public class QuestionCountListener extends AbstractMessageListener{

    private static final Logger LOGGER = LoggerFactory.getLogger(QuestionCountListener.class);
    
    private QuestionCountListenerHelper helper;
    
    @Autowired
    public void setHelper(QuestionCountListenerHelper helper) {
        this.helper = helper;
    }
    
    @Override
    protected void onMessage(Message message) {
        LOGGER.info("exam/QuestionCountListener", message.toString());
        String questionIds = message.getHeader(MessageHeaderContent.IDS);
        
        init();
        
        switch (message.getType()) {
            case MessageTypeContent.EXAM_QUESTION_PUBLISH:
                updateQuestionCount(questionIds);
                break;
            case MessageTypeContent.EXAM_QUESTION_CANCEL:
                updateQuestionCount(questionIds);
                break;
            default:
                break;
        }
    }
        
    private void updateQuestionCount(String questionIds) {
        helper.updateQuestionCount(questionIds);
    }

    private void init() {
        if (helper.needToInit()) helper.initQuestionCount();
    }

    @Override
    public int[] getTypes() {
        return new int[]{
            MessageTypeContent.EXAM_QUESTION_PUBLISH,
            MessageTypeContent.EXAM_QUESTION_CANCEL,
        };
    }

}
