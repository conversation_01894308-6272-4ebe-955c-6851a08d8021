/**
 * *

 迁移到exam-stu

package com.zxy.product.exam.async.listener;



import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.async.helper.ExamRegistListenerHelper;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.entity.ExamRecord;


@Component
public class ExamRegistListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamRegistListener.class);

    private TransactionTemplate transactionTemplate;

    private ExamRegistListenerHelper examRegistListenerHelper;

    @Autowired
    public void setExamRegistListenerHelper(ExamRegistListenerHelper examRegistListenerHelper) {
        this.examRegistListenerHelper = examRegistListenerHelper;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}

    //ps,未开始转考试中，考试中转已结束的两种情况没有做对应的消息监听
    @Override
    protected void onMessage(Message message) {

    	Integer type = message.getType();


    	 transactionTemplate.execute(new TransactionCallbackWithoutResult() {
 			@Override
 			protected void doInTransactionWithoutResult(TransactionStatus status) {
 				switch (type) {
 				case MessageTypeContent.EXAM_SIGNUP_INSERT:
 					doSignUpRegisting(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_PASS:
 					doSignUpPassed(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_REFUSE:
 					doSignUpRefused(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_DELETE:
 					doSignUpCancel(message);
 					break;
 				case MessageTypeContent.EXAM_EXAM_RECORD_INSERT:
 					doExamRecordInsert(message);
 					break;
 				case MessageTypeContent.SUBMIT_PAPER:
 					doSubmitPaper(message);
 					break;
 				case MessageTypeContent.EXAM_EXAM_RECORD_DELETE:
 					doExamRecordDeleted(message);
 					break;
 				case MessageTypeContent.EXAM_ADD_USER_TO_EXAM:
 					doExamRecordInsert(message);
 					break;
 				case MessageTypeContent.EXAM_CHANGE_TO_ORDINARY:
 					doExamChangeToOrdinary(message);
 					break;
 				case MessageTypeContent.EXAM_RECORD_SCORE_RESULT:
 					updateTopScore(message);
 					break;
 				case MessageTypeContent.EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER:
 					doExamChangeToNoSpecify(message);
 					break;
 				default:
 					break;
 				}
 			}
    	 });
    }

    private void doExamChangeToNoSpecify(Message message) {
    	String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
    	examRegistListenerHelper.doExamChangeToNoSpecify(examId);
	}

    private void updateTopScore(Message message) {
		String examRecordId = message.getHeader(MessageHeaderContent.ID);
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		examRegistListenerHelper.updateTopScore(examRecordId, examId);
	}

	private void doExamChangeToOrdinary(Message message) {
    	String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
    	examRegistListenerHelper.doExamChangeToOrdinary(examId);
	}

	private void doExamRecordDeleted(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
		examRegistListenerHelper.doExamRecordDeleted(examId, memberId);
	}

	private void doSubmitPaper(Message message) {
		String submitTypeStr = message.getHeader(ExamRecord.SUBMIT_PAPER_TYPE);
		String submitPaperExamId = message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_ID);
		ExamRecord.SubmitType submitType = null;
		if (submitTypeStr != null) {
			submitType = ExamRecord.SubmitType.valueOf(submitTypeStr);
		}
		String examRecordId = message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID);
		examRegistListenerHelper.doSubmitPaper(submitType, examRecordId, submitPaperExamId);
	}

	private void doExamRecordInsert(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		Integer status = Integer.valueOf(message.getHeader(MessageHeaderContent.STATUS));
		examRegistListenerHelper.doExamRecordInsert(examId, memberIds, status);
	}

	private void doSignUpCancel(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		examRegistListenerHelper.doSignUpCancel(examId, memberIds);
	}

	private void doSignUpRefused(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		examRegistListenerHelper.doSignUpRefused(examId, memberIds);
	}

	private void doSignUpPassed(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String ids = message.getHeader(MessageHeaderContent.IDS);
		examRegistListenerHelper.doSignUpPassed(examId, ids);
	}


	private void doSignUpRegisting(Message message) {
		LOGGER.info("ExamRegistListener/doSignUpRegisting", message.toString());
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String ids = message.getHeader(MessageHeaderContent.IDS);
		examRegistListenerHelper.doSignUpRegisting(examId, ids);
	}

	@Override
	public int[] getTypes() {
		return new int[]{

//			MessageTypeContent.EXAM_SIGNUP_INSERT,
//			MessageTypeContent.EXAM_SIGNUP_PASS,
//			MessageTypeContent.EXAM_SIGNUP_REFUSE,
//			MessageTypeContent.EXAM_SIGNUP_DELETE,
//			MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
	//		MessageTypeContent.SUBMIT_PAPER,
//			MessageTypeContent.EXAM_EXAM_RECORD_DELETE,
//			MessageTypeContent.EXAM_ADD_USER_TO_EXAM,
//			MessageTypeContent.EXAM_CHANGE_TO_ORDINARY,
//			MessageTypeContent.EXAM_RECORD_SCORE_RESULT,
//			MessageTypeContent.EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER

		};
	}

}
 */