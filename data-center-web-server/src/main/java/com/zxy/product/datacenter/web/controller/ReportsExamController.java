package com.zxy.product.datacenter.web.controller;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.datacenter.api.service.demo.ReportsExamService;
import com.zxy.product.datacenter.vo.ReportsExamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 学情分析-考试-控制层
 * <AUTHOR>
 */
@Controller
@RequestMapping("/reports-exam")
public class ReportsExamController {

    private static final String HIGH_ERROR_RATE_CACHE_KEY = "high-error-rate#subjectId";
    private static final String HIGH_ACCURACY_CACHE_KEY = "high-accuracy#subjectId";
    private static final String HIGH_HIGH_ACCURACY_CIRCLE_CHART_CACHE_KEY = "high-accuracy-circle-chart#subjectId";
    private static final String QUESTION_TYPE_ACCURACY_CACHE_KEY = "question-type-accuracy#subjectId";
    private static final String MEMBER_EXAM_SCORE_DISTRIBUTED_CACHE_KEY = "member-exam-score-distributed#subjectId";
    private static final String EXAM_QUESTIONS_TYPE_DISTRIBUTED_CACHE_KEY = "exam-questions-type-distributed#subjectId";
    private static final String DIRECTLY_AFFILIATED_ORGANIZATION_CACHE_KEY = "directly-affiliated-organization#subjectId";
    private static final String PROFESSIONAL_COMPANY_ORGANIZATION_CACHE_KEY = "professional-company-organization#subjectId";
    private static final String PROVINCE_ORGANIZATION_CACHE_KEY = "province-organization#subjectId";
    private static final String PROVINCE_ORGANIZATION_ASSESSMENT_CACHE_KEY = "province-organization-assessment#subjectId";
    private static final String PROFESSIONAL_COMPANY_ORGANIZATION_ASSESSMENT_CACHE_KEY = "professional-company-organization-assessment#subjectId";
    private static final String DIRECTLY_AFFILIATED_ORGANIZATION_ASSESSMENT_CACHE_KEY = "directly-affiliated-organization-assessment#subjectId";
    private static final int EXPIRE_TIME = 24 * 60 * 60;

    private ReportsExamService reportsExamService;
    private Cache cache;


    @Autowired
    public void setReportsExamService(ReportsExamService reportsExamService) {
        this.reportsExamService = reportsExamService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("datacenter", "reports-exam");
    }

    /**
     * 试题错误率较高题目
     */
    @RequestMapping("/high-error-rate")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,questionId,titleContent,errorRate,examOrder")
    public List<ReportsExamVO> HighErrorRate(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(HIGH_ERROR_RATE_CACHE_KEY + "#" + subjectId, () -> reportsExamService.HighErrorRate(subjectId) , EXPIRE_TIME);
    }

    /**
     * 试题正确率较高题目
     */
    @RequestMapping("/high-accuracy")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,questionId,titleContent,accuracy,examOrder")
    public List<ReportsExamVO> HighAccuracy(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(HIGH_ACCURACY_CACHE_KEY + "#" + subjectId, () -> reportsExamService.HighAccuracy(subjectId) , EXPIRE_TIME);
    }

    /**
     * 试题答题正确率分布圆图
     */
    @RequestMapping("/high-accuracy-circle-chart")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,accuracyType,count")
    public List<ReportsExamVO> HighAccuracyCircleChart(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(HIGH_HIGH_ACCURACY_CIRCLE_CHART_CACHE_KEY + "#" + subjectId, () -> reportsExamService.HighAccuracyCircleChart(subjectId), EXPIRE_TIME);
    }

    /**
     * 试题类型分布图
     */
    @RequestMapping("/question-type-accuracy")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,questionType,accuracy")
    public List<ReportsExamVO> questionTypeAccuracy(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(QUESTION_TYPE_ACCURACY_CACHE_KEY + "#" + subjectId, () -> reportsExamService.questionTypeAccuracy(subjectId), EXPIRE_TIME);
    }

    /**
     * 学员考核成绩分布
     */
    @RequestMapping("/member-exam-score-distributed")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,scoreType,count,totalScore")
    public List<ReportsExamVO> memberExamScoreDistributed(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(MEMBER_EXAM_SCORE_DISTRIBUTED_CACHE_KEY + "#" + subjectId, () -> reportsExamService.memberExamScoreDistributed(subjectId), EXPIRE_TIME);
    }

    /**
     * 考试试题题型分布
     */
    @RequestMapping("/exam-questions-type-distributed")
    @Param(name = "subjectId", required = true)
    @JSON("subjectId,questionType,highDifficultyCount,mediumDifficultyCount,lowDifficultyCount")
    public List<ReportsExamVO> examQuestionsTypeDistributed(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(EXAM_QUESTIONS_TYPE_DISTRIBUTED_CACHE_KEY + "#" + subjectId, () -> reportsExamService.examQuestionsTypeDistributed(subjectId), EXPIRE_TIME);
    }

    /**
     * 机构分数
     */
    @RequestMapping("/organization-score")
    @Param(name = "subjectId", required = true)
    @Param(name = "organizationId", required = true)
    @JSON("organizationId,organizationName,averageScore,maxScore,subOrganizationName")
    public List<ReportsExamVO> organizationScore(RequestContext requestContext) {
        return reportsExamService.organizationScore(requestContext.getString("subjectId"), requestContext.getString("organizationId"));
    }

    /**
     * 各单位考核分数情况
     * 直属单位
     */
    @RequestMapping("/directly-affiliated-organization")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,averageScore,maxScore")
    public List<ReportsExamVO> directlyAffiliatedOrganization(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(DIRECTLY_AFFILIATED_ORGANIZATION_CACHE_KEY + "#" + subjectId, () -> reportsExamService.directlyAffiliatedOrganization(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核分数情况
     * 专业公司
     */
    @RequestMapping("/professional-company-organization")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,averageScore,maxScore")
    public List<ReportsExamVO> professionalCompanyOrganization(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(PROFESSIONAL_COMPANY_ORGANIZATION_CACHE_KEY + "#" + subjectId, () -> reportsExamService.professionalCompanyOrganization(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核分数情况
     * 省公司
     */
    @RequestMapping("/province-organization")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,averageScore,maxScore")
    public List<ReportsExamVO> provinceOrganization(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(PROVINCE_ORGANIZATION_CACHE_KEY + "#" + subjectId, () -> reportsExamService.provinceOrganization(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核情况
     * 省公司
     */
    @RequestMapping("/province-organization-assessment")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,examMemberCnt,assessmentParticipationRate,assessmentPassNumber,assessmentPassRate")
    public List<ReportsExamVO> provinceOrganizationAssessment(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(PROVINCE_ORGANIZATION_ASSESSMENT_CACHE_KEY + "#" + subjectId, () -> reportsExamService.provinceOrganizationAssessment(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核情况
     * 专业公司
     */
    @RequestMapping("/professional-company-organization-assessment")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,examMemberCnt,assessmentParticipationRate,assessmentPassNumber,assessmentPassRate")
    public List<ReportsExamVO> professionalCompanyOrganizationAssessment(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(PROFESSIONAL_COMPANY_ORGANIZATION_ASSESSMENT_CACHE_KEY + "#" + subjectId, () -> reportsExamService.professionalCompanyOrganizationAssessment(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核情况
     * 直属单位
     */
    @RequestMapping("/directly-affiliated-organization-assessment")
    @Param(name = "subjectId", required = true)
    @JSON("organizationId,organizationName,examMemberCnt,assessmentParticipationRate,assessmentPassNumber,assessmentPassRate")
    public List<ReportsExamVO> directlyAffiliatedOrganizationAssessment(RequestContext requestContext) {
        String subjectId = requestContext.getString("subjectId");
        return cache.get(DIRECTLY_AFFILIATED_ORGANIZATION_ASSESSMENT_CACHE_KEY + "#" + subjectId, () -> reportsExamService.directlyAffiliatedOrganizationAssessment(subjectId), EXPIRE_TIME);
    }

    /**
     * 各单位考核情况
     * 机构
     */
    @RequestMapping("/organization-assessment")
    @Param(name = "subjectId", required = true)
    @Param(name = "organizationId", required = true)
    @JSON("organizationId,organizationName,examMemberCnt,assessmentParticipationRate,assessmentPassNumber,assessmentPassRate,subOrganizationName")
    public List<ReportsExamVO> organizationAssessment(RequestContext requestContext) {
        return reportsExamService.organizationAssessment(requestContext.getString("subjectId"), requestContext.getString("organizationId"));
    }



    /**
     * 考核评估
     */
    @RequestMapping("/assessment")
    @Param(name = "subjectId", required = true)
    @Param(name = "organizationId", required = true)
    @JSON("organizationId,examMemberCnt,assessmentParticipationRate,assessmentPassNumber,assessmentPassRate,subjectId,averageScore,maxScore")
    public List<ReportsExamVO> assessment(RequestContext requestContext) {
        return reportsExamService.assessment(requestContext.getString("subjectId"), requestContext.getString("organizationId"));
    }

}
