/**
  提交人:魏传博
  提交时间:2017-11-13
 */
/*报名记录表增加字段：是否最新的报名记录0否 1是 */
ALTER TABLE exam.`t_signup_record` ADD COLUMN f_is_current TINYINT COMMENT '是否最新的报名记录0否 1是';
/*报名记录表增加字段：报名记录id */
ALTER TABLE exam.`t_signup_record` ADD COLUMN f_signup_id varchar(40) COMMENT '报名记录id';
/**
  提交人:魏传博
  提交时间:2017-11-13
 */
ALTER TABLE exam.t_question ADD COLUMN f_exam_num VARCHAR(100) NULL COMMENT '试题编号，同步试卷的时候落库，报错的时候返回第三方';

/**
  提交人:黄文威
  提交时间:2017-11-15
 */
/*考试表增加字段：切屏强制交卷人次 */
ALTER TABLE exam.`t_exam` ADD COLUMN f_switch_submit_person_time int COMMENT '切屏强制交卷人次';
/*考试表增加字段：强制交卷人数 */
ALTER TABLE exam.`t_exam` ADD COLUMN f_force_submit_time int COMMENT '强制交卷人数';
/*考试表增加字段：正常交卷人数 */
ALTER TABLE exam.`t_exam` ADD COLUMN f_normal_submit_time int COMMENT '正常交卷人数';
/*考试表增加字段：超时交卷人数 */
ALTER TABLE exam.`t_exam` ADD COLUMN f_over_time_submit_time int COMMENT '超时交卷人数';
/**
  提交人:曹彬
  提交时间:2017-11-15
 */
ALTER TABLE exam.t_question MODIFY f_exam_num VARCHAR(128) COMMENT '试题编号，同步试卷的时候落库，报错的时候返回第三方';
CREATE TABLE exam.t_error_reason
(
    f_id VARCHAR(40) PRIMARY KEY NOT NULL COMMENT '主键',
    f_error_reason VARCHAR(256) COMMENT '错误原因',
    f_member_id VARCHAR(40) COMMENT '对应人员表id',
    f_exam_num VARCHAR(128) COMMENT '试题编号'
);
ALTER TABLE exam.t_error_reason COMMENT = '试题报错原因表';
ALTER TABLE exam.t_error_reason ADD COLUMN f_create_time BIGINT(20) NULL COMMENT '创建时间';