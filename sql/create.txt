/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2016/12/24 11:37:29                          */
/*==============================================================*/







/*==============================================================*/
/* Table: t_expert                                              */
/*==============================================================*/
create table t_expert
(
   f_id                 national varchar(40) not null,
   f_member_id          national varchar(40) comment '用户id',
   f_type               int(1) default 0 comment '专家类别 0内部专家(默认) 1外部专家',
   f_topic              national varchar(2000) comment '关联话题id，多个逗号隔开',
   f_hiring_time        bigint(20) comment '聘用时间，初次为审核通过时间',
   f_answer_num         int(11) default 0 comment '回答数',
   f_care_num           int(11) default 0 comment '关注他的数',
   f_share_num          int(11) default 0 comment '分享数',
   f_active_status      int(1) default 0 comment '活动状态 0(草稿) 1活动(发布) 2冻结 3解聘 4离职',
   f_recommend          int(1) default 0 comment '是否推荐：0不推荐(默认)  1推荐',
   f_recommend_sort     int(11) comment '推荐排序',
   f_introduce          national varchar(2000) comment '专家介绍',
   f_name               national varchar(100) comment '外部专家名',
   f_org                national varchar(500) comment '外部专家机构',
   f_dismissal_description national varchar(500) comment '解聘理由',
   f_freeze_time        bigint(20) comment '冻结时间',
   f_create_time        bigint(20) comment '创建时间',
   f_audit_status       int(1) default 0 comment '审核状态：0 没通过  1通过',
   f_delete_flag        int(1) default 0 comment '删除状态：0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   primary key (f_id)
);

alter table t_expert comment '问吧专家表';

/*==============================================================*/
/* Index: f_member_id                                           */
/*==============================================================*/
create index f_member_id on t_expert
(
   f_member_id
);

/*==============================================================*/
/* Index: f_organization_id                                     */
/*==============================================================*/
create index f_organization_id on t_expert
(
   f_organization_id
);

/*==============================================================*/
/* Table: t_expert_audit                                        */
/*==============================================================*/
create table t_expert_audit
(
   f_id                 national varchar(40) not null,
   f_expert_id          national varchar(40) comment '用户id',
   f_advantage          national varchar(2000) comment '申请理由(专家优势 )',
   f_apply_time         bigint(20) comment '申请时间',
   f_apply_type         int(1) default 0 comment '申请类别：0身份审核  1更改话题',
   f_audit_note         national varchar(2000) comment '审核说明',
   f_audit_status       int(1) default 0 comment '审核状态： 0待审核(默认) 1通过 2拒绝',
   f_delete_flag        int(1) default 0 comment '删除状态：0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_audit_suggestion   national varchar(500) comment '审核意见',
   primary key (f_id)
);

alter table t_expert_audit comment '问吧专家审核表';

/*==============================================================*/
/* Table: t_expert_audit_topic                                  */
/*==============================================================*/
create table t_expert_audit_topic
(
   f_id                 national varchar(40) not null,
   f_expert_audit_id    national varchar(40) comment '专家审核id',
   f_topic_id           national varchar(40) comment '话题id',
   f_create_time        bigint(20),
   f_delete_flag        int(1) default 0 comment '删除状态：0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   primary key (f_id)
);

alter table t_expert_audit_topic comment '问吧专家变更擅长话题待审核表';

/*==============================================================*/
/* Table: t_expert_qualifications                               */
/*==============================================================*/
create table t_expert_qualifications
(
   f_id                 national varchar(40) not null,
   f_qualifications     national varchar(2000) comment '专家资质',
   f_enclosure_name     national varchar(100) comment '专家资质附件名称',
   f_enclosure_url      national varchar(100) comment '专家资质附件源文件地址',
   f_transfer_view_url  national varchar(100) comment '专家资质附件预览地址',
   f_transfer_flag      int(1) comment '专家资质附件转换状态',
   f_enclosure_suffix_img national varchar(100) comment '专家资质附件显示图标',
   f_enclosure_suffix   national varchar(100) comment '专家资质附件文件类型后缀',
   f_enclosure_type     int(1) comment '专家资质附件文件类型 1：文档  2：多媒体 3：Epub电子书',
   f_create_time        bigint(20) comment '修改时间',
   f_delete_flag        int(1) default 0 comment '删除状态：0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   primary key (f_id)
);

alter table t_expert_qualifications comment '问吧专家资质表';

/*==============================================================*/
/* Table: t_expert_topic_rel                                    */
/*==============================================================*/
create table t_expert_topic_rel
(
   f_id                 national varchar(40) not null,
   f_expert_id          national varchar(40) comment '专家id',
   f_topic_id           national varchar(40) comment '话题id',
   f_create_time        bigint(20) comment '创建时间',
   f_delete_flag        int(1) default 0 comment '删除状态0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   primary key (f_id)
);

alter table t_expert_topic_rel comment '问吧专家话题关联表';

/*==============================================================*/
/* Table: t_grant_detail                                        */
/*==============================================================*/
create table t_grant_detail
(
   f_id                 national varchar(40) not null comment 'ID',
   f_grant_id           national varchar(40) comment '授权ID',
   f_member_id          national varchar(40) not null comment '人员ID',
   f_organization_id    national varchar(40) comment '组织ID',
   f_create_time        bigint(20) comment '创建时间',
   f_uri                national varchar(40) comment 'uri',
   f_operator_types     national varchar(45) comment '操作类型',
   primary key (f_id)
);

/*==============================================================*/
/* Table: t_member                                              */
/*==============================================================*/
create table t_member
(
   f_id                 national varchar(40) not null comment 'ID',
   f_name               national varchar(20) comment '人员名称',
   f_organization_id    national varchar(45) comment '组织ID',
   f_create_time        bigint(20) comment '创建时间',
   f_full_name          national varchar(45) comment '姓名',
   f_head_portrait      national varchar(200) comment '用户头象',
   primary key (f_id)
);

alter table t_member comment '用户表';

/*==============================================================*/
/* Table: t_organization                                        */
/*==============================================================*/
create table t_organization
(
   f_id                 national varchar(40) not null comment 'ID',
   f_name               national varchar(45) comment '组织名称',
   f_parent_id          national varchar(45) comment '上级组织',
   f_level              int(1) comment '级别',
   f_create_time        bigint(20) comment '创建时间',
   f_code               national varchar(45) comment '编码',
   f_status             int(2) comment '状态',
   f_company_id         national varchar(45) comment '所属机构',
   f_order              int(5) comment '顺序',
   primary key (f_id)
);

/*==============================================================*/
/* Table: t_organization_detail                                 */
/*==============================================================*/
create table t_organization_detail
(
   f_id                 national varchar(40) not null comment 'ID',
   f_root               national varchar(45) comment '父节点',
   f_sub                national varchar(40) comment '子节点',
   f_create_time        bigint(20) comment '创建时间',
   primary key (f_id)
);

/*==============================================================*/
/* Table: t_question                                            */
/*==============================================================*/
create table t_question
(
   f_id                 national varchar(40) not null,
   f_title              national varchar(200) comment '标题',
   f_type               int(1) comment '问题类型：1问题 2分享',
   f_flag               int(1) comment '状态：1活动2关闭',
   f_topic              national varchar(2000) comment '关联话题id，多个逗号隔开',
   f_content            national varchar(2000) comment '内容',
   f_json_img           national varchar(2000) comment '内容图片',
   f_content_txt        national varchar(6000),
   f_json_attach        national varchar(2000) comment '内容附件',
   f_create_member_id   national varchar(40) comment '创建人id',
   f_create_time        bigint(20) comment '创建时间',
   f_last_modify_time   bigint(20) comment '修改时间',
   f_browse_num         int(11) default 0 comment '浏览量 默认0',
   f_discuss_num        int(11) default 0 comment '讨论数 默认0',
   f_forward_num        int(11) default 0 comment '转发数',
   f_praise_num         int(11) default 0 comment '点赞数',
   f_collect_num        int(11) default 0 comment '收藏数',
   f_care_num           int(11) default 0 comment '关注数',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_accuse_status      int(1) default 0 comment '举报状态：0未举报默认 1被举报',
   f_share_has_cover    int(1) default 0 comment '分享是否有封面,0:无,1:有',
   f_share_object_id    national varchar(100) comment '分享对象的id',
   f_share_type         national varchar(2) default '0' comment '分享类型:1:课程分享2:学习路径3:知识分享4:班级分享5:调研分享6:微课大赛分享7:考试分享8:专题分享9:直播分享',
   f_share_title        national varchar(200) comment '分享标题',
   f_essence_status     int(1) default 0 comment '是否加精   0非精品（默认） 1精品',
   f_close_status       int(1) default 0 comment '关闭状态 0未关闭(默认)  1已关闭',
   f_top_status         int(1) default 0 comment '顶置状态 0未项置(默认) 1已顶置',
   f_top_time           bigint(20) comment '顶置时间',
   f_delete_flag        national varchar(1) default '0' comment '删除状态 0未删除(默认)  1已删除',
   primary key (f_id)
);

alter table t_question comment '问道问题表';


/*==============================================================*/
/* Table: t_question_attach                                     */
/*==============================================================*/
create table t_question_attach
(
   f_id                 national varchar(40) not null,
   f_rel_id             national varchar(40) comment '关联的id，问题，评论，回复等的id',
   f_rel_object_type    int(1) comment '关联对象类型 1问题 2问题补充 3讨论 4回复',
   f_name               national varchar(100) comment '文件名，上传时的名',
   f_original_file      national varchar(100) comment '上传到服务器地址转换前的名，用于下载',
   f_view_name          national varchar(100) comment '转换后的文件地址，用于预览',
   f_suffix             national varchar(100) comment '文档后缀类型',
   f_suffix_img         national varchar(100) comment '文档后缀类型表示图片的路径',
   f_type               int(1) comment '文件类型： 1图片 2文档 3视频音频',
   f_transfer_flag      int(1) comment 'mq转换状态： 0、转换中，1、转换成功，2、转换失败',
   f_create_member_id   national varchar(40) comment '创建人',
   f_download_num       int(11) default 0 comment '下载量',
   f_view_num           int(11) default 0 comment '浏览量',
   f_create_time        bigint(20) comment '创建时间',
   f_delete_flag        int(1) default 0 comment '删除状态 0未删除(默认)  1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_thumbnail          national varchar(100) comment '缩略图',
   f_size               national varchar(100) comment '文件大小',
   primary key (f_id)
);

alter table t_question_attach comment '问道问题附件表';

/*==============================================================*/
/* Table: t_question_discuss                                    */
/*==============================================================*/
create table t_question_discuss
(
   f_id                 national varchar(40) not null,
   f_question_id        national varchar(40) not null comment '问题id',
   f_content            national varchar(2000) comment '回复内容',
   f_json_attach        national varchar(2000) comment '附件冗余字段',
   f_reply_num          int(11) default 0 comment '回复数 默认0',
   f_praise_num         int(11) default 0 comment '点赞数 默认0',
   f_audit_status       national varchar(1) default '0' comment '审核状态 0未审核(默认)   1通过  2拒绝',
   f_essence_status     national varchar(1) default '0' comment '是否加精 0否(默认) 1是',
   f_top_status         national varchar(1) default '0' comment '顶置状态 0未项置(默认) 1已顶置',
   f_top_time           bigint(20) comment '顶置时间',
   f_create_member_id   national varchar(40) comment '创建人id',
   f_create_time        bigint(20) comment '创建时间',
   f_delete_flag        int(1) default 0 comment '删除状态 0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_content_txt        national varchar(6000),
   f_accuse_status      int(1) default 0 comment '举报状态：0未举报默认 1被举报',
   f_read_status        int(1) default 0 comment '阅读状态：0未阅读(默认) 1已阅读',
   primary key (f_id, f_question_id)
);

alter table t_question_discuss comment '问道问题讨论表';

/*==============================================================*/
/* Table: t_question_reply                                      */
/*==============================================================*/
create table t_question_reply
(
   f_id                 national varchar(40) not null,
   f_question_id        national varchar(40) not null comment '问题id',
   f_content            national varchar(2000) comment '回复内容',
   f_discuss_id         national varchar(40) comment '讨论id',
   f_to_user_id         national varchar(40) comment '被回复的人id',
   f_praise_num         int(11) default 0 comment '点赞数 默认0',
   f_audit_status       int(1) default 0 comment '审核状态 0未审核(默认)   1通过  2拒绝',
   f_create_member_id   national varchar(40) comment '创建人id',
   f_create_time        bigint(20) comment '创建时间',
   f_delete_flag        int(1) default 0 comment '删除状态 0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_accuse_status      int(1) default 0 comment '举报状态：0未举报默认 1被举报',
   f_read_status        int(1) default 0 comment '阅读状态：0未阅读(默认) 1已阅读',
   primary key (f_id, f_question_id)
);

alter table t_question_reply comment '问道问题回复表';

/*==============================================================*/
/* Table: t_question_reviewed                                   */
/*==============================================================*/
create table t_question_reviewed
(
   f_id                 national varchar(40) not null,
   t_q_f_question_id    varchar(40) not null comment '问题id',
   f_question_id        national varchar(40) not null comment '问题id',
   f_audit_type         national varchar(2) default '0' comment '审核类型：1提问审核 2讨论审核 3回复审核 4提问举报审核 5讨论举报审核 6回复举报审核7补充内容审核8讨论区评论审核9讨论区回复审核10讨论区评论举报审核11讨论区回复举报审核',
   f_object_id          national varchar(40) comment '审核对象id',
   f_content            national varchar(6000) comment '审核内容',
   f_create_user_id     national varchar(40) comment '提交人',
   f_create_time        bigint(20) comment '审核时间',
   f_audit_user_id      national varchar(40) comment '审核人',
   f_audit_time         bigint(20) comment '审核时间',
   f_audit_status       int(1) default 0 comment '审核状态：0待审核(默认)  1通过 2拒绝',
   f_audit_note         national varchar(1000) comment '审核说明',
   f_delete_flag        int(1) default 0 comment '删除状态：0未删除(默认) 1已删除',
   f_organization_id    national varchar(40) not null comment '组织id',
   f_accuse_num         int(11) default 0 comment '举报次数',
   f_be_user_id         national varchar(40) comment '被举报人id',
   f_source_type        national varchar(40) comment '来源：1问吧 2知识 3课程 4学习',
   f_need_audit         int(1) default 0 comment '是否需要审核 0不需要（默认） 1需要',
   primary key (f_id, t_q_f_question_id)
);

alter table t_question_reviewed comment '问道问题审核表';

/*==============================================================*/
/* Table: t_topic                                               */
/*==============================================================*/
create table t_topic
(
   f_id                 national varchar(40) not null comment '主键',
   f_name               national varchar(20) comment '话题名称',
   f_cover_url          national varchar(200) comment '封面图片路径',
   f_type_id            national varchar(40) comment '话题类别ID',
   f_state              int(1) comment '话题状态（启用1，禁用0）',
   f_description        national varchar(3000) comment '话题描述',
   f_source             int(2) comment '来源（1管理员 2用户）',
   f_publish_member_id  national varchar(40) comment '发布人',
   f_publish_time       bigint(20) comment '发布时间',
   f_recommend          int(1) comment '初次登录推荐（推荐1，不推荐0）',
   f_create_member_id   national varchar(40) comment '创建人',
   f_create_time        bigint(20) comment '创建时间',
   f_delete_flag        int(1) comment '删除标记（0未删除，1已删除）',
   primary key (f_id)
);

alter table t_expert_audit add constraint FK_Reference_16 foreign key (f_id)
      references t_expert (f_id);

alter table t_expert_audit_topic add constraint FK_Reference_17 foreign key (f_id)
      references t_expert (f_id);

alter table t_expert_topic_rel add constraint FK_Reference_25 foreign key (f_expert_id)
      references t_expert (f_id);

alter table t_question_attach add constraint FK_Reference_2 foreign key (f_id)
      references t_question (f_id);

alter table t_question_discuss add constraint FK_Reference_1 foreign key (f_question_id)
      references t_question (f_id);

alter table t_question_reply add constraint FK_Reference_10 foreign key (f_discuss_id, f_question_id)
      references t_question_discuss (f_id, f_question_id);

alter table t_question_reviewed add constraint FK_Reference_7 foreign key (f_id)
      references t_question (f_id);

alter table t_question_reviewed add constraint FK_Reference_8 foreign key (f_organization_id, f_question_id)
      references t_question_discuss (f_id, f_question_id);

alter table t_question_reviewed add constraint FK_Reference_9 foreign key (f_id, f_question_id)
      references t_question_reply (f_id, f_question_id);
