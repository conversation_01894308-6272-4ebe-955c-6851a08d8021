package com.zxy.product.askbar.api;


import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.askbar.entity.SimilarAsk;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RemoteService
public interface SimilarService {

    /**
     * 根据faqId查询相似问列表
     * @param faqId  faq问题id
     * @return
     */
    @Transactional(readOnly = true)
    List<SimilarAsk> findSimilarAsk(String faqId);

    /**
     * 保存相似问
     * @param faqId  faqId
     * @param question 问题描述
     * @return
     */
    @Transactional
    Integer saveSimilarAsk(String faqId,List<String> question,String memberId);

    /**
     * 根据id删除相似问
     * @param id
     * @return
     */
    @Transactional
    Integer deleteById(String id);


    /**
     * 根据faqId删除所属相似问
     * @param faqId
     * @return
     */
    @Transactional
    Integer deleteByFaqId(String faqId);

    /**
     * 根据faqId查询所有的相似问
     * @return
     */
    @Transactional(readOnly = true)
    List<SimilarAsk> findAllSimilarAsk(List<String> list);



    @Transactional(readOnly = true)
    Integer findSimilarAskByQuestion(List<String> list);

}
