package com.zxy.product.askbar.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.askbar.entity.Attention;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 关注
 */
@RemoteService(timeout = 60000)
public interface AttentionService {

    @Transactional
    Attention insert(String businessId, Integer businessType, String organizationId, String createMemberId);

    /**
     * 插入后不进行其他系统的同步
     */
    @Transactional
    Attention insertNotSync(String businessId, Integer businessType, String organizationId, String createMemberId);

    @Transactional
    Integer delete(String id, String memberId);

    @Transactional
    Integer deleteNotSync(String id, String memberId);

    /**
     * 根据传进来的businessId查询是否关注，0未关注 1已关注
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String getIsAttention(String businessId, String memberId);

    /** 根据人员id查找是否关注过专家、话题-与我相关页面判断用 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Integer> findUserAttentionCount(String currentUserId, String rootOrganizationId);

    /** 批量关注：支持同时关注话题和专家 */
    @Transactional
    void insertBatch(Optional<String> topics, Optional<String> experts, String organizationId,
            String currentUserId);

    /**
     * 查询人员关注的业务列表
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Attention> findByMemberId(String memberId, Optional<Integer> businessType);

}
