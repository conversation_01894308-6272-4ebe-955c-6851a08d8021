/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables;


import com.zxy.product.askbar.jooq.AskBar;
import com.zxy.product.askbar.jooq.Keys;
import com.zxy.product.askbar.jooq.tables.records.TrendRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 动态表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Trend extends TableImpl<TrendRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>ask-bar.t_trend</code>
     */
    public static final Trend TREND = new Trend();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TrendRecord> getRecordType() {
        return TrendRecord.class;
    }

    /**
     * The column <code>ask-bar.t_trend.f_id</code>. 主键id
     */
    public final TableField<TrendRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键id");

    /**
     * The column <code>ask-bar.t_trend.f_business_id</code>. 业务id
     */
    public final TableField<TrendRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "业务id");

    /**
     * The column <code>ask-bar.t_trend.f_business_type</code>. 业务类型:1问题 2文章 3分享 4讨论
     */
    public final TableField<TrendRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER, this, "业务类型:1问题 2文章 3分享 4讨论");

    /**
     * The column <code>ask-bar.t_trend.f_question_id</code>. 问题id
     */
    public final TableField<TrendRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问题id");

    /**
     * The column <code>ask-bar.t_trend.f_discuss_id</code>. 讨论id
     */
    public final TableField<TrendRecord, String> DISCUSS_ID = createField("f_discuss_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讨论id");

    /**
     * The column <code>ask-bar.t_trend.f_create_member_id</code>. 创建人id
     */
    public final TableField<TrendRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * The column <code>ask-bar.t_trend.f_create_time</code>. 创建时间
     */
    public final TableField<TrendRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>ask-bar.t_trend.f_root_organization_id</code>. 根组织id
     */
    public final TableField<TrendRecord, String> ROOT_ORGANIZATION_ID = createField("f_root_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "根组织id");

    /**
     * The column <code>ask-bar.t_trend.f_is_current</code>. 是否为最新动态，1是、0否
     */
    public final TableField<TrendRecord, Integer> IS_CURRENT = createField("f_is_current", org.jooq.impl.SQLDataType.INTEGER, this, "是否为最新动态，1是、0否");

    /**
     * Create a <code>ask-bar.t_trend</code> table reference
     */
    public Trend() {
        this("t_trend", null);
    }

    /**
     * Create an aliased <code>ask-bar.t_trend</code> table reference
     */
    public Trend(String alias) {
        this(alias, TREND);
    }

    private Trend(String alias, Table<TrendRecord> aliased) {
        this(alias, aliased, null);
    }

    private Trend(String alias, Table<TrendRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "动态表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return AskBar.ASK_BAR_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TrendRecord> getPrimaryKey() {
        return Keys.KEY_T_TREND_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TrendRecord>> getKeys() {
        return Arrays.<UniqueKey<TrendRecord>>asList(Keys.KEY_T_TREND_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Trend as(String alias) {
        return new Trend(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Trend rename(String name) {
        return new Trend(name, null);
    }
}
