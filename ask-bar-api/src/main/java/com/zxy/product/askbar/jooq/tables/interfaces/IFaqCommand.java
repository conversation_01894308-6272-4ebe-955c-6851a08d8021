/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 口令配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IFaqCommand extends Serializable {

    /**
     * Setter for <code>ask-bar.t_faq_command.f_id</code>. 主键id
     */
    public void setId(String value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_id</code>. 主键id
     */
    public String getId();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_title</code>. 主题
     */
    public void setTitle(String value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_title</code>. 主题
     */
    public String getTitle();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_url</code>. 跳转URL
     */
    public void setUrl(String value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_url</code>. 跳转URL
     */
    public String getUrl();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_status</code>. 状态0：未发布1：已发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_status</code>. 状态0：未发布1：已发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>ask-bar.t_faq_command.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>ask-bar.t_faq_command.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IFaqCommand
     */
    public void from(com.zxy.product.askbar.jooq.tables.interfaces.IFaqCommand from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IFaqCommand
     */
    public <E extends com.zxy.product.askbar.jooq.tables.interfaces.IFaqCommand> E into(E into);
}
