/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables;


import com.zxy.product.askbar.jooq.AskBar;
import com.zxy.product.askbar.jooq.Keys;
import com.zxy.product.askbar.jooq.tables.records.IssueEvaluateRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 问题评价表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IssueEvaluate extends TableImpl<IssueEvaluateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>ask-bar.t_issue_evaluate</code>
     */
    public static final IssueEvaluate ISSUE_EVALUATE = new IssueEvaluate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IssueEvaluateRecord> getRecordType() {
        return IssueEvaluateRecord.class;
    }

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_id</code>.
     */
    public final TableField<IssueEvaluateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_member_id</code>. 提问人
     */
    public final TableField<IssueEvaluateRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "提问人");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_praise_or_trample</code>. 赞=1,踩=2
     */
    public final TableField<IssueEvaluateRecord, Integer> PRAISE_OR_TRAMPLE = createField("f_praise_or_trample", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "赞=1,踩=2");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_issue</code>. 问题
     */
    public final TableField<IssueEvaluateRecord, String> ISSUE = createField("f_issue", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问题");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_answer</code>. 回答
     */
    public final TableField<IssueEvaluateRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "回答");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_create_time</code>. 创建时间
     */
    public final TableField<IssueEvaluateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>ask-bar.t_issue_evaluate.f_quiz_date</code>. 提问时间
     */
    public final TableField<IssueEvaluateRecord, Long> QUIZ_DATE = createField("f_quiz_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提问时间");

    /**
     * Create a <code>ask-bar.t_issue_evaluate</code> table reference
     */
    public IssueEvaluate() {
        this("t_issue_evaluate", null);
    }

    /**
     * Create an aliased <code>ask-bar.t_issue_evaluate</code> table reference
     */
    public IssueEvaluate(String alias) {
        this(alias, ISSUE_EVALUATE);
    }

    private IssueEvaluate(String alias, Table<IssueEvaluateRecord> aliased) {
        this(alias, aliased, null);
    }

    private IssueEvaluate(String alias, Table<IssueEvaluateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "问题评价表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return AskBar.ASK_BAR_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<IssueEvaluateRecord> getPrimaryKey() {
        return Keys.KEY_T_ISSUE_EVALUATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<IssueEvaluateRecord>> getKeys() {
        return Arrays.<UniqueKey<IssueEvaluateRecord>>asList(Keys.KEY_T_ISSUE_EVALUATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IssueEvaluate as(String alias) {
        return new IssueEvaluate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IssueEvaluate rename(String name) {
        return new IssueEvaluate(name, null);
    }
}
