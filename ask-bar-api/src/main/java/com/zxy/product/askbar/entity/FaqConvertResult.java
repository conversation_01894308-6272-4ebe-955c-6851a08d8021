package com.zxy.product.askbar.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: ask-bar
 * @Package: com.zxy.product.askbar.entity
 * @ClassName: FaqConvertResult
 * @Author: futzh
 * @Description: faq转入问题库返回对象
 * @Date: 2020/12/2 13:11
 * @Version: 1.0
 */
public class FaqConvertResult  implements Serializable {
    private static final long serialVersionUID = 2611156289017813255L;
    private Integer flag;
    private String msg;
    private List<String> successUuIds;
    private List<String> errorUuIds;

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<String> getSuccessUuIds() {
        return successUuIds;
    }

    public void setSuccessUuIds(List<String> successUuIds) {
        this.successUuIds = successUuIds;
    }

    public List<String> getErrorUuIds() {
        return errorUuIds;
    }

    public void setErrorUuIds(List<String> errorUuIds) {
        this.errorUuIds = errorUuIds;
    }
}
