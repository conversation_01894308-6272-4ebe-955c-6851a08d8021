/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 专家工作室表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudio extends Serializable {

    /**
     * Setter for <code>ask-bar.t_studio.f_id</code>. 主键ID
     */
    public void setId(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_id</code>. 主键ID
     */
    public String getId();

    /**
     * Setter for <code>ask-bar.t_studio.f_name</code>. 工作室名称
     */
    public void setName(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_name</code>. 工作室名称
     */
    public String getName();

    /**
     * Setter for <code>ask-bar.t_studio.f_organization_id</code>. 归属组织机构id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_organization_id</code>. 归属组织机构id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>ask-bar.t_studio.f_status</code>. 状态  所有内容需关联只查启用状态的数据（0：禁用，1：启用）
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_status</code>. 状态  所有内容需关联只查启用状态的数据（0：禁用，1：启用）
     */
    public Integer getStatus();

    /**
     * Setter for <code>ask-bar.t_studio.f_recommend_flag</code>. 是否推荐(0：否,1：是)
     */
    public void setRecommendFlag(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_recommend_flag</code>. 是否推荐(0：否,1：是)
     */
    public Integer getRecommendFlag();

    /**
     * Setter for <code>ask-bar.t_studio.f_sequence</code>. 排序，推荐业务使用
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_sequence</code>. 排序，推荐业务使用
     */
    public Integer getSequence();

    /**
     * Setter for <code>ask-bar.t_studio.f_property</code>. 工作室属性(1：首席科学家 2：首席专家 3：省级专家 4：其他专家)
     */
    public void setProperty(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_property</code>. 工作室属性(1：首席科学家 2：首席专家 3：省级专家 4：其他专家)
     */
    public Integer getProperty();

    /**
     * Setter for <code>ask-bar.t_studio.f_positional_title</code>. 专家职称
     */
    public void setPositionalTitle(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_positional_title</code>. 专家职称
     */
    public String getPositionalTitle();

    /**
     * Setter for <code>ask-bar.t_studio.f_maxim</code>. 工作室格言
     */
    public void setMaxim(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_maxim</code>. 工作室格言
     */
    public String getMaxim();

    /**
     * Setter for <code>ask-bar.t_studio.f_introduction</code>. 工作室简介富文本
     */
    public void setIntroduction(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_introduction</code>. 工作室简介富文本
     */
    public String getIntroduction();

    /**
     * Setter for <code>ask-bar.t_studio.f_introduction_txt</code>. 工作室简介
     */
    public void setIntroductionTxt(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_introduction_txt</code>. 工作室简介
     */
    public String getIntroductionTxt();

    /**
     * Setter for <code>ask-bar.t_studio.f_cover_id</code>. 封面id
     */
    public void setCoverId(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_cover_id</code>. 封面id
     */
    public String getCoverId();

    /**
     * Setter for <code>ask-bar.t_studio.f_cover_path</code>. 封面path
     */
    public void setCoverPath(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_cover_path</code>. 封面path
     */
    public String getCoverPath();

    /**
     * Setter for <code>ask-bar.t_studio.f_portrait_id</code>. 头像id
     */
    public void setPortraitId(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_portrait_id</code>. 头像id
     */
    public String getPortraitId();

    /**
     * Setter for <code>ask-bar.t_studio.f_portrait_path</code>. 头像path
     */
    public void setPortraitPath(String value);

    /**
     * Getter for <code>ask-bar.t_studio.f_portrait_path</code>. 头像path
     */
    public String getPortraitPath();

    /**
     * Setter for <code>ask-bar.t_studio.f_content_num</code>. 内容数
     */
    public void setContentNum(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_content_num</code>. 内容数
     */
    public Integer getContentNum();

    /**
     * Setter for <code>ask-bar.t_studio.f_popular</code>. 人气值(定时任务算)
     */
    public void setPopular(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_popular</code>. 人气值(定时任务算)
     */
    public Integer getPopular();

    /**
     * Setter for <code>ask-bar.t_studio.f_delete_flag</code>. 是否删除(0：否,1：是)
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_delete_flag</code>. 是否删除(0：否,1：是)
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>ask-bar.t_studio.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>ask-bar.t_studio.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>ask-bar.t_studio.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>ask-bar.t_studio.f_modify_date</code>. 更新时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>ask-bar.t_studio.f_order</code>. 顺序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_order</code>. 顺序
     */
    public Integer getOrder();

    /**
     * Setter for <code>ask-bar.t_studio.f_hide</code>. 隐藏 0 隐藏 1 显示
     */
    public void setHide(Integer value);

    /**
     * Getter for <code>ask-bar.t_studio.f_hide</code>. 隐藏 0 隐藏 1 显示
     */
    public Integer getHide();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudio
     */
    public void from(IStudio from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudio
     */
    public <E extends IStudio> E into(E into);
}
