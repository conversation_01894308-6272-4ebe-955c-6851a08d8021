package com.zxy.product.report.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.report.entity.TeachRecord;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.Optional;

/**
 * Created by y<PERSON>uan on 9/6/17.
 */
@RemoteService(timeout = 2000)
public interface TeachRecordService {
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<TeachRecord> find(int page, int pageSize, String userId, Optional<String> teacherName,
                                  Optional<String> organizationId, Optional<String> courseName,
                                  Optional<String> className, Optional<Date> startTime,
                                  Optional<Date> endTime, Optional<Integer> studentCount,
                                  Optional<Double> teacherEvaluate);
}
