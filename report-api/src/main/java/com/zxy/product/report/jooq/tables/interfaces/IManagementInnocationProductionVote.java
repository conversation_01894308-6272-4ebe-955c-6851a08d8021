/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 管理创新成果投票表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IManagementInnocationProductionVote extends Serializable {

    /**
     * Setter for <code>report.t_management_innocation_production_vote.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_management_innocation_production_vote.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>report.t_management_innocation_production_vote.f_member_id</code>. 投票人id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>report.t_management_innocation_production_vote.f_member_id</code>. 投票人id
     */
    public String getMemberId();

    /**
     * Setter for <code>report.t_management_innocation_production_vote.f_management_innocation_production_id</code>. 成果id
     */
    public void setManagementInnocationProductionId(String value);

    /**
     * Getter for <code>report.t_management_innocation_production_vote.f_management_innocation_production_id</code>. 成果id
     */
    public String getManagementInnocationProductionId();

    /**
     * Setter for <code>report.t_management_innocation_production_vote.f_vote_number</code>. 所在投票次数
     */
    public void setVoteNumber(Integer value);

    /**
     * Getter for <code>report.t_management_innocation_production_vote.f_vote_number</code>. 所在投票次数
     */
    public Integer getVoteNumber();

    /**
     * Setter for <code>report.t_management_innocation_production_vote.f_type</code>. 模式 1:十佳 2:实践推广
     */
    public void setType(Integer value);

    /**
     * Getter for <code>report.t_management_innocation_production_vote.f_type</code>. 模式 1:十佳 2:实践推广
     */
    public Integer getType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IManagementInnocationProductionVote
     */
    public void from(com.zxy.product.report.jooq.tables.interfaces.IManagementInnocationProductionVote from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IManagementInnocationProductionVote
     */
    public <E extends com.zxy.product.report.jooq.tables.interfaces.IManagementInnocationProductionVote> E into(E into);
}
