package com.zxy.product.report.api.ihr;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.report.entity.IhrSyncItemMember;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService
public interface IhrSyncItemMemberService extends IhrAbstractSyncItemService<IhrSyncItemMember>{

    /**
     * 同步批次分页
     * @param page          页码
     * @param pageSize      每页行数
     * @param operation     操作类型
     * @param checkStatus   校验状态
     * @param fileName      文件名
     * @return  List集合
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<IhrSyncItemMember> findPage(Integer page, Integer pageSize, Optional<Integer> operation, Optional<Integer> checkStatus, Optional<String> fileName);

    /**
     * 获取要同步的Map<操作类型,数量>
     * @param fileName      文件名
     * @param checkStatus   校验状态
     * @return  Map<操作类型,数量>
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<Integer, Integer> syncItemDataCount(String fileName, Integer checkStatus);
}
