/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.report.jooq.tables.interfaces.IPracticeHistory;

import javax.annotation.Generated;


/**
 * 实践案例归档表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PracticeHistoryEntity extends BaseEntity implements IPracticeHistory {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  imgPath;
    private Integer type;
    private String  organizationId;
    private String  organizationName;
    private Integer upperNumber;
    private Integer totalVotes;
    private Integer ownVotes;
    private Integer validVotes;
    private Long    lastVoteTime;
    private Integer order;
    private Integer date;

    public PracticeHistoryEntity() {}

    public PracticeHistoryEntity(PracticeHistoryEntity value) {
        this.name = value.name;
        this.imgPath = value.imgPath;
        this.type = value.type;
        this.organizationId = value.organizationId;
        this.organizationName = value.organizationName;
        this.upperNumber = value.upperNumber;
        this.totalVotes = value.totalVotes;
        this.ownVotes = value.ownVotes;
        this.validVotes = value.validVotes;
        this.lastVoteTime = value.lastVoteTime;
        this.order = value.order;
        this.date = value.date;
    }

    public PracticeHistoryEntity(
        String  id,
        String  name,
        String  imgPath,
        Integer type,
        String  organizationId,
        String  organizationName,
        Integer upperNumber,
        Integer totalVotes,
        Integer ownVotes,
        Integer validVotes,
        Long    lastVoteTime,
        Integer order,
        Long    createTime,
        Integer date
    ) {
        super.setId(id);
        this.name = name;
        this.imgPath = imgPath;
        this.type = type;
        this.organizationId = organizationId;
        this.organizationName = organizationName;
        this.upperNumber = upperNumber;
        this.totalVotes = totalVotes;
        this.ownVotes = ownVotes;
        this.validVotes = validVotes;
        this.lastVoteTime = lastVoteTime;
        this.order = order;
        super.setCreateTime(createTime);
        this.date = date;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getImgPath() {
        return this.imgPath;
    }

    @Override
    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getOrganizationName() {
        return this.organizationName;
    }

    @Override
    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    @Override
    public Integer getUpperNumber() {
        return this.upperNumber;
    }

    @Override
    public void setUpperNumber(Integer upperNumber) {
        this.upperNumber = upperNumber;
    }

    @Override
    public Integer getTotalVotes() {
        return this.totalVotes;
    }

    @Override
    public void setTotalVotes(Integer totalVotes) {
        this.totalVotes = totalVotes;
    }

    @Override
    public Integer getOwnVotes() {
        return this.ownVotes;
    }

    @Override
    public void setOwnVotes(Integer ownVotes) {
        this.ownVotes = ownVotes;
    }

    @Override
    public Integer getValidVotes() {
        return this.validVotes;
    }

    @Override
    public void setValidVotes(Integer validVotes) {
        this.validVotes = validVotes;
    }

    @Override
    public Long getLastVoteTime() {
        return this.lastVoteTime;
    }

    @Override
    public void setLastVoteTime(Long lastVoteTime) {
        this.lastVoteTime = lastVoteTime;
    }

    @Override
    public Integer getOrder() {
        return this.order;
    }

    @Override
    public void setOrder(Integer order) {
        this.order = order;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getDate() {
        return this.date;
    }

    @Override
    public void setDate(Integer date) {
        this.date = date;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PracticeHistoryEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(imgPath);
        sb.append(", ").append(type);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(organizationName);
        sb.append(", ").append(upperNumber);
        sb.append(", ").append(totalVotes);
        sb.append(", ").append(ownVotes);
        sb.append(", ").append(validVotes);
        sb.append(", ").append(lastVoteTime);
        sb.append(", ").append(order);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(date);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPracticeHistory from) {
        setId(from.getId());
        setName(from.getName());
        setImgPath(from.getImgPath());
        setType(from.getType());
        setOrganizationId(from.getOrganizationId());
        setOrganizationName(from.getOrganizationName());
        setUpperNumber(from.getUpperNumber());
        setTotalVotes(from.getTotalVotes());
        setOwnVotes(from.getOwnVotes());
        setValidVotes(from.getValidVotes());
        setLastVoteTime(from.getLastVoteTime());
        setOrder(from.getOrder());
        setCreateTime(from.getCreateTime());
        setDate(from.getDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPracticeHistory> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PracticeHistoryEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.report.jooq.tables.records.PracticeHistoryRecord r = new com.zxy.product.report.jooq.tables.records.PracticeHistoryRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ID, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.NAME, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.IMG_PATH.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.IMG_PATH, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.IMG_PATH));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TYPE, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_ID, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_NAME, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORGANIZATION_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.UPPER_NUMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.UPPER_NUMBER, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.UPPER_NUMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TOTAL_VOTES.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TOTAL_VOTES, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.TOTAL_VOTES));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.OWN_VOTES.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.OWN_VOTES, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.OWN_VOTES));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.VALID_VOTES.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.VALID_VOTES, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.VALID_VOTES));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.LAST_VOTE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.LAST_VOTE_TIME, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.LAST_VOTE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORDER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORDER, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.ORDER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.CREATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.DATE, record.getValue(com.zxy.product.report.jooq.tables.PracticeHistory.PRACTICE_HISTORY.DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
