/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.report.jooq.tables.interfaces.IVoteCampaigners;

import javax.annotation.Generated;


/**
 * 竞选者
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class VoteCampaignersEntity extends BaseEntity implements IVoteCampaigners {

    private static final long serialVersionUID = 1L;

    private String  userId;
    private String  userName;
    private String  organizationId;
    private String  organizationName;
    private String  slogan;
    private String  headImg;
    private Integer votes;
    private String  briefTitle;
    private String  briefContent;
    private String  deeds;
    private Long    updateTime;

    public VoteCampaignersEntity() {}

    public VoteCampaignersEntity(VoteCampaignersEntity value) {
        this.userId = value.userId;
        this.userName = value.userName;
        this.organizationId = value.organizationId;
        this.organizationName = value.organizationName;
        this.slogan = value.slogan;
        this.headImg = value.headImg;
        this.votes = value.votes;
        this.briefTitle = value.briefTitle;
        this.briefContent = value.briefContent;
        this.deeds = value.deeds;
        this.updateTime = value.updateTime;
    }

    public VoteCampaignersEntity(
        String  id,
        String  userId,
        String  userName,
        String  organizationId,
        String  organizationName,
        String  slogan,
        String  headImg,
        Integer votes,
        String  briefTitle,
        String  briefContent,
        String  deeds,
        Long    createTime,
        Long    updateTime
    ) {
        super.setId(id);
        this.userId = userId;
        this.userName = userName;
        this.organizationId = organizationId;
        this.organizationName = organizationName;
        this.slogan = slogan;
        this.headImg = headImg;
        this.votes = votes;
        this.briefTitle = briefTitle;
        this.briefContent = briefContent;
        this.deeds = deeds;
        super.setCreateTime(createTime);
        this.updateTime = updateTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getUserId() {
        return this.userId;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String getUserName() {
        return this.userName;
    }

    @Override
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getOrganizationName() {
        return this.organizationName;
    }

    @Override
    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    @Override
    public String getSlogan() {
        return this.slogan;
    }

    @Override
    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    @Override
    public String getHeadImg() {
        return this.headImg;
    }

    @Override
    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    @Override
    public Integer getVotes() {
        return this.votes;
    }

    @Override
    public void setVotes(Integer votes) {
        this.votes = votes;
    }

    @Override
    public String getBriefTitle() {
        return this.briefTitle;
    }

    @Override
    public void setBriefTitle(String briefTitle) {
        this.briefTitle = briefTitle;
    }

    @Override
    public String getBriefContent() {
        return this.briefContent;
    }

    @Override
    public void setBriefContent(String briefContent) {
        this.briefContent = briefContent;
    }

    @Override
    public String getDeeds() {
        return this.deeds;
    }

    @Override
    public void setDeeds(String deeds) {
        this.deeds = deeds;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getUpdateTime() {
        return this.updateTime;
    }

    @Override
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("VoteCampaignersEntity (");

        sb.append(getId());
        sb.append(", ").append(userId);
        sb.append(", ").append(userName);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(organizationName);
        sb.append(", ").append(slogan);
        sb.append(", ").append(headImg);
        sb.append(", ").append(votes);
        sb.append(", ").append(briefTitle);
        sb.append(", ").append(briefContent);
        sb.append(", ").append(deeds);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(updateTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IVoteCampaigners from) {
        setId(from.getId());
        setUserId(from.getUserId());
        setUserName(from.getUserName());
        setOrganizationId(from.getOrganizationId());
        setOrganizationName(from.getOrganizationName());
        setSlogan(from.getSlogan());
        setHeadImg(from.getHeadImg());
        setVotes(from.getVotes());
        setBriefTitle(from.getBriefTitle());
        setBriefContent(from.getBriefContent());
        setDeeds(from.getDeeds());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IVoteCampaigners> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends VoteCampaignersEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.report.jooq.tables.records.VoteCampaignersRecord r = new com.zxy.product.report.jooq.tables.records.VoteCampaignersRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ID, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_ID, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_NAME, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.USER_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_ID, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_NAME, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.ORGANIZATION_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.SLOGAN.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.SLOGAN, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.SLOGAN));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.HEAD_IMG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.HEAD_IMG, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.HEAD_IMG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.VOTES.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.VOTES, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.VOTES));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_TITLE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_TITLE, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_TITLE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_CONTENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_CONTENT, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.BRIEF_CONTENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.DEEDS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.DEEDS, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.DEEDS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.CREATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.UPDATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.UPDATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.VoteCampaigners.VOTE_CAMPAIGNERS.UPDATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
