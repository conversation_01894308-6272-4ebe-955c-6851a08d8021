/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables;


import com.zxy.product.report.jooq.Keys;
import com.zxy.product.report.jooq.Report;
import com.zxy.product.report.jooq.tables.records.SyncItemMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SyncItemMember extends TableImpl<SyncItemMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>report.t_sync_item_member</code>
     */
    public static final SyncItemMember SYNC_ITEM_MEMBER = new SyncItemMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SyncItemMemberRecord> getRecordType() {
        return SyncItemMemberRecord.class;
    }

    /**
     * The column <code>report.t_sync_item_member.f_id</code>.
     */
    public final TableField<SyncItemMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>report.t_sync_item_member.f_create_time</code>.
     */
    public final TableField<SyncItemMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * The column <code>report.t_sync_item_member.f_member_id</code>. 同步过来的员工id
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工id");

    /**
     * The column <code>report.t_sync_item_member.f_member_name</code>. 同步过来的员工编号
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_NAME = createField("f_member_name", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "同步过来的员工编号");

    /**
     * The column <code>report.t_sync_item_member.f_member_full_name</code>. 同步过来的员工姓名
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_FULL_NAME = createField("f_member_full_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工姓名");

    /**
     * The column <code>report.t_sync_item_member.f_member_sex</code>. 同步过来的员工性别
     */
    public final TableField<SyncItemMemberRecord, Integer> MEMBER_SEX = createField("f_member_sex", org.jooq.impl.SQLDataType.INTEGER, this, "同步过来的员工性别");

    /**
     * The column <code>report.t_sync_item_member.f_member_credential_value</code>. 同步过来的员工身份证号
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_CREDENTIAL_VALUE = createField("f_member_credential_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工身份证号");

    /**
     * The column <code>report.t_sync_item_member.f_member_born_date</code>. 同步过来的员工出生日期
     */
    public final TableField<SyncItemMemberRecord, Long> MEMBER_BORN_DATE = createField("f_member_born_date", org.jooq.impl.SQLDataType.BIGINT, this, "同步过来的员工出生日期");

    /**
     * The column <code>report.t_sync_item_member.f_member_nationality_value</code>. 同步过来的员工国籍
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_NATIONALITY_VALUE = createField("f_member_nationality_value", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "同步过来的员工国籍");

    /**
     * The column <code>report.t_sync_item_member.f_member_ethnicity_value</code>. 同步过来的员工民族
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_ETHNICITY_VALUE = createField("f_member_ethnicity_value", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "同步过来的员工民族");

    /**
     * The column <code>report.t_sync_item_member.f_member_entry_date</code>. 同步过来的员工参加移动时间(对应入职时间)
     */
    public final TableField<SyncItemMemberRecord, Long> MEMBER_ENTRY_DATE = createField("f_member_entry_date", org.jooq.impl.SQLDataType.BIGINT, this, "同步过来的员工参加移动时间(对应入职时间)");

    /**
     * The column <code>report.t_sync_item_member.f_member_join_date</code>. 同步过来的员工参加本单位时间
     */
    public final TableField<SyncItemMemberRecord, Long> MEMBER_JOIN_DATE = createField("f_member_join_date", org.jooq.impl.SQLDataType.BIGINT, this, "同步过来的员工参加本单位时间");

    /**
     * The column <code>report.t_sync_item_member.f_member_customer_type_value</code>. 同步过来的员工类型
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_CUSTOMER_TYPE_VALUE = createField("f_member_customer_type_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工类型");

    /**
     * The column <code>report.t_sync_item_member.f_member_incumbency_status_value</code>. 同步过来的员工分配状态(对应在职状态)
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_INCUMBENCY_STATUS_VALUE = createField("f_member_incumbency_status_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工分配状态(对应在职状态)");

    /**
     * The column <code>report.t_sync_item_member.f_member_phone_number</code>. 同步过来的员工手机号码
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_PHONE_NUMBER = createField("f_member_phone_number", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工手机号码");

    /**
     * The column <code>report.t_sync_item_member.f_member_email</code>. 同步过来的员工电子邮箱
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_EMAIL = createField("f_member_email", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "同步过来的员工电子邮箱");

    /**
     * The column <code>report.t_sync_item_member.f_member_politicalization_value</code>. 同步过来的员工政治面貌
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_POLITICALIZATION_VALUE = createField("f_member_politicalization_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工政治面貌");

    /**
     * The column <code>report.t_sync_item_member.f_member_expiry_date</code>. 同步过来的员工失效日期
     */
    public final TableField<SyncItemMemberRecord, Long> MEMBER_EXPIRY_DATE = createField("f_member_expiry_date", org.jooq.impl.SQLDataType.BIGINT, this, "同步过来的员工失效日期");

    /**
     * The column <code>report.t_sync_item_member.f_member_school</code>. 同步过来的员工学院
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_SCHOOL = createField("f_member_school", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "同步过来的员工学院");

    /**
     * The column <code>report.t_sync_item_member.f_member_graduate_date</code>. 同步过来的员工毕业时间
     */
    public final TableField<SyncItemMemberRecord, Long> MEMBER_GRADUATE_DATE = createField("f_member_graduate_date", org.jooq.impl.SQLDataType.BIGINT, this, "同步过来的员工毕业时间");

    /**
     * The column <code>report.t_sync_item_member.f_member_education_value</code>. 同步过来的员工学历
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_EDUCATION_VALUE = createField("f_member_education_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "同步过来的员工学历");

    /**
     * The column <code>report.t_sync_item_member.f_member_is_leader</code>. 同步过来的员工是否为领导班子成员
     */
    public final TableField<SyncItemMemberRecord, Integer> MEMBER_IS_LEADER = createField("f_member_is_leader", org.jooq.impl.SQLDataType.INTEGER, this, "同步过来的员工是否为领导班子成员");

    /**
     * The column <code>report.t_sync_item_member.f_member_sequence</code>. 同步过来的员工排序号
     */
    public final TableField<SyncItemMemberRecord, Integer> MEMBER_SEQUENCE = createField("f_member_sequence", org.jooq.impl.SQLDataType.INTEGER, this, "同步过来的员工排序号");

    /**
     * The column <code>report.t_sync_item_member.f_member_organization_code</code>. 同步过来的员工所属部门编码
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_ORGANIZATION_CODE = createField("f_member_organization_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "同步过来的员工所属部门编码");

    /**
     * The column <code>report.t_sync_item_member.f_member_position_code</code>. 同步过来的员工职位编码(对应职位编码)
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_POSITION_CODE = createField("f_member_position_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "同步过来的员工职位编码(对应职位编码)");

    /**
     * The column <code>report.t_sync_item_member.f_root_organization_id</code>. 根组织id
     */
    public final TableField<SyncItemMemberRecord, String> ROOT_ORGANIZATION_ID = createField("f_root_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "根组织id");

    /**
     * The column <code>report.t_sync_item_member.f_type</code>. 同步类型(INSERT=新增;UPDATE=修改;DELETE=删除)
     */
    public final TableField<SyncItemMemberRecord, String> TYPE = createField("f_type", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "同步类型(INSERT=新增;UPDATE=修改;DELETE=删除)");

    /**
     * The column <code>report.t_sync_item_member.f_status</code>. 同步状态(0=初始状态;1=成功;2=失败)
     */
    public final TableField<SyncItemMemberRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "同步状态(0=初始状态;1=成功;2=失败)");

    /**
     * The column <code>report.t_sync_item_member.f_remark</code>. 备注
     */
    public final TableField<SyncItemMemberRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "备注");

    /**
     * The column <code>report.t_sync_item_member.f_version</code>. 版本
     */
    public final TableField<SyncItemMemberRecord, String> VERSION = createField("f_version", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "版本");

    /**
     * The column <code>report.t_sync_item_member.f_member_status</code>. 是否减员 0-是，1-否
     */
    public final TableField<SyncItemMemberRecord, Integer> MEMBER_STATUS = createField("f_member_status", org.jooq.impl.SQLDataType.INTEGER, this, "是否减员 0-是，1-否");

    /**
     * The column <code>report.t_sync_item_member.f_member_position_name</code>. 职位名称
     */
    public final TableField<SyncItemMemberRecord, String> MEMBER_POSITION_NAME = createField("f_member_position_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "职位名称");

    /**
     * Create a <code>report.t_sync_item_member</code> table reference
     */
    public SyncItemMember() {
        this("t_sync_item_member", null);
    }

    /**
     * Create an aliased <code>report.t_sync_item_member</code> table reference
     */
    public SyncItemMember(String alias) {
        this(alias, SYNC_ITEM_MEMBER);
    }

    private SyncItemMember(String alias, Table<SyncItemMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private SyncItemMember(String alias, Table<SyncItemMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Report.REPORT_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SyncItemMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_SYNC_ITEM_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SyncItemMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<SyncItemMemberRecord>>asList(Keys.KEY_T_SYNC_ITEM_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SyncItemMember as(String alias) {
        return new SyncItemMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SyncItemMember rename(String name) {
        return new SyncItemMember(name, null);
    }
}
