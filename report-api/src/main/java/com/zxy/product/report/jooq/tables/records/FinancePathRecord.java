/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.records;


import com.zxy.product.report.jooq.tables.FinancePath;
import com.zxy.product.report.jooq.tables.interfaces.IFinancePath;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 财务部考试文件表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FinancePathRecord extends UpdatableRecordImpl<FinancePathRecord> implements Record6<String, Long, Integer, String, String, String>, IFinancePath {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>report.t_finance_path.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>report.t_finance_path.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>report.t_finance_path.f_type</code>. 文件类型：1：word ，2: excel，3: ppt ，4: ptf，5：压缩包
     */
    @Override
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_type</code>. 文件类型：1：word ，2: excel，3: ppt ，4: ptf，5：压缩包
     */
    @Override
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>report.t_finance_path.f_group_name</code>. 组名
     */
    @Override
    public void setGroupName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_group_name</code>. 组名
     */
    @Override
    public String getGroupName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>report.t_finance_path.f_file_id</code>. 文件id
     */
    @Override
    public void setFileId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_file_id</code>. 文件id
     */
    @Override
    public String getFileId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>report.t_finance_path.f_file_name</code>. 文件名称
     */
    @Override
    public void setFileName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>report.t_finance_path.f_file_name</code>. 文件名称
     */
    @Override
    public String getFileName() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Long, Integer, String, String, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Long, Integer, String, String, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return FinancePath.FINANCE_PATH.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return FinancePath.FINANCE_PATH.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return FinancePath.FINANCE_PATH.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return FinancePath.FINANCE_PATH.GROUP_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return FinancePath.FINANCE_PATH.FILE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return FinancePath.FINANCE_PATH.FILE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getGroupName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getFileId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getFileName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value3(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value4(String value) {
        setGroupName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value5(String value) {
        setFileId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord value6(String value) {
        setFileName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePathRecord values(String value1, Long value2, Integer value3, String value4, String value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IFinancePath from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setType(from.getType());
        setGroupName(from.getGroupName());
        setFileId(from.getFileId());
        setFileName(from.getFileName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IFinancePath> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FinancePathRecord
     */
    public FinancePathRecord() {
        super(FinancePath.FINANCE_PATH);
    }

    /**
     * Create a detached, initialised FinancePathRecord
     */
    public FinancePathRecord(String id, Long createTime, Integer type, String groupName, String fileId, String fileName) {
        super(FinancePath.FINANCE_PATH);

        set(0, id);
        set(1, createTime);
        set(2, type);
        set(3, groupName);
        set(4, fileId);
        set(5, fileName);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.report.jooq.tables.pojos.FinancePathEntity)) {
            return false;
        }
        com.zxy.product.report.jooq.tables.pojos.FinancePathEntity pojo = (com.zxy.product.report.jooq.tables.pojos.FinancePathEntity)source;
        pojo.into(this);
        return true;
    }
}
