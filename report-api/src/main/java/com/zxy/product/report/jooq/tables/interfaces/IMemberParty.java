/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 党员信息（原样同步党建云数据）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMemberParty extends Serializable {

    /**
     * Setter for <code>report.t_member_party.f_id</code>. 主键（同步）
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_member_party.f_id</code>. 主键（同步）
     */
    public String getId();

    /**
     * Setter for <code>report.t_member_party.f_member_id</code>. 人员id(暂不维护)，请使用ihrcode做关联
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>report.t_member_party.f_member_id</code>. 人员id(暂不维护)，请使用ihrcode做关联
     */
    public String getMemberId();

    /**
     * Setter for <code>report.t_member_party.f_ihr_code</code>. ihr新编码，用来和网大账号匹配
     */
    public void setIhrCode(String value);

    /**
     * Getter for <code>report.t_member_party.f_ihr_code</code>. ihr新编码，用来和网大账号匹配
     */
    public String getIhrCode();

    /**
     * Setter for <code>report.t_member_party.f_user_name</code>. 党建登录名称 
     */
    public void setUserName(String value);

    /**
     * Getter for <code>report.t_member_party.f_user_name</code>. 党建登录名称 
     */
    public String getUserName();

    /**
     * Setter for <code>report.t_member_party.f_code</code>. 党建员工编号
     */
    public void setCode(String value);

    /**
     * Getter for <code>report.t_member_party.f_code</code>. 党建员工编号
     */
    public String getCode();

    /**
     * Setter for <code>report.t_member_party.f_display_name</code>. 员工姓名
     */
    public void setDisplayName(String value);

    /**
     * Getter for <code>report.t_member_party.f_display_name</code>. 员工姓名
     */
    public String getDisplayName();

    /**
     * Setter for <code>report.t_member_party.f_idcard_number</code>. 身份证信息
     */
    public void setIdcardNumber(String value);

    /**
     * Getter for <code>report.t_member_party.f_idcard_number</code>. 身份证信息
     */
    public String getIdcardNumber();

    /**
     * Setter for <code>report.t_member_party.f_work_hour</code>. 工作时间
     */
    public void setWorkHour(Long value);

    /**
     * Getter for <code>report.t_member_party.f_work_hour</code>. 工作时间
     */
    public Long getWorkHour();

    /**
     * Setter for <code>report.t_member_party.f_work_organization</code>. 工作单位
     */
    public void setWorkOrganization(String value);

    /**
     * Getter for <code>report.t_member_party.f_work_organization</code>. 工作单位
     */
    public String getWorkOrganization();

    /**
     * Setter for <code>report.t_member_party.f_org_id</code>. 党组织ID
     */
    public void setOrgId(String value);

    /**
     * Getter for <code>report.t_member_party.f_org_id</code>. 党组织ID
     */
    public String getOrgId();

    /**
     * Setter for <code>report.t_member_party.f_org_name</code>. 党组织名称
     */
    public void setOrgName(String value);

    /**
     * Getter for <code>report.t_member_party.f_org_name</code>. 党组织名称
     */
    public String getOrgName();

    /**
     * Setter for <code>report.t_member_party.f_user_type</code>. 党建用户类型（申请人、积极分子、发展对象、预备党员、党员）
     */
    public void setUserType(String value);

    /**
     * Getter for <code>report.t_member_party.f_user_type</code>. 党建用户类型（申请人、积极分子、发展对象、预备党员、党员）
     */
    public String getUserType();

    /**
     * Setter for <code>report.t_member_party.f_party_date</code>. 入党时间
     */
    public void setPartyDate(Long value);

    /**
     * Getter for <code>report.t_member_party.f_party_date</code>. 入党时间
     */
    public Long getPartyDate();

    /**
     * Setter for <code>report.t_member_party.f_party_member_state</code>. 党员状态 （开除党籍，退党除名，劝退和劝退不退除名，自行脱党除名，取消预备党员资格，死亡）
     */
    public void setPartyMemberState(String value);

    /**
     * Getter for <code>report.t_member_party.f_party_member_state</code>. 党员状态 （开除党籍，退党除名，劝退和劝退不退除名，自行脱党除名，取消预备党员资格，死亡）
     */
    public String getPartyMemberState();

    /**
     * Setter for <code>report.t_member_party.f_apply_party_date</code>. 申请入党时间
     */
    public void setApplyPartyDate(Long value);

    /**
     * Getter for <code>report.t_member_party.f_apply_party_date</code>. 申请入党时间
     */
    public Long getApplyPartyDate();

    /**
     * Setter for <code>report.t_member_party.f_to_activist_date</code>. 转为积极分子时间
     */
    public void setToActivistDate(Long value);

    /**
     * Getter for <code>report.t_member_party.f_to_activist_date</code>. 转为积极分子时间
     */
    public Long getToActivistDate();

    /**
     * Setter for <code>report.t_member_party.f_to_object_date</code>. 转为发展对象时间
     */
    public void setToObjectDate(Long value);

    /**
     * Getter for <code>report.t_member_party.f_to_object_date</code>. 转为发展对象时间
     */
    public Long getToObjectDate();

    /**
     * Setter for <code>report.t_member_party.f_to_member_date</code>. 转为正式党员时间
     */
    public void setToMemberDate(Long value);

    /**
     * Getter for <code>report.t_member_party.f_to_member_date</code>. 转为正式党员时间
     */
    public Long getToMemberDate();

    /**
     * Setter for <code>report.t_member_party.f_party_state</code>. 党员状态（onduty 在职，retire 退休，other 其他（失联，离职未转出）
     */
    public void setPartyState(String value);

    /**
     * Getter for <code>report.t_member_party.f_party_state</code>. 党员状态（onduty 在职，retire 退休，other 其他（失联，离职未转出）
     */
    public String getPartyState();

    /**
     * Setter for <code>report.t_member_party.f_is_floating</code>. 是否流动党员
     */
    public void setIsFloating(Integer value);

    /**
     * Getter for <code>report.t_member_party.f_is_floating</code>. 是否流动党员
     */
    public Integer getIsFloating();

    /**
     * Setter for <code>report.t_member_party.f_influx_place</code>. 流入地
     */
    public void setInfluxPlace(String value);

    /**
     * Getter for <code>report.t_member_party.f_influx_place</code>. 流入地
     */
    public String getInfluxPlace();

    /**
     * Setter for <code>report.t_member_party.f_post_id</code>. 工作岗位ID
     */
    public void setPostId(String value);

    /**
     * Getter for <code>report.t_member_party.f_post_id</code>. 工作岗位ID
     */
    public String getPostId();

    /**
     * Setter for <code>report.t_member_party.f_user_id</code>. Guid类型的用户编号
     */
    public void setUserId(String value);

    /**
     * Getter for <code>report.t_member_party.f_user_id</code>. Guid类型的用户编号
     */
    public String getUserId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMemberParty
     */
    public void from(IMemberParty from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMemberParty
     */
    public <E extends IMemberParty> E into(E into);
}
