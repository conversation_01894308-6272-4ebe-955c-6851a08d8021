package com.zxy.product.report.api.legal;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.report.entity.LegalQuestionRecord;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/26
 */
@RemoteService
public interface LegalQuestionRecordService {

    @Transactional
    List<LegalQuestionRecord> save(String memberId, List<LegalQuestionRecord> records);

    @Transactional
    void compute(String memberId);

}
