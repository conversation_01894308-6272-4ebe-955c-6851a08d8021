/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 中国移动职务同步表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISyncItemJob extends Serializable {

    /**
     * Setter for <code>report.t_sync_item_job.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>report.t_sync_item_job.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>report.t_sync_item_job.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>report.t_sync_item_job.f_job_id</code>. 同步过来的职务id
     */
    public void setJobId(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_job_id</code>. 同步过来的职务id
     */
    public String getJobId();

    /**
     * Setter for <code>report.t_sync_item_job.f_job_code</code>. 同步过来的职务编码
     */
    public void setJobCode(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_job_code</code>. 同步过来的职务编码
     */
    public String getJobCode();

    /**
     * Setter for <code>report.t_sync_item_job.f_job_name</code>. 同步过来的职务名称
     */
    public void setJobName(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_job_name</code>. 同步过来的职务名称
     */
    public String getJobName();

    /**
     * Setter for <code>report.t_sync_item_job.f_job_status</code>. 同步过来的职务状态(0=禁用;1=可用)
     */
    public void setJobStatus(Integer value);

    /**
     * Getter for <code>report.t_sync_item_job.f_job_status</code>. 同步过来的职务状态(0=禁用;1=可用)
     */
    public Integer getJobStatus();

    /**
     * Setter for <code>report.t_sync_item_job.f_job_default_type_id</code>. 默认的组织类别id
     */
    public void setJobDefaultTypeId(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_job_default_type_id</code>. 默认的组织类别id
     */
    public String getJobDefaultTypeId();

    /**
     * Setter for <code>report.t_sync_item_job.f_root_organization_id</code>. 根组织id
     */
    public void setRootOrganizationId(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_root_organization_id</code>. 根组织id
     */
    public String getRootOrganizationId();

    /**
     * Setter for <code>report.t_sync_item_job.f_type</code>. 同步类型(INSERT=新增;UPDATE=修改;DELETE=删除)
     */
    public void setType(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_type</code>. 同步类型(INSERT=新增;UPDATE=修改;DELETE=删除)
     */
    public String getType();

    /**
     * Setter for <code>report.t_sync_item_job.f_status</code>. 同步状态(0=初始状态;1=成功;2=失败)
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>report.t_sync_item_job.f_status</code>. 同步状态(0=初始状态;1=成功;2=失败)
     */
    public Integer getStatus();

    /**
     * Setter for <code>report.t_sync_item_job.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>report.t_sync_item_job.f_version</code>. 版本
     */
    public void setVersion(String value);

    /**
     * Getter for <code>report.t_sync_item_job.f_version</code>. 版本
     */
    public String getVersion();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISyncItemJob
     */
    public void from(com.zxy.product.report.jooq.tables.interfaces.ISyncItemJob from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISyncItemJob
     */
    public <E extends com.zxy.product.report.jooq.tables.interfaces.ISyncItemJob> E into(E into);
}
