/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 网格学习投票明细
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IGridVoteDetail extends Serializable {

    /**
     * Setter for <code>report.t_grid_vote_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_vote_id</code>. 项目id
     */
    public void setVoteId(String value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_vote_id</code>. 项目id
     */
    public String getVoteId();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_tg</code>. 推广价值
     */
    public void setTg(Integer value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_tg</code>. 推广价值
     */
    public Integer getTg();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_cx</code>. 创新启迪
     */
    public void setCx(Integer value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_cx</code>. 创新启迪
     */
    public Integer getCx();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_zz</code>. 制作质量
     */
    public void setZz(Integer value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_zz</code>. 制作质量
     */
    public Integer getZz();

    /**
     * Setter for <code>report.t_grid_vote_detail.f_content</code>. 更多交流
     */
    public void setContent(String value);

    /**
     * Getter for <code>report.t_grid_vote_detail.f_content</code>. 更多交流
     */
    public String getContent();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IGridVoteDetail
     */
    public void from(IGridVoteDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IGridVoteDetail
     */
    public <E extends IGridVoteDetail> E into(E into);
}
