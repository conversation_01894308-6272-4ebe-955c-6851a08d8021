/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables;


import com.zxy.product.report.jooq.Keys;
import com.zxy.product.report.jooq.Report;
import com.zxy.product.report.jooq.tables.records.VoteRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 用户投票记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class VoteRecord extends TableImpl<VoteRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>report.t_vote_record</code>
     */
    public static final VoteRecord VOTE_RECORD = new VoteRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<VoteRecordRecord> getRecordType() {
        return VoteRecordRecord.class;
    }

    /**
     * The column <code>report.t_vote_record.f_id</code>. 自增ID
     */
    public final TableField<VoteRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "自增ID");

    /**
     * The column <code>report.t_vote_record.f_vote_id</code>. 投票者ID
     */
    public final TableField<VoteRecordRecord, String> VOTE_ID = createField("f_vote_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "投票者ID");

    /**
     * The column <code>report.t_vote_record.f_campaigners_id</code>. 竞选者ID
     */
    public final TableField<VoteRecordRecord, String> CAMPAIGNERS_ID = createField("f_campaigners_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "竞选者ID");

    /**
     * The column <code>report.t_vote_record.f_create_time</code>. 投票时间
     */
    public final TableField<VoteRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "投票时间");

    /**
     * The column <code>report.t_vote_record.f_create_time_str</code>.
     */
    public final TableField<VoteRecordRecord, String> CREATE_TIME_STR = createField("f_create_time_str", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * Create a <code>report.t_vote_record</code> table reference
     */
    public VoteRecord() {
        this("t_vote_record", null);
    }

    /**
     * Create an aliased <code>report.t_vote_record</code> table reference
     */
    public VoteRecord(String alias) {
        this(alias, VOTE_RECORD);
    }

    private VoteRecord(String alias, Table<VoteRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private VoteRecord(String alias, Table<VoteRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "用户投票记录");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Report.REPORT_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<VoteRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_VOTE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<VoteRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<VoteRecordRecord>>asList(Keys.KEY_T_VOTE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public VoteRecord as(String alias) {
        return new VoteRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public VoteRecord rename(String name) {
        return new VoteRecord(name, null);
    }
}
