package com.zxy.product.report.content;

/**
 * 消息常量
 * <AUTHOR>
 *
 */
public class MessageTypeContent {

    /**
     * 人资模块(HR) 1 课程模块(COURSE) 2 system 3 exam 4 course 5.ask-bar 6.train ...  8 log ,9 hr-sync
     *
     * ONE 01 ALL 02
     */


    public static final int COURSE_SCHEDULE_MESSAGE = 90002; //系统定时发送消息

    public static final int ALL_COURSE_SCHEDULE_MESSAGE = 90003; //系统定时发送消息

    public static final int EXAM_CERTIFICATE_SCHEDULE_MESSAGE = 90004; //系统定时发送消息

    public static final int ALL_EXAM_CERTIFICATE_SCHEDULE_MESSAGE = 90005; //系统定时发送消息

    public static final int SUBJECT_CERTIFICATE_SCHEDULE_MESSAGE = 90006; //系统定时发送消息

    public static final int ALL_SUBJECT_CERTIFICATE_SCHEDULE_MESSAGE = 90007; //系统定时发送消息

    /*
     * 手动同步
     */
    public static final int HR_SYNC_WEB_ONE = 99101;
    public static final int HR_SYNC_WEB_ALL = 99102;

    // web handle into report sync
    public static final int HR_SYNC_HANDLE_ORGANIZATION = 99106;
    public static final int HR_SYNC_HANDLE_JOB = 99107;
    public static final int HR_SYNC_HANDLE_OPSITION = 99108;
    public static final int HR_SYNC_HANDLE_MEMBER = 99109;

    // handle sync
    public static final int HR_SYNC_HANDLE_EMPTY = 99200;

    // result to items
    public static final int HR_SYNC_JOB_RESOURCE = 99103;
    public static final int HR_SYNC_POSITION_RESOURCE = 99104;
    public static final int HR_SYNC_MEMBER_RESOURCE = 99105;

    // syncdata to result
    public static final int SYNC_ITEM_ORGANIZATION_REPORT = 99201;
    public static final int SYNC_ITEM_JOB_REPORT = 99202;
    public static final int SYNC_ITEM_POSITION_REPORT = 99203;
    public static final int SYNC_ITEM_MEMBER_REPORT = 99204;



    // item to syncdata
    public static final int SYNC_ORGANIZATION_TO_SYSTEM = 99301;
    public static final int SYNC_JOB_TO_HUMAN = 99302;
    public static final int SYNC_POSITION_TO_HUMAN = 99303;
    public static final int SYNC_MEMBER_TO_HUMAN = 99304;

    // item to handle sync
    public static final int HANDLE_ORGANIZATION_TO_SYSTEM = 99401;
    public static final int HANDLE_JOB_TO_HUMAN = 99402;
    public static final int HANDLE_POSITION_TO_HUMAN = 99403;
    public static final int HANDLE_MEMBER_TO_HUMAN = 99404;

    // CSV
    public static final int CSV_RECORD_TO_COURSE = 99500;
    public static final int CSV_COURSE_TO_TRAIN = 99510;
    public static final int CSV_NOT_AUTO = 99511;
    //VOTE
	public static final int VOTE_UPDATE_INSERT_RECORD = 99600;


    //搜索行为
    public static final int  SYSTEM_SEARCH_BEHAVIOR=99701;//搜索行为创建

    //*************************************红船****************************************
    public static final int  RED_SHIP_TASK = 99711;//任务提交给红船
    public static final int  RED_SHIP_MATERIAL_INSERT = 99712;//添加素材
    public static final int  RED_SHIP_TASK_UPDATE = 99713;//任务更新
    public static final int  RED_SHIP_DELETE_FILE = 99714;//文件删除
    public static final int  RED_SHIP_BUSINESS_UPDATE= 99715;//业务更新
    public static final int  RED_SHIP_COURSE_MATERIAL_INSERT = 99716;//课程上传素材


    // ********************************* ihr-sync-content ************************************** //

    // ihr定时同步
    public static final int IHR_AUTO_SYNC_POSITION_RESOURCE = 99901;
    public static final int IHR_AUTO_SYNC_JOB_RESOURCE = 99902;
    public static final int IHR_AUTO_SYNC_MEMBER_RESOURCE = 99903;
    public static final int IHR_AUTO_SYNC_JOB_SYNC = 99916;

    // ihr手动同步全类型
    public static final int IHR_MANUAL_SYNC_SINGLE_ORGANIZATION_RESOURCE = 99904;
    public static final int IHR_MANUAL_SYNC_SINGLE_POSITION_RESOURCE = 99905;
    public static final int IHR_MANUAL_SYNC_SINGLE_JOB_RESOURCE = 99906;
    public static final int IHR_MANUAL_SYNC_SINGLE_MEMBER_RESOURCE = 99907;
    public static final int IHR_MANUAL_SYNC_SINGLE_JOB_SYNC = 99917;

    // ihr手动同步单类型
    public static final int IHR_MANUAL_SYNC_ALL_ORGANIZATION_RESOURCE = 99908;
    public static final int IHR_MANUAL_SYNC_ALL_POSITION_RESOURCE = 99909;
    public static final int IHR_MANUAL_SYNC_ALL_JOB_RESOURCE = 99910;
    public static final int IHR_MANUAL_SYNC_ALL_MEMBER_RESOURCE = 99911;
    public static final int IHR_MANUAL_SYNC_ALL_JOB_SYNC = 99918;

    // ihr空数据消息
    public static final int IHR_SYNC_ORGANIZATION_NO_DATA = 99912;
    public static final int IHR_SYNC_JOB_NO_DATA = 99913;
    public static final int IHR_SYNC_POSITION_NO_DATA = 99914;
    public static final int IHR_SYNC_MEMBER_NO_DATA = 99915;

    public static final int PARALLEL_SYNC_DATA_ALL=99980;

    // 党建数据同步相关消息
    // 自动同步
    public static final int PARTY_AUTO_SYNC_ORGANIZATION = 99981;
    public static final int PARTY_AUTO_SYNC_MEMBER = 99982;
    // 手动同步
    public static final int PARTY_MANUAL_SYNC_ALL = 99983;
    public static final int PARTY_MANUAL_SYNC_ORGANIZATION = 99984;
    public static final int PARTY_MANUAL_SYNC_MEMBER = 99985;


    // 九天手动同步
    public static final int JIUTIAN_SYNC_MEMBER_ALL = 99991;
    public static final int JIUTIAN_SYNC_TIME_ALL = 99992;
    public static final int JIUTIAN_SYNC_CONTENT_ALL = 99993;
    public static final int JIUTIAN_SYNC_ORG_ALL = 99994;
    public static final int JIUTIAN_SYNC_TOPIC_ALL = 99995;
    public static final int JIUTIAN_SYNC_POSITION_TOPIC_ALL = 99996;
    public static final int JIUTIAN_SYNC_COMPENSATE = 99997;
    public static final int JIUTIAN_SYNC_USER_ALL = 99999;


    /**
     * 搜索置顶词同步
     */
    public static final int SEARCH_WORD_TOP = 99899;

    /**
     * 搜索点击率
     */
    public static final int ARTICLE_ONE = 99898;


    public static final int SHORT_VIDEO = 88888;
    public static final int CMC_CERTIFICATE = 88889;
    public static final int REH = 88890;


    public static final int MIGU_BOOK_OLD = 900001;
    public static final int MIGU_BOOK_NEW = 900002;

    public static final int SYNCHRONOUS_COURSE=800014;
}
