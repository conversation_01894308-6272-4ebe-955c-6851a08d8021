package com.zxy.product.report.entity;

import com.zxy.product.report.jooq.tables.pojos.IhrSyncItemPositionEntity;

public class IhrSyncItemPosition extends IhrSyncItemPositionEntity {
    private static final long serialVersionUID = 3427262829286468776L;

    private static final String UNDER_LINE = "_";
    // 校验状态 0:未校验 1:校验成功 2:校验失败
    public static final Integer CHECK_STATUS_INIT = 0;
    public static final Integer CHECK_STATUS_SUCCESS = 1;
    public static final Integer CHECK_STATUS_FAILURE = 2;

    // 同步状态 0:未同步 1:同步成功 2:同步失败
    public static final Integer SYNC_STATUS_INIT = 0;
    public static final Integer SYNC_STATUS_SUCCESS = 1;
    public static final Integer SYNC_STATUS_FAILURE = 2;

    // 操作类型 1:新增 2:修改 3:删除
    public static final Integer OPERATION_INSERT = 1;
    public static final Integer OPERATION_UPDATE = 2;
    public static final Integer OPERATION_DELETE = 3;

    // 职位状态 0:禁用 1:可用
    public static final Integer POSITION_STATUS_NO = 0;
    public static final Integer POSITION_STATUS_YES = 1;

    // 默认值
    public static final String DEFAULT_ROOT_ORGANIZATION_ID = "1";
    public static final String DEFAULT_IHR_JOB_ID = "default_ihr_job_id";
    public static final String DEFAULT_MIS_ID = "ihr";
    public static final String DEFAULT_MIS_CODE = "ihr";

    public String getMapKey() {
        return this.getPositionCode() + UNDER_LINE + this.getPositionOrganizationId();
    }
}
