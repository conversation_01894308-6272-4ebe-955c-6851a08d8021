/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 20周年活动-活动主题小节图片详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAnniversaryChapterSection extends Serializable {

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_name</code>. 名称
     */
    public void setName(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_name</code>. 名称
     */
    public String getName();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_anniversary_id</code>. 主题ID
     */
    public void setAnniversaryId(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_anniversary_id</code>. 主题ID
     */
    public String getAnniversaryId();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_chapter_id</code>. 章节ID
     */
    public void setChapterId(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_chapter_id</code>. 章节ID
     */
    public String getChapterId();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_description</code>. 每张图片简介
     */
    public void setDescription(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_description</code>. 每张图片简介
     */
    public String getDescription();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_image_id</code>. pc原图片ID
     */
    public void setImageId(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_image_id</code>. pc原图片ID
     */
    public String getImageId();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_image_path</code>. pc原图片路径ID
     */
    public void setImagePath(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_image_path</code>. pc原图片路径ID
     */
    public String getImagePath();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_audio_id</code>. 音频id
     */
    public void setAudioId(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_audio_id</code>. 音频id
     */
    public String getAudioId();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_audio_path</code>. 音频访问路径
     */
    public void setAudioPath(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_audio_path</code>. 音频访问路径
     */
    public String getAudioPath();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_sequence</code>. 排序
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_sequence</code>. 排序
     */
    public Integer getSequence();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_client_type</code>. 客户端，1.pc，2.app
     */
    public void setClientType(Integer value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_client_type</code>. 客户端，1.pc，2.app
     */
    public Integer getClientType();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>report.t_anniversary_chapter_section.f_large_image_path</code>. 放大图片展示路径
     */
    public void setLargeImagePath(String value);

    /**
     * Getter for <code>report.t_anniversary_chapter_section.f_large_image_path</code>. 放大图片展示路径
     */
    public String getLargeImagePath();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAnniversaryChapterSection
     */
    public void from(com.zxy.product.report.jooq.tables.interfaces.IAnniversaryChapterSection from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAnniversaryChapterSection
     */
    public <E extends com.zxy.product.report.jooq.tables.interfaces.IAnniversaryChapterSection> E into(E into);
}
