package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/28
 * @description ：文档&电子书&图片
 */
public class FileBookImag implements Serializable {

    boolean open;// 是否打开
    String type;// 水印类型，1图片水印，2文字水印
    String file;// 图片水印的图片
    String preType;// 文字水印的 前缀
    String text;// 内容
    String typeface;// 内容字体
    String size;// 内容字号大小
    String color;// 内容颜色
    String opacityText;// 文字透明度
    String opacityImg;// 图片透明度

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTypeface() {
        return typeface;
    }

    public void setTypeface(String typeface) {
        this.typeface = typeface;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getOpacityText() {
        return opacityText;
    }

    public void setOpacityText(String opacityText) {
        this.opacityText = opacityText;
    }

    public String getOpacityImg() {
        return opacityImg;
    }

    public void setOpacityImg(String opacityImg) {
        this.opacityImg = opacityImg;
    }
}
