/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.AdvertisementRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 广告表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Advertisement extends TableImpl<AdvertisementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_advertisement</code>
     */
    public static final Advertisement ADVERTISEMENT = new Advertisement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AdvertisementRecord> getRecordType() {
        return AdvertisementRecord.class;
    }

    /**
     * The column <code>system.t_advertisement.f_id</code>. 主键
     */
    public final TableField<AdvertisementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>system.t_advertisement.f_organization_id</code>. 组织机构
     */
    public final TableField<AdvertisementRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织机构");

    /**
     * The column <code>system.t_advertisement.f_title</code>. 名称
     */
    public final TableField<AdvertisementRecord, String> TITLE = createField("f_title", org.jooq.impl.SQLDataType.CLOB, this, "名称");

    /**
     * The column <code>system.t_advertisement.f_client</code>. 0 全部 1 pc 2 app
     */
    public final TableField<AdvertisementRecord, Integer> CLIENT = createField("f_client", org.jooq.impl.SQLDataType.INTEGER, this, "0 全部 1 pc 2 app");

    /**
     * The column <code>system.t_advertisement.f_pc_file_path</code>. pc文件路径
     */
    public final TableField<AdvertisementRecord, String> PC_FILE_PATH = createField("f_pc_file_path", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "pc文件路径");

    /**
     * The column <code>system.t_advertisement.f_pc_file_name</code>. pc文件名称
     */
    public final TableField<AdvertisementRecord, String> PC_FILE_NAME = createField("f_pc_file_name", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "pc文件名称");

    /**
     * The column <code>system.t_advertisement.f_pc_content</code>. pc内容
     */
    public final TableField<AdvertisementRecord, String> PC_CONTENT = createField("f_pc_content", org.jooq.impl.SQLDataType.CLOB, this, "pc内容");

    /**
     * The column <code>system.t_advertisement.f_app_file_path</code>. app文件路径
     */
    public final TableField<AdvertisementRecord, String> APP_FILE_PATH = createField("f_app_file_path", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "app文件路径");

    /**
     * The column <code>system.t_advertisement.f_app_file_name</code>. app文件名称
     */
    public final TableField<AdvertisementRecord, String> APP_FILE_NAME = createField("f_app_file_name", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "app文件名称");

    /**
     * The column <code>system.t_advertisement.f_app_content</code>. app内容
     */
    public final TableField<AdvertisementRecord, String> APP_CONTENT = createField("f_app_content", org.jooq.impl.SQLDataType.CLOB, this, "app内容");

    /**
     * The column <code>system.t_advertisement.f_content</code>. 文案内容
     */
    public final TableField<AdvertisementRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB, this, "文案内容");

    /**
     * The column <code>system.t_advertisement.f_source</code>. 来源
     */
    public final TableField<AdvertisementRecord, String> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.VARCHAR.length(1), this, "来源");

    /**
     * The column <code>system.t_advertisement.f_publish_member_id</code>. 发布人
     */
    public final TableField<AdvertisementRecord, String> PUBLISH_MEMBER_ID = createField("f_publish_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "发布人");

    /**
     * The column <code>system.t_advertisement.f_publish_time</code>. 发布时间
     */
    public final TableField<AdvertisementRecord, Long> PUBLISH_TIME = createField("f_publish_time", org.jooq.impl.SQLDataType.BIGINT, this, "发布时间");

    /**
     * The column <code>system.t_advertisement.f_create_member_id</code>. 创建时间
     */
    public final TableField<AdvertisementRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建时间");

    /**
     * The column <code>system.t_advertisement.f_create_time</code>. 创建时间
     */
    public final TableField<AdvertisementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>system.t_advertisement.f_link_type</code>. 链接类型
     */
    public final TableField<AdvertisementRecord, Integer> LINK_TYPE = createField("f_link_type", org.jooq.impl.SQLDataType.INTEGER, this, "链接类型");

    /**
     * The column <code>system.t_advertisement.f_link_address</code>. 链接地址
     */
    public final TableField<AdvertisementRecord, String> LINK_ADDRESS = createField("f_link_address", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "链接地址");

    /**
     * The column <code>system.t_advertisement.f_status</code>. 状态 0 未发布 1已发布
     */
    public final TableField<AdvertisementRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态 0 未发布 1已发布");

    /**
     * The column <code>system.t_advertisement.f_seq</code>. 排序
     */
    public final TableField<AdvertisementRecord, Integer> SEQ = createField("f_seq", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>system.t_advertisement</code> table reference
     */
    public Advertisement() {
        this("t_advertisement", null);
    }

    /**
     * Create an aliased <code>system.t_advertisement</code> table reference
     */
    public Advertisement(String alias) {
        this(alias, ADVERTISEMENT);
    }

    private Advertisement(String alias, Table<AdvertisementRecord> aliased) {
        this(alias, aliased, null);
    }

    private Advertisement(String alias, Table<AdvertisementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "广告表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AdvertisementRecord> getPrimaryKey() {
        return Keys.KEY_T_ADVERTISEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AdvertisementRecord>> getKeys() {
        return Arrays.<UniqueKey<AdvertisementRecord>>asList(Keys.KEY_T_ADVERTISEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Advertisement as(String alias) {
        return new Advertisement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Advertisement rename(String name) {
        return new Advertisement(name, null);
    }
}
