/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.RecentVisits;
import com.zxy.product.system.jooq.tables.interfaces.IRecentVisits;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 虚拟空间-最近访问
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RecentVisitsRecord extends UpdatableRecordImpl<RecentVisitsRecord> implements Record5<String, String, String, Long, Timestamp>, IRecentVisits {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_recent_visits.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_recent_visits.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_recent_visits.f_home_config_id</code>. 虚拟空间id
     */
    @Override
    public void setHomeConfigId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_recent_visits.f_home_config_id</code>. 虚拟空间id
     */
    @Override
    public String getHomeConfigId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_recent_visits.f_member_id</code>. 人员id
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_recent_visits.f_member_id</code>. 人员id
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_recent_visits.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_recent_visits.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>system.t_recent_visits.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_recent_visits.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Timestamp> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Timestamp> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return RecentVisits.RECENT_VISITS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return RecentVisits.RECENT_VISITS.HOME_CONFIG_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return RecentVisits.RECENT_VISITS.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return RecentVisits.RECENT_VISITS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field5() {
        return RecentVisits.RECENT_VISITS.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getHomeConfigId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value5() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord value2(String value) {
        setHomeConfigId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord value5(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RecentVisitsRecord values(String value1, String value2, String value3, Long value4, Timestamp value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IRecentVisits from) {
        setId(from.getId());
        setHomeConfigId(from.getHomeConfigId());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IRecentVisits> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RecentVisitsRecord
     */
    public RecentVisitsRecord() {
        super(RecentVisits.RECENT_VISITS);
    }

    /**
     * Create a detached, initialised RecentVisitsRecord
     */
    public RecentVisitsRecord(String id, String homeConfigId, String memberId, Long createTime, Timestamp modifyDate) {
        super(RecentVisits.RECENT_VISITS);

        set(0, id);
        set(1, homeConfigId);
        set(2, memberId);
        set(3, createTime);
        set(4, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.RecentVisitsEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.RecentVisitsEntity pojo = (com.zxy.product.system.jooq.tables.pojos.RecentVisitsEntity)source;
        pojo.into(this);
        return true;
    }
}
