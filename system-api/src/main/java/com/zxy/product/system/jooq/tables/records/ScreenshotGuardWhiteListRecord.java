/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.ScreenshotGuardWhiteList;
import com.zxy.product.system.jooq.tables.interfaces.IScreenshotGuardWhiteList;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 防录屏白名单表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ScreenshotGuardWhiteListRecord extends UpdatableRecordImpl<ScreenshotGuardWhiteListRecord> implements Record4<String, Integer, String, Long>, IScreenshotGuardWhiteList {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_screenshot_guard_white_list.f_id</code>. 主键ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_screenshot_guard_white_list.f_id</code>. 主键ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_screenshot_guard_white_list.f_type</code>. 业务分类 1-课程 2-知识 3-考试
     */
    @Override
    public void setType(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_screenshot_guard_white_list.f_type</code>. 业务分类 1-课程 2-知识 3-考试
     */
    @Override
    public Integer getType() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>system.t_screenshot_guard_white_list.f_business_id</code>. 业务ID 默认ALL为全部
     */
    @Override
    public void setBusinessId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_screenshot_guard_white_list.f_business_id</code>. 业务ID 默认ALL为全部
     */
    @Override
    public String getBusinessId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_screenshot_guard_white_list.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_screenshot_guard_white_list.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, Integer, String, Long> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, Integer, String, Long> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ScreenshotGuardWhiteListRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ScreenshotGuardWhiteListRecord value2(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ScreenshotGuardWhiteListRecord value3(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ScreenshotGuardWhiteListRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ScreenshotGuardWhiteListRecord values(String value1, Integer value2, String value3, Long value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IScreenshotGuardWhiteList from) {
        setId(from.getId());
        setType(from.getType());
        setBusinessId(from.getBusinessId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IScreenshotGuardWhiteList> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ScreenshotGuardWhiteListRecord
     */
    public ScreenshotGuardWhiteListRecord() {
        super(ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST);
    }

    /**
     * Create a detached, initialised ScreenshotGuardWhiteListRecord
     */
    public ScreenshotGuardWhiteListRecord(String id, Integer type, String businessId, Long createTime) {
        super(ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST);

        set(0, id);
        set(1, type);
        set(2, businessId);
        set(3, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.ScreenshotGuardWhiteListEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.ScreenshotGuardWhiteListEntity pojo = (com.zxy.product.system.jooq.tables.pojos.ScreenshotGuardWhiteListEntity)source;
        pojo.into(this);
        return true;
    }
}
