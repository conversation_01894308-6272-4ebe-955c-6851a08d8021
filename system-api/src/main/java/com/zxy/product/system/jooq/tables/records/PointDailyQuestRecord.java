/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.PointDailyQuest;
import com.zxy.product.system.jooq.tables.interfaces.IPointDailyQuest;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;


/**
 * 每日任务表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PointDailyQuestRecord extends UpdatableRecordImpl<PointDailyQuestRecord> implements Record5<String, String, String, Integer, Integer>, IPointDailyQuest {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_point_daily_quest.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_point_daily_quest.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_point_daily_quest.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_point_daily_quest.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_point_daily_quest.f_point_rule_id</code>. 规则id
     */
    @Override
    public void setPointRuleId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_point_daily_quest.f_point_rule_id</code>. 规则id
     */
    @Override
    public String getPointRuleId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_point_daily_quest.f_finish_count</code>. 当日完成次数
     */
    @Override
    public void setFinishCount(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_point_daily_quest.f_finish_count</code>. 当日完成次数
     */
    @Override
    public Integer getFinishCount() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>system.t_point_daily_quest.f_trigger_count</code>. 触发次数(由于某些业务原因自己无法统计,因此在这里进行触发)
     */
    @Override
    public void setTriggerCount(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_point_daily_quest.f_trigger_count</code>. 触发次数(由于某些业务原因自己无法统计,因此在这里进行触发)
     */
    @Override
    public Integer getTriggerCount() {
        return (Integer) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Integer, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Integer, Integer> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return PointDailyQuest.POINT_DAILY_QUEST.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return PointDailyQuest.POINT_DAILY_QUEST.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return PointDailyQuest.POINT_DAILY_QUEST.POINT_RULE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return PointDailyQuest.POINT_DAILY_QUEST.FINISH_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return PointDailyQuest.POINT_DAILY_QUEST.TRIGGER_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getPointRuleId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getFinishCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getTriggerCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord value3(String value) {
        setPointRuleId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord value4(Integer value) {
        setFinishCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord value5(Integer value) {
        setTriggerCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuestRecord values(String value1, String value2, String value3, Integer value4, Integer value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPointDailyQuest from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setPointRuleId(from.getPointRuleId());
        setFinishCount(from.getFinishCount());
        setTriggerCount(from.getTriggerCount());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPointDailyQuest> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PointDailyQuestRecord
     */
    public PointDailyQuestRecord() {
        super(PointDailyQuest.POINT_DAILY_QUEST);
    }

    /**
     * Create a detached, initialised PointDailyQuestRecord
     */
    public PointDailyQuestRecord(String id, String memberId, String pointRuleId, Integer finishCount, Integer triggerCount) {
        super(PointDailyQuest.POINT_DAILY_QUEST);

        set(0, id);
        set(1, memberId);
        set(2, pointRuleId);
        set(3, finishCount);
        set(4, triggerCount);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.PointDailyQuestEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.PointDailyQuestEntity pojo = (com.zxy.product.system.jooq.tables.pojos.PointDailyQuestEntity)source;
        pojo.into(this);
        return true;
    }
}
