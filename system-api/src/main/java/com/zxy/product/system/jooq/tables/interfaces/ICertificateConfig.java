/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 证书配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICertificateConfig extends Serializable {

    /**
     * Setter for <code>system.t_certificate_config.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>system.t_certificate_config.f_type</code>. 证书类型：1课程、2专题、3培训班级、4考试、5直播、6讲师、7专家、8认证
     */
    public void setType(Integer value);

    /**
     * Getter for <code>system.t_certificate_config.f_type</code>. 证书类型：1课程、2专题、3培训班级、4考试、5直播、6讲师、7专家、8认证
     */
    public Integer getType();

    /**
     * Setter for <code>system.t_certificate_config.f_param_key</code>. 参数键
     */
    public void setParamKey(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_param_key</code>. 参数键
     */
    public String getParamKey();

    /**
     * Setter for <code>system.t_certificate_config.f_param_code</code>. 参数如{0},{1}
     */
    public void setParamCode(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_param_code</code>. 参数如{0},{1}
     */
    public String getParamCode();

    /**
     * Setter for <code>system.t_certificate_config.f_param_name</code>. 如姓名
     */
    public void setParamName(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_param_name</code>. 如姓名
     */
    public String getParamName();

    /**
     * Setter for <code>system.t_certificate_config.f_param_examples</code>. 如:示例：李成
     */
    public void setParamExamples(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_param_examples</code>. 如:示例：李成
     */
    public String getParamExamples();

    /**
     * Setter for <code>system.t_certificate_config.f_delete_flag</code>. 删除标识
     */
    public void setDeleteFlag(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_delete_flag</code>. 删除标识
     */
    public String getDeleteFlag();

    /**
     * Setter for <code>system.t_certificate_config.f_root_organization_id</code>. 根组织id
     */
    public void setRootOrganizationId(String value);

    /**
     * Getter for <code>system.t_certificate_config.f_root_organization_id</code>. 根组织id
     */
    public String getRootOrganizationId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICertificateConfig
     */
    public void from(com.zxy.product.system.jooq.tables.interfaces.ICertificateConfig from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICertificateConfig
     */
    public <E extends com.zxy.product.system.jooq.tables.interfaces.ICertificateConfig> E into(E into);
}
