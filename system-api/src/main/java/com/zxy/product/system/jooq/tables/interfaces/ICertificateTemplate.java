/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 证书模板表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICertificateTemplate extends Serializable {

    /**
     * Setter for <code>system.t_certificate_template.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>system.t_certificate_template.f_name</code>. 证书名称
     */
    public void setName(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_name</code>. 证书名称
     */
    public String getName();

    /**
     * Setter for <code>system.t_certificate_template.f_cover</code>. 证书封面
     */
    public void setCover(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_cover</code>. 证书封面
     */
    public String getCover();

    /**
     * Setter for <code>system.t_certificate_template.f_type</code>. 证书类型：1课程、2专题、3培训班级、4普通考试证书、5直播、6讲师、7专家、8网络部认证考试证书、9省公司认证考试证书
     */
    public void setType(Integer value);

    /**
     * Getter for <code>system.t_certificate_template.f_type</code>. 证书类型：1课程、2专题、3培训班级、4普通考试证书、5直播、6讲师、7专家、8网络部认证考试证书、9省公司认证考试证书
     */
    public Integer getType();

    /**
     * Setter for <code>system.t_certificate_template.f_is_default</code>. 是否默认：1是、2否
     */
    public void setIsDefault(Integer value);

    /**
     * Getter for <code>system.t_certificate_template.f_is_default</code>. 是否默认：1是、2否
     */
    public Integer getIsDefault();

    /**
     * Setter for <code>system.t_certificate_template.f_html_code</code>. 证书内容
     */
    public void setHtmlCode(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_html_code</code>. 证书内容
     */
    public String getHtmlCode();

    /**
     * Setter for <code>system.t_certificate_template.f_app_html_code</code>. app证书样式
     */
    public void setAppHtmlCode(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_app_html_code</code>. app证书样式
     */
    public String getAppHtmlCode();

    /**
     * Setter for <code>system.t_certificate_template.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>system.t_certificate_template.f_seq</code>. 排序
     */
    public void setSeq(Integer value);

    /**
     * Getter for <code>system.t_certificate_template.f_seq</code>. 排序
     */
    public Integer getSeq();

    /**
     * Setter for <code>system.t_certificate_template.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_certificate_template.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_certificate_template.f_delete_flag</code>. 删除标识
     */
    public void setDeleteFlag(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_delete_flag</code>. 删除标识
     */
    public String getDeleteFlag();

    /**
     * Setter for <code>system.t_certificate_template.f_root_organization_id</code>. 组织ID
     */
    public void setRootOrganizationId(String value);

    /**
     * Getter for <code>system.t_certificate_template.f_root_organization_id</code>. 组织ID
     */
    public String getRootOrganizationId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICertificateTemplate
     */
    public void from(com.zxy.product.system.jooq.tables.interfaces.ICertificateTemplate from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICertificateTemplate
     */
    public <E extends com.zxy.product.system.jooq.tables.interfaces.ICertificateTemplate> E into(E into);
}
