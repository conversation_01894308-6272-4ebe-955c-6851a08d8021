/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IRole;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RoleEntity extends BaseEntity implements IRole {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  parentId;
    private String  path;
    private String  organizationId;
    private String  createMemberId;
    private Long    codeLow;
    private Long    codeMiddle;
    private Long    codeHigh;
    private Integer init;
    private String  desc;
    private Integer    type;
    private Integer    childFlag;
    private Integer order;
    private String  menuId;

    public RoleEntity() {}

    public RoleEntity(RoleEntity value) {
        this.name = value.name;
        this.parentId = value.parentId;
        this.path = value.path;
        this.organizationId = value.organizationId;
        this.createMemberId = value.createMemberId;
        this.codeLow = value.codeLow;
        this.codeMiddle = value.codeMiddle;
        this.codeHigh = value.codeHigh;
        this.init = value.init;
        this.desc = value.desc;
        this.type = value.type;
        this.childFlag = value.childFlag;
        this.order = value.order;
        this.menuId = value.menuId;
    }

    public RoleEntity(
        String  id,
        String  name,
        String  parentId,
        String  path,
        String  organizationId,
        String  createMemberId,
        Long    createTime,
        Long    codeLow,
        Long    codeMiddle,
        Long    codeHigh,
        Integer init,
        String  desc,
        Integer    type,
        Integer    childFlag,
        Integer order,
        String  menuId
    ) {
        super.setId(id);
        this.name = name;
        this.parentId = parentId;
        this.path = path;
        this.organizationId = organizationId;
        this.createMemberId = createMemberId;
        super.setCreateTime(createTime);
        this.codeLow = codeLow;
        this.codeMiddle = codeMiddle;
        this.codeHigh = codeHigh;
        this.init = init;
        this.desc = desc;
        this.type = type;
        this.childFlag = childFlag;
        this.order = order;
        this.menuId = menuId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getParentId() {
        return this.parentId;
    }

    @Override
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    @Override
    public String getPath() {
        return this.path;
    }

    @Override
    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getCodeLow() {
        return this.codeLow;
    }

    @Override
    public void setCodeLow(Long codeLow) {
        this.codeLow = codeLow;
    }

    @Override
    public Long getCodeMiddle() {
        return this.codeMiddle;
    }

    @Override
    public void setCodeMiddle(Long codeMiddle) {
        this.codeMiddle = codeMiddle;
    }

    @Override
    public Long getCodeHigh() {
        return this.codeHigh;
    }

    @Override
    public void setCodeHigh(Long codeHigh) {
        this.codeHigh = codeHigh;
    }

    @Override
    public Integer getInit() {
        return this.init;
    }

    @Override
    public void setInit(Integer init) {
        this.init = init;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getChildFlag() {
        return this.childFlag;
    }

    @Override
    public void setChildFlag(Integer childFlag) {
        this.childFlag = childFlag;
    }

    @Override
    public Integer getOrder() {
        return this.order;
    }

    @Override
    public void setOrder(Integer order) {
        this.order = order;
    }

    @Override
    public String getMenuId() {
        return this.menuId;
    }

    @Override
    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("RoleEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(parentId);
        sb.append(", ").append(path);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(codeLow);
        sb.append(", ").append(codeMiddle);
        sb.append(", ").append(codeHigh);
        sb.append(", ").append(init);
        sb.append(", ").append(desc);
        sb.append(", ").append(type);
        sb.append(", ").append(childFlag);
        sb.append(", ").append(order);
        sb.append(", ").append(menuId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IRole from) {
        setId(from.getId());
        setName(from.getName());
        setParentId(from.getParentId());
        setPath(from.getPath());
        setOrganizationId(from.getOrganizationId());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setCodeLow(from.getCodeLow());
        setCodeMiddle(from.getCodeMiddle());
        setCodeHigh(from.getCodeHigh());
        setInit(from.getInit());
        setDesc(from.getDesc());
        setType(from.getType());
        setChildFlag(from.getChildFlag());
        setOrder(from.getOrder());
        setMenuId(from.getMenuId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IRole> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends RoleEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.RoleRecord r = new com.zxy.product.system.jooq.tables.records.RoleRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.ID, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.NAME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.NAME, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.NAME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.PARENT_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.PARENT_ID, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.PARENT_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.PATH) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.PATH, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.PATH));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.ORGANIZATION_ID, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_MEMBER_ID, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_LOW) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_LOW, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_LOW));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_MIDDLE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_MIDDLE, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_MIDDLE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_HIGH) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_HIGH, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CODE_HIGH));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.INIT) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.INIT, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.INIT));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.DESC) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.DESC, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.DESC));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.TYPE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.TYPE, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.CHILD_FLAG) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.CHILD_FLAG, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.CHILD_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.ORDER) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.ORDER, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.ORDER));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.Role.ROLE.MENU_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.Role.ROLE.MENU_ID, record.getValue(com.zxy.product.system.jooq.tables.Role.ROLE.MENU_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
