/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.ShareRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 分享模板表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Share extends TableImpl<ShareRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_share</code>
     */
    public static final Share SHARE = new Share();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ShareRecord> getRecordType() {
        return ShareRecord.class;
    }

    /**
     * The column <code>system.t_share.f_id</code>.
     */
    public final TableField<ShareRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>system.t_share.f_organization_id</code>. 组织ID
     */
    public final TableField<ShareRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织ID");

    /**
     * The column <code>system.t_share.f_name</code>. 分享类型名
     */
    public final TableField<ShareRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "分享类型名");

    /**
     * The column <code>system.t_share.f_code</code>. 分享类型代码
     */
    public final TableField<ShareRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "分享类型代码");

    /**
     * The column <code>system.t_share.f_content</code>. 分享内容
     */
    public final TableField<ShareRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB, this, "分享内容");

    /**
     * The column <code>system.t_share.f_create_time</code>.
     */
    public final TableField<ShareRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * The column <code>system.t_share.f_create_member_id</code>.
     */
    public final TableField<ShareRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * Create a <code>system.t_share</code> table reference
     */
    public Share() {
        this("t_share", null);
    }

    /**
     * Create an aliased <code>system.t_share</code> table reference
     */
    public Share(String alias) {
        this(alias, SHARE);
    }

    private Share(String alias, Table<ShareRecord> aliased) {
        this(alias, aliased, null);
    }

    private Share(String alias, Table<ShareRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "分享模板表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ShareRecord> getPrimaryKey() {
        return Keys.KEY_T_SHARE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ShareRecord>> getKeys() {
        return Arrays.<UniqueKey<ShareRecord>>asList(Keys.KEY_T_SHARE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Share as(String alias) {
        return new Share(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Share rename(String name) {
        return new Share(name, null);
    }
}
