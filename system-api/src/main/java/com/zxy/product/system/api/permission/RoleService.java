package com.zxy.product.system.api.permission;

import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.Role;

@RemoteService
public interface RoleService {

    @Transactional
    Role insert(String memberId, String name, String organizationId, String parentId, String[] menuIds, Optional<String> desc, Optional<Integer> childFlag);

    @Transactional
    Role update(String memberId, String id, Optional<String> name, Optional<String> organizationId, Optional<String> parentId, String[] menuIds,
                String rootOrganizationId, Optional<Integer> order, Optional<String> desc, Optional<Integer> childFlag);

    @Transactional
    Role updateOrder(String id, Integer order);

    @Transactional
    Boolean updateChildFlag(String id, Integer childFlag);

    @Transactional
    int delete(String id);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Role get(String id, Optional<String> grantId, String currentUserId, String uri, Optional<String> memberId);

    /** 查询用户能看到的角色,含分页 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Role> findPage(int page, int pageSize, String memberId, Optional<String> name, Optional<String> parentName, Optional<String> orgainizationId,
                               Optional<Long> createTimeStart, Optional<Long> createTimeEnd, Integer contain, Optional<Integer> type, Optional<String> memberIdOptional,
                               Optional<Integer> childFlag, String uri);

    /** 查询当前用户被授权的角色,以及基于此角色,他自己创建过的子角色,和衍生下去的子角色 */
	List<Role> findGrantRoles(String memberId, String uri);
    /**
     * 查询当前用户被授权的角色,以及基于此角色,他自己创建过的子角色,和衍生下去的子角色
     * 以及元角色
     */
    PagedResult<Role> findGrantRolePage(String memberId, String uri, Optional<String> content, Optional<Integer> type);

    /** 查询当前用户被授权过的角色(仅查被授权过的,不含其子) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Role>findByMemberId(String memberid);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int getRoleLengthByMemberId(String memberId);

    /** 查询用户被授予的角色(包括角色所属部门名称、授权的组织信息) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Role> findWithGranted(String memberId);

    /** 根据角色id查询用户 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Member> findMemberByRole(Integer page, Integer pageSize, String roleId, String organizationId);

    /** 查询有角色的用户
     * @param contain
     * @param content */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Member> findMemberWithRole(Integer page, Integer pageSize, String memberId, String uri, Optional<String> organizationId, Optional<String> fullName,
                                           Optional<String> name, Optional<String> positionId, Integer contain, Optional<String> content);

    /** 查询所有有角色的用户 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findMemberWithRole(String memberId, String uri, String organizationId);

    /** 管理员名单导出 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findAdminMemberForExport(String memberId, String uri, Optional<String> organizationId, Optional<String> fullName, Optional<String> name,
                                          Optional<String> positionId, Integer contain, String rootOrganizationId, int page, int pageSize);

    /** 管理员名单导出 查询人员用来排序 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findAdminMemberIdForExport(String memberId, String uri, Optional<String> organizationId, Optional<String> fullName, Optional<String> name,
                                            Optional<String> positionId, Integer contain, String rootOrganizationId);

    /**
     * 查询当前登录用户是否有管理员角色（仅为登录提供）
     *
     * @param memberId 当前登录用户Id
     * @return 查询当前登录用户是否有管理员角色
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer whetherAdministrator(String memberId);

    /**
     * 当前管理员是有admin角色
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Boolean isContainsAdminRole(String memberId, String roleId, String organizationId);

    /**
     * 查询当前角色所有子角色
     * @param roleId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Role> listsubRole(String roleId);
}

