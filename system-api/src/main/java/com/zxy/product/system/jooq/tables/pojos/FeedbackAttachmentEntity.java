/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IFeedbackAttachment;

import javax.annotation.Generated;


/**
 * 反馈附件表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FeedbackAttachmentEntity extends BaseEntity implements IFeedbackAttachment {

    private static final long serialVersionUID = 1L;

    private String feedbackId;
    private String attachmentId;

    public FeedbackAttachmentEntity() {}

    public FeedbackAttachmentEntity(FeedbackAttachmentEntity value) {
        this.feedbackId = value.feedbackId;
        this.attachmentId = value.attachmentId;
    }

    public FeedbackAttachmentEntity(
        String id,
        String feedbackId,
        String attachmentId,
        Long   createTime
    ) {
        super.setId(id);
        this.feedbackId = feedbackId;
        this.attachmentId = attachmentId;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getFeedbackId() {
        return this.feedbackId;
    }

    @Override
    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId;
    }

    @Override
    public String getAttachmentId() {
        return this.attachmentId;
    }

    @Override
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("FeedbackAttachmentEntity (");

        sb.append(getId());
        sb.append(", ").append(feedbackId);
        sb.append(", ").append(attachmentId);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IFeedbackAttachment from) {
        setId(from.getId());
        setFeedbackId(from.getFeedbackId());
        setAttachmentId(from.getAttachmentId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IFeedbackAttachment> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends FeedbackAttachmentEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.FeedbackAttachmentRecord r = new com.zxy.product.system.jooq.tables.records.FeedbackAttachmentRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ID, record.getValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.FEEDBACK_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.FEEDBACK_ID, record.getValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.FEEDBACK_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ATTACHMENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ATTACHMENT_ID, record.getValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.ATTACHMENT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT.CREATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
