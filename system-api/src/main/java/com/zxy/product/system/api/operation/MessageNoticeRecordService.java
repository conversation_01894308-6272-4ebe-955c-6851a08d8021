package com.zxy.product.system.api.operation;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.MessageNoticeRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 站内信通知记录
 * <AUTHOR>
 *
 */
@RemoteService
public interface MessageNoticeRecordService {

    /**
     * 消息列表
     * @param page
     * @param pageSize
     * @param readStatus
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<MessageNoticeRecord> find(Integer page, Integer pageSize, Optional<Integer> readStatus, String currentUserId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<MessageNoticeRecord> findByList(Integer size, Optional<Integer> readStatus, String currentUserId);
    /**
     * 消息详情，并标记已读
     * @param id
     * @return
     */
    @Transactional
    MessageNoticeRecord get(String id);

    @Transactional
    String[] deleteAll(String[] ids);

    /**
     * 标记已读
     * @param ids
     * @return
     */
    @Transactional
    String[] markRead(String[] ids);

    /**
     * 查看消息数
     * @return
     */
    @Transactional
    int getCount(Optional<Integer> readStatus, String currentUserId);

}
