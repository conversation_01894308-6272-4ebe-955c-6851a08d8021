package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.MenuEntity;

import java.util.List;

public class Menu extends MenuEntity {

    private static final long serialVersionUID = 7581974508462864109L;
    public static final Integer INIT_YES = 0; // 是系统默认菜单
    public static final Integer INIT_NO = 1; // 不是系统默认菜单
    public static final Integer INIT_OTHER = 2; // 为系统监控扩展

    public static final int ADD = 1; // 新增
    public static final int UPDATE = 0; // 修改
    public static final int DELETE = -1; // 删除

    private String parentName;
    private boolean external; // 是否是外部链接

    private Organization organization;
    private List<Menu> children;

    private String roleId;
    private String operatorTypes;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getOperatorTypes() {
        return operatorTypes;
    }

    public void setOperatorTypes(String operatorTypes) {
        this.operatorTypes = operatorTypes;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

	public List<Menu> getChildren() {
		return children;
	}

	public void setChildren(List<Menu> children) {
		this.children = children;
	}

	public boolean getExternal() {
		return external;
	}

	public void setExternal(boolean external) {
		this.external = external;
	}

}
