/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IDeleteDataSystem;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 删除记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeleteDataSystemEntity extends BaseEntity implements IDeleteDataSystem {

    private static final long serialVersionUID = 1L;

    private String    databaseName;
    private String    tableName;
    private String    businessId;
    private Timestamp modifyDate;
    private String    companyId;

    public DeleteDataSystemEntity() {}

    public DeleteDataSystemEntity(DeleteDataSystemEntity value) {
        this.databaseName = value.databaseName;
        this.tableName = value.tableName;
        this.businessId = value.businessId;
        this.modifyDate = value.modifyDate;
        this.companyId = value.companyId;
    }

    public DeleteDataSystemEntity(
        String    id,
        String    databaseName,
        String    tableName,
        String    businessId,
        Long      createTime,
        Timestamp modifyDate,
        String    companyId
    ) {
        super.setId(id);
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.businessId = businessId;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
        this.companyId = companyId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getDatabaseName() {
        return this.databaseName;
    }

    @Override
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    @Override
    public String getTableName() {
        return this.tableName;
    }

    @Override
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public String getBusinessId() {
        return this.businessId;
    }

    @Override
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String getCompanyId() {
        return this.companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DeleteDataSystemEntity (");

        sb.append(getId());
        sb.append(", ").append(databaseName);
        sb.append(", ").append(tableName);
        sb.append(", ").append(businessId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(companyId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IDeleteDataSystem from) {
        setId(from.getId());
        setDatabaseName(from.getDatabaseName());
        setTableName(from.getTableName());
        setBusinessId(from.getBusinessId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IDeleteDataSystem> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends DeleteDataSystemEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.DeleteDataSystemRecord r = new com.zxy.product.system.jooq.tables.records.DeleteDataSystemRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.ID, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.DATABASE_NAME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.DATABASE_NAME, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.DATABASE_NAME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.TABLE_NAME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.TABLE_NAME, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.TABLE_NAME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.BUSINESS_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.BUSINESS_ID, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.BUSINESS_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.MODIFY_DATE, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.MODIFY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.COMPANY_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.COMPANY_ID, record.getValue(com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM.COMPANY_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
