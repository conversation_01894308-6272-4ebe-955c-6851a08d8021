/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.ICoverPattern;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 封面栏目配置项表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CoverPatternEntity extends BaseEntity implements ICoverPattern {

    private static final long serialVersionUID = 1L;

    private String    coverLabel;
    private String    coverKey;
    private String    coverSettingJson;
    private Integer   showOrder;
    private String    sizeId;
    private String    updateMemberId;
    private Timestamp modifyDate;
    private String    companyId;

    public CoverPatternEntity() {}

    public CoverPatternEntity(CoverPatternEntity value) {
        this.coverLabel = value.coverLabel;
        this.coverKey = value.coverKey;
        this.coverSettingJson = value.coverSettingJson;
        this.showOrder = value.showOrder;
        this.sizeId = value.sizeId;
        this.updateMemberId = value.updateMemberId;
        this.modifyDate = value.modifyDate;
        this.companyId = value.companyId;
    }

    public CoverPatternEntity(
        String    id,
        String    coverLabel,
        String    coverKey,
        String    coverSettingJson,
        Integer   showOrder,
        String    sizeId,
        String    updateMemberId,
        Long      createTime,
        Timestamp modifyDate,
        String    companyId
    ) {
        super.setId(id);
        this.coverLabel = coverLabel;
        this.coverKey = coverKey;
        this.coverSettingJson = coverSettingJson;
        this.showOrder = showOrder;
        this.sizeId = sizeId;
        this.updateMemberId = updateMemberId;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
        this.companyId = companyId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getCoverLabel() {
        return this.coverLabel;
    }

    @Override
    public void setCoverLabel(String coverLabel) {
        this.coverLabel = coverLabel;
    }

    @Override
    public String getCoverKey() {
        return this.coverKey;
    }

    @Override
    public void setCoverKey(String coverKey) {
        this.coverKey = coverKey;
    }

    @Override
    public String getCoverSettingJson() {
        return this.coverSettingJson;
    }

    @Override
    public void setCoverSettingJson(String coverSettingJson) {
        this.coverSettingJson = coverSettingJson;
    }

    @Override
    public Integer getShowOrder() {
        return this.showOrder;
    }

    @Override
    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    @Override
    public String getSizeId() {
        return this.sizeId;
    }

    @Override
    public void setSizeId(String sizeId) {
        this.sizeId = sizeId;
    }

    @Override
    public String getUpdateMemberId() {
        return this.updateMemberId;
    }

    @Override
    public void setUpdateMemberId(String updateMemberId) {
        this.updateMemberId = updateMemberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String getCompanyId() {
        return this.companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CoverPatternEntity (");

        sb.append(getId());
        sb.append(", ").append(coverLabel);
        sb.append(", ").append(coverKey);
        sb.append(", ").append(coverSettingJson);
        sb.append(", ").append(showOrder);
        sb.append(", ").append(sizeId);
        sb.append(", ").append(updateMemberId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(companyId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICoverPattern from) {
        setId(from.getId());
        setCoverLabel(from.getCoverLabel());
        setCoverKey(from.getCoverKey());
        setCoverSettingJson(from.getCoverSettingJson());
        setShowOrder(from.getShowOrder());
        setSizeId(from.getSizeId());
        setUpdateMemberId(from.getUpdateMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICoverPattern> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CoverPatternEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.CoverPatternRecord r = new com.zxy.product.system.jooq.tables.records.CoverPatternRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.ID, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_LABEL) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_LABEL, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_LABEL));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_KEY) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_KEY, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_KEY));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_SETTING_JSON) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_SETTING_JSON, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COVER_SETTING_JSON));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SHOW_ORDER) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SHOW_ORDER, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SHOW_ORDER));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SIZE_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SIZE_ID, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.SIZE_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.UPDATE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.UPDATE_MEMBER_ID, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.UPDATE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.MODIFY_DATE, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.MODIFY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COMPANY_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COMPANY_ID, record.getValue(com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN.COMPANY_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
