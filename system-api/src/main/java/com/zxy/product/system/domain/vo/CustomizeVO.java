package com.zxy.product.system.domain.vo;

import java.io.Serializable;

/**
 * 首页配置项自定义VO，包含所有find-by-ids返回的所有字段
 * <AUTHOR>
 * @date 2024年11月11日 10:06
 */
public class CustomizeVO implements Serializable {
    private static final long serialVersionUID = -251691179771481734L;

    /**主键*/
    private String id;

    /**首页配置Id*/
    private String cfgId;

    /**首页配置项填充数据Id*/
    private String dataId;

    /**首页配置项填充数据类型*/
    private Integer dataType;

    /**首页配置项填充图片地址*/
    private String url;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getCfgId() { return cfgId; }

    public void setCfgId(String cfgId) { this.cfgId = cfgId; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public Integer getDataType() { return dataType; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    @Override
    public String toString() {
        return "CustomizeVO{" +
                "id='" + id + '\'' +
                ", cfgId='" + cfgId + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataType=" + dataType +
                ", url='" + url + '\'' +
                '}';
    }
}
