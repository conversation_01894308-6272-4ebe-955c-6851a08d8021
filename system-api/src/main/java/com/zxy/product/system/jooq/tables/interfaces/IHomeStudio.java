/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 首页专家工作室配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IHomeStudio extends Serializable {

    /**
     * Setter for <code>system.t_home_studio.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_home_studio.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>system.t_home_studio.f_module_config_id</code>. 配置模块ID
     */
    public void setModuleConfigId(String value);

    /**
     * Getter for <code>system.t_home_studio.f_module_config_id</code>. 配置模块ID
     */
    public String getModuleConfigId();

    /**
     * Setter for <code>system.t_home_studio.f_studio_id</code>. 工作室id
     */
    public void setStudioId(String value);

    /**
     * Getter for <code>system.t_home_studio.f_studio_id</code>. 工作室id
     */
    public String getStudioId();

    /**
     * Setter for <code>system.t_home_studio.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_home_studio.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_home_studio.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>system.t_home_studio.f_order</code>. 排序
     */
    public Integer getOrder();

    /**
     * Setter for <code>system.t_home_studio.f_client_type</code>. 0 PC端 1 App端
     */
    public void setClientType(Integer value);

    /**
     * Getter for <code>system.t_home_studio.f_client_type</code>. 0 PC端 1 App端
     */
    public Integer getClientType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IHomeStudio
     */
    public void from(IHomeStudio from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IHomeStudio
     */
    public <E extends IHomeStudio> E into(E into);
}
