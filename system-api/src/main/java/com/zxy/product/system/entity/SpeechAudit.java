package com.zxy.product.system.entity;

import java.util.List;

import com.zxy.product.system.jooq.tables.pojos.SpeechAuditEntity;
/**
 * 审核记录
 * <AUTHOR>
 *
 */
public class SpeechAudit extends SpeechAuditEntity{
    private static final long serialVersionUID = -4159327088414450829L;
    public static final String URI = "operation/speech-audit";
    private Member member;
    private Organization organization;
    //部门名称
    private String orgName;
    //送审人name
    private String createMemberName;
    //审核人name
    private String auditMemberName;
    //被举报人名称
    private String beMemberName;
    //被举报人头像
    private String beHeadPortrait;
    //举报列表
    private List<CommentAccuse> commentAccuses;
    //部门名称
    private String organizationName;
    //头像
    private String headPortrait;
    //查出来的消息模板
    private String messageContent;
    //记录消息模板的template
    private String templateCode;

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getBeMemberName() {
        return beMemberName;
    }

    public void setBeMemberName(String beMemberName) {
        this.beMemberName = beMemberName;
    }

    public String getHeadPortrait() {
        return headPortrait;
    }

    public void setHeadPortrait(String headPortrait) {
        this.headPortrait = headPortrait;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public List<CommentAccuse> getCommentAccuses() {
        return commentAccuses;
    }

    public void setCommentAccuses(List<CommentAccuse> commentAccuses) {
        this.commentAccuses = commentAccuses;
    }

    public String getBeHeadPortrait() {
        return beHeadPortrait;
    }

    public void setBeHeadPortrait(String beHeadPortrait) {
        this.beHeadPortrait = beHeadPortrait;
    }

    public Member getMember() {
        return member;
    }
    public void setMember(Member member) {
        this.member = member;
    }
    public Organization getOrganization() {
        return organization;
    }
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    public String getCreateMemberName() {
        return createMemberName;
    }
    public void setCreateMemberName(String createMemberName) {
        this.createMemberName = createMemberName;
    }
    public String getAuditMemberName() {
        return auditMemberName;
    }
    public void setAuditMemberName(String auditMemberName) {
        this.auditMemberName = auditMemberName;
    }
    public String getOrgName() {
        return orgName;
    }
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

}
