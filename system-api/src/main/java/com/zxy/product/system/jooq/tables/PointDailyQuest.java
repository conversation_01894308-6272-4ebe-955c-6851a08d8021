/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.PointDailyQuestRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 每日任务表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PointDailyQuest extends TableImpl<PointDailyQuestRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_point_daily_quest</code>
     */
    public static final PointDailyQuest POINT_DAILY_QUEST = new PointDailyQuest();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PointDailyQuestRecord> getRecordType() {
        return PointDailyQuestRecord.class;
    }

    /**
     * The column <code>system.t_point_daily_quest.f_id</code>. 主键
     */
    public final TableField<PointDailyQuestRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>system.t_point_daily_quest.f_member_id</code>. 用户id
     */
    public final TableField<PointDailyQuestRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>system.t_point_daily_quest.f_point_rule_id</code>. 规则id
     */
    public final TableField<PointDailyQuestRecord, String> POINT_RULE_ID = createField("f_point_rule_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "规则id");

    /**
     * The column <code>system.t_point_daily_quest.f_finish_count</code>. 当日完成次数
     */
    public final TableField<PointDailyQuestRecord, Integer> FINISH_COUNT = createField("f_finish_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "当日完成次数");

    /**
     * The column <code>system.t_point_daily_quest.f_trigger_count</code>. 触发次数(由于某些业务原因自己无法统计,因此在这里进行触发)
     */
    public final TableField<PointDailyQuestRecord, Integer> TRIGGER_COUNT = createField("f_trigger_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "触发次数(由于某些业务原因自己无法统计,因此在这里进行触发)");

    /**
     * Create a <code>system.t_point_daily_quest</code> table reference
     */
    public PointDailyQuest() {
        this("t_point_daily_quest", null);
    }

    /**
     * Create an aliased <code>system.t_point_daily_quest</code> table reference
     */
    public PointDailyQuest(String alias) {
        this(alias, POINT_DAILY_QUEST);
    }

    private PointDailyQuest(String alias, Table<PointDailyQuestRecord> aliased) {
        this(alias, aliased, null);
    }

    private PointDailyQuest(String alias, Table<PointDailyQuestRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "每日任务表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PointDailyQuestRecord> getPrimaryKey() {
        return Keys.KEY_T_POINT_DAILY_QUEST_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PointDailyQuestRecord>> getKeys() {
        return Arrays.<UniqueKey<PointDailyQuestRecord>>asList(Keys.KEY_T_POINT_DAILY_QUEST_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointDailyQuest as(String alias) {
        return new PointDailyQuest(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PointDailyQuest rename(String name) {
        return new PointDailyQuest(name, null);
    }
}
