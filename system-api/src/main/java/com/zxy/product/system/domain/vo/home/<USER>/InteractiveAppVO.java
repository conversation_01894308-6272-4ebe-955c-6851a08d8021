package com.zxy.product.system.domain.vo.home.Interactive;

import java.io.Serializable;

/**
 * 首页互动学习：覆写App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:27
 */
public class InteractiveAppVO extends InteractiveParentVO implements Serializable {
    private static final long serialVersionUID = -7599249525756812189L;

    /**互动学习：配置Id*/
    private String id;

    /**互动学习：数据简介*/
    private String dataExt;

    /**互动学习：数据URL跳转地址*/
    private String url;

    /**互动学习：数据名称*/
    private String dataName;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    @Override
    public String toString() {
        return "InteractiveAppVO{" +
                "id='" + id + '\'' +
                ", dataExt='" + dataExt + '\'' +
                ", url='" + url + '\'' +
                ", dataName='" + dataName + '\'' +
                '}';
    }
}
