/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IPointDetail_4;

import javax.annotation.Generated;


/**
 * 用户积分详情流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PointDetail_4Entity extends BaseEntity implements IPointDetail_4 {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  ruleId;
    private Integer pointChange;
    private Integer pointBalance;
    private Integer pointTotal;
    private String  pointDetailDesc;

    public PointDetail_4Entity() {}

    public PointDetail_4Entity(PointDetail_4Entity value) {
        this.memberId = value.memberId;
        this.ruleId = value.ruleId;
        this.pointChange = value.pointChange;
        this.pointBalance = value.pointBalance;
        this.pointTotal = value.pointTotal;
        this.pointDetailDesc = value.pointDetailDesc;
    }

    public PointDetail_4Entity(
        String  id,
        String  memberId,
        String  ruleId,
        Integer pointChange,
        Integer pointBalance,
        Integer pointTotal,
        Long    createTime,
        String  pointDetailDesc
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.ruleId = ruleId;
        this.pointChange = pointChange;
        this.pointBalance = pointBalance;
        this.pointTotal = pointTotal;
        super.setCreateTime(createTime);
        this.pointDetailDesc = pointDetailDesc;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getRuleId() {
        return this.ruleId;
    }

    @Override
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    @Override
    public Integer getPointChange() {
        return this.pointChange;
    }

    @Override
    public void setPointChange(Integer pointChange) {
        this.pointChange = pointChange;
    }

    @Override
    public Integer getPointBalance() {
        return this.pointBalance;
    }

    @Override
    public void setPointBalance(Integer pointBalance) {
        this.pointBalance = pointBalance;
    }

    @Override
    public Integer getPointTotal() {
        return this.pointTotal;
    }

    @Override
    public void setPointTotal(Integer pointTotal) {
        this.pointTotal = pointTotal;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getPointDetailDesc() {
        return this.pointDetailDesc;
    }

    @Override
    public void setPointDetailDesc(String pointDetailDesc) {
        this.pointDetailDesc = pointDetailDesc;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PointDetail_4Entity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(ruleId);
        sb.append(", ").append(pointChange);
        sb.append(", ").append(pointBalance);
        sb.append(", ").append(pointTotal);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(pointDetailDesc);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPointDetail_4 from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setRuleId(from.getRuleId());
        setPointChange(from.getPointChange());
        setPointBalance(from.getPointBalance());
        setPointTotal(from.getPointTotal());
        setCreateTime(from.getCreateTime());
        setPointDetailDesc(from.getPointDetailDesc());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPointDetail_4> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PointDetail_4Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.PointDetail_4Record r = new com.zxy.product.system.jooq.tables.records.PointDetail_4Record();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.ID, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.MEMBER_ID, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.RULE_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.RULE_ID, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.RULE_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_CHANGE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_CHANGE, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_CHANGE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_BALANCE) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_BALANCE, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_BALANCE));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_TOTAL) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_TOTAL, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_TOTAL));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_DETAIL_DESC) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_DETAIL_DESC, record.getValue(com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4.POINT_DETAIL_DESC));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
