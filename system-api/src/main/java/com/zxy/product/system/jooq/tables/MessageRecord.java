/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.MessageRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 消息发送记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MessageRecord extends TableImpl<MessageRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_message_record</code>
     */
    public static final MessageRecord MESSAGE_RECORD = new MessageRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MessageRecordRecord> getRecordType() {
        return MessageRecordRecord.class;
    }

    /**
     * The column <code>system.t_message_record.f_id</code>. ID
     */
    public final TableField<MessageRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>system.t_message_record.f_subject</code>. 主题
     */
    public final TableField<MessageRecordRecord, String> SUBJECT = createField("f_subject", org.jooq.impl.SQLDataType.CLOB, this, "主题");

    /**
     * The column <code>system.t_message_record.f_content</code>. 消息内容
     */
    public final TableField<MessageRecordRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB, this, "消息内容");

    /**
     * The column <code>system.t_message_record.f_text_content</code>. 消息text内容
     */
    public final TableField<MessageRecordRecord, String> TEXT_CONTENT = createField("f_text_content", org.jooq.impl.SQLDataType.CLOB, this, "消息text内容");

    /**
     * The column <code>system.t_message_record.f_sender_id</code>. 发件人id
     */
    public final TableField<MessageRecordRecord, String> SENDER_ID = createField("f_sender_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "发件人id");

    /**
     * The column <code>system.t_message_record.f_receiver_id</code>. 收件人id
     */
    public final TableField<MessageRecordRecord, String> RECEIVER_ID = createField("f_receiver_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "收件人id");

    /**
     * The column <code>system.t_message_record.f_type</code>. 消息类型 1站内消息 2邮件 3APP 4短信
     */
    public final TableField<MessageRecordRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "消息类型 1站内消息 2邮件 3APP 4短信");

    /**
     * The column <code>system.t_message_record.f_read_status</code>. 阅读状态：0未读(默认) 1已读
     */
    public final TableField<MessageRecordRecord, Integer> READ_STATUS = createField("f_read_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "阅读状态：0未读(默认) 1已读");

    /**
     * The column <code>system.t_message_record.f_business_id</code>. 主业务id
     */
    public final TableField<MessageRecordRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主业务id");

    /**
     * The column <code>system.t_message_record.f_business_code</code>. 主业务类型code
     */
    public final TableField<MessageRecordRecord, String> BUSINESS_CODE = createField("f_business_code", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "主业务类型code");

    /**
     * The column <code>system.t_message_record.f_organization_id</code>. 组织id
     */
    public final TableField<MessageRecordRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>system.t_message_record.f_create_time</code>. 创建时间
     */
    public final TableField<MessageRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>system.t_message_record.f_attr</code>. 消息属性:0消息通知 1@我
     */
    public final TableField<MessageRecordRecord, Integer> ATTR = createField("f_attr", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "消息属性:0消息通知 1@我");

    /**
     * Create a <code>system.t_message_record</code> table reference
     */
    public MessageRecord() {
        this("t_message_record", null);
    }

    /**
     * Create an aliased <code>system.t_message_record</code> table reference
     */
    public MessageRecord(String alias) {
        this(alias, MESSAGE_RECORD);
    }

    private MessageRecord(String alias, Table<MessageRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private MessageRecord(String alias, Table<MessageRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "消息发送记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MessageRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_MESSAGE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MessageRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<MessageRecordRecord>>asList(Keys.KEY_T_MESSAGE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MessageRecord as(String alias) {
        return new MessageRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MessageRecord rename(String name) {
        return new MessageRecord(name, null);
    }
}
