package com.zxy.product.system.domain.vo.home.customize;

import java.io.Serializable;

/**
 * 首页自定义：PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:54
 */
public class CustomizePcVO extends CustomizeParentVO implements Serializable {
    private static final long serialVersionUID = -9140164553559727523L;

    /**自定义：配置Id*/
    private String id;

    /**自定义：资源Id*/
    private String dataId;

    /**自定义：资源类型*/
    private Integer dataType;

    /**自定义：资源名称*/
    private String dataName;

    /**自定义：资源图片地址*/
    private String imagePath;

    /**自定义：资源图片URL跳转地址*/
    private String url;

    /**自定义：资源简介*/
    private String dataExt;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public Integer getDataType() { return dataType; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public String getImagePath() { return imagePath; }

    public void setImagePath(String imagePath) { this.imagePath = imagePath; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    @Override
    public String toString() {
        return "CustomizePcVO{" +
                "id='" + id + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataType=" + dataType +
                ", dataName='" + dataName + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", url='" + url + '\'' +
                ", dataExt='" + dataExt + '\'' +
                '}';
    }
}
