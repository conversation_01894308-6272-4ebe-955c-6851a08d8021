/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.RoleMenuRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RoleMenu extends TableImpl<RoleMenuRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_role_menu</code>
     */
    public static final RoleMenu ROLE_MENU = new RoleMenu();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RoleMenuRecord> getRecordType() {
        return RoleMenuRecord.class;
    }

    /**
     * The column <code>system.t_role_menu.f_id</code>. ID
     */
    public final TableField<RoleMenuRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>system.t_role_menu.f_role_id</code>. 角色ID
     */
    public final TableField<RoleMenuRecord, String> ROLE_ID = createField("f_role_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "角色ID");

    /**
     * The column <code>system.t_role_menu.f_menu_id</code>. 菜单ID
     */
    public final TableField<RoleMenuRecord, String> MENU_ID = createField("f_menu_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "菜单ID");

    /**
     * The column <code>system.t_role_menu.f_create_time</code>. 创建时间
     */
    public final TableField<RoleMenuRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>system.t_role_menu</code> table reference
     */
    public RoleMenu() {
        this("t_role_menu", null);
    }

    /**
     * Create an aliased <code>system.t_role_menu</code> table reference
     */
    public RoleMenu(String alias) {
        this(alias, ROLE_MENU);
    }

    private RoleMenu(String alias, Table<RoleMenuRecord> aliased) {
        this(alias, aliased, null);
    }

    private RoleMenu(String alias, Table<RoleMenuRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RoleMenuRecord> getPrimaryKey() {
        return Keys.KEY_T_ROLE_MENU_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RoleMenuRecord>> getKeys() {
        return Arrays.<UniqueKey<RoleMenuRecord>>asList(Keys.KEY_T_ROLE_MENU_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RoleMenu as(String alias) {
        return new RoleMenu(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RoleMenu rename(String name) {
        return new RoleMenu(name, null);
    }
}
