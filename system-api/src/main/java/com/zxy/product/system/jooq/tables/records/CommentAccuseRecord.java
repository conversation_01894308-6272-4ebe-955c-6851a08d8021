/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.CommentAccuse;
import com.zxy.product.system.jooq.tables.interfaces.ICommentAccuse;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 讨论区举报记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CommentAccuseRecord extends UpdatableRecordImpl<CommentAccuseRecord> implements Record9<String, String, String, String, Long, Integer, String, Integer, String>, ICommentAccuse {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_comment_accuse.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_type</code>. 举报分类id：1违反法律 2内容不准确 3垃圾信息 4其他
     */
    @Override
    public void setType(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_type</code>. 举报分类id：1违反法律 2内容不准确 3垃圾信息 4其他
     */
    @Override
    public String getType() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_accuse_note</code>. 举报说明
     */
    @Override
    public void setAccuseNote(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_accuse_note</code>. 举报说明
     */
    @Override
    public String getAccuseNote() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_create_member_id</code>. 创建人id
     */
    @Override
    public void setCreateMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_create_member_id</code>. 创建人id
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_audit_status</code>. 审核状态
     */
    @Override
    public void setAuditStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_audit_status</code>. 审核状态
     */
    @Override
    public Integer getAuditStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_audit_record_id</code>. 审核记录id
     */
    @Override
    public void setAuditRecordId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_audit_record_id</code>. 审核记录id
     */
    @Override
    public String getAuditRecordId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_object_type</code>. 举报对象类型 ：1讨论区评论 2讨论区回复
     */
    @Override
    public void setObjectType(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_object_type</code>. 举报对象类型 ：1讨论区评论 2讨论区回复
     */
    @Override
    public Integer getObjectType() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>system.t_comment_accuse.f_object_id</code>. 举报对象id
     */
    @Override
    public void setObjectId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>system.t_comment_accuse.f_object_id</code>. 举报对象id
     */
    @Override
    public String getObjectId() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, String, Long, Integer, String, Integer, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, String, Long, Integer, String, Integer, String> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CommentAccuse.COMMENT_ACCUSE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CommentAccuse.COMMENT_ACCUSE.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CommentAccuse.COMMENT_ACCUSE.ACCUSE_NOTE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CommentAccuse.COMMENT_ACCUSE.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return CommentAccuse.COMMENT_ACCUSE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CommentAccuse.COMMENT_ACCUSE.AUDIT_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return CommentAccuse.COMMENT_ACCUSE.AUDIT_RECORD_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CommentAccuse.COMMENT_ACCUSE.OBJECT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return CommentAccuse.COMMENT_ACCUSE.OBJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAccuseNote();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getAuditStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getAuditRecordId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getObjectType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getObjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value2(String value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value3(String value) {
        setAccuseNote(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value4(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value6(Integer value) {
        setAuditStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value7(String value) {
        setAuditRecordId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value8(Integer value) {
        setObjectType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord value9(String value) {
        setObjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentAccuseRecord values(String value1, String value2, String value3, String value4, Long value5, Integer value6, String value7, Integer value8, String value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICommentAccuse from) {
        setId(from.getId());
        setType(from.getType());
        setAccuseNote(from.getAccuseNote());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setAuditStatus(from.getAuditStatus());
        setAuditRecordId(from.getAuditRecordId());
        setObjectType(from.getObjectType());
        setObjectId(from.getObjectId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICommentAccuse> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CommentAccuseRecord
     */
    public CommentAccuseRecord() {
        super(CommentAccuse.COMMENT_ACCUSE);
    }

    /**
     * Create a detached, initialised CommentAccuseRecord
     */
    public CommentAccuseRecord(String id, String type, String accuseNote, String createMemberId, Long createTime, Integer auditStatus, String auditRecordId, Integer objectType, String objectId) {
        super(CommentAccuse.COMMENT_ACCUSE);

        set(0, id);
        set(1, type);
        set(2, accuseNote);
        set(3, createMemberId);
        set(4, createTime);
        set(5, auditStatus);
        set(6, auditRecordId);
        set(7, objectType);
        set(8, objectId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.CommentAccuseEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.CommentAccuseEntity pojo = (com.zxy.product.system.jooq.tables.pojos.CommentAccuseEntity)source;
        pojo.into(this);
        return true;
    }
}
