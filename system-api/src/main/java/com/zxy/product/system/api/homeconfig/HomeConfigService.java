package com.zxy.product.system.api.homeconfig;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.HomeConfig;
import com.zxy.product.system.entity.HomeModule;
import com.zxy.product.system.entity.HomeModuleConfig;
import com.zxy.product.system.entity.Organization;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService
public interface HomeConfigService {

    @Transactional
    HomeConfig insert(Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini, Optional<String> logoMiniPath,
                      Optional<String> description, String name, String organizationId, String version, List<HomeModuleConfig> homeModuleConfigs);

    @Transactional
    HomeConfig update(String id, Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini,Optional<String> logoMiniPath,
                      Optional<String> description,String name, String organizationId, String version);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<HomeConfig> find(Integer page, Integer pageSize, String currentUserId, Optional<String> organizationId);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    HomeConfig get(String id);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    HomeConfig getDetail(String id);

    @Transactional
    String enable(String id, Integer type);

    @Transactional
    String disable(String id);

    @Transactional
    String delete(String id);

    /**
     * 获取某个组织激活的首页配置
     *
     * @param organizationId
     * @param type
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Optional<HomeConfig> getEnableHomeConfig(String organizationId, Integer type);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<String> findEnableOrganizationIds();

    /***
     * 首页绑定的模块
     * @param id
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeModule> findModulesById(String id);


    /***
     * 修改首页的浏览权限
     * @param id
     * @param enableBrowse
     * @return
     */
    @Transactional
    HomeConfig update(String id, Integer enableBrowse, Optional<Integer> state);

    @Transactional
    void disableByOrganizationId(String organizationId);

    /**
     * 查找组织下是否存在激活的首页
     * @param orgId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    int existsEnableByOrgId(String orgId);

    /**
     * 查找存在激活首页的组织
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<Organization> findOrgByEnableHome(String organizationId);

    /**
     * 查找存在激活首页的组织及所以上级单位
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<Organization> findOrgSubByEnableHome(String organizationId);

    /**
     * 查询哪些组织拥有虚拟空间
     * @param orgList
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<Organization> findOrgVirtual(List<Organization> orgList);

    /**
     * 查询虚拟空间列表
     *
     * @param page            当前页
     * @param pageSize        每页多少条
     * @param currentUserId   当前用户id
     * @param createTimeStart 创建时间-开始
     * @param createTimeEnd   创建时间-结束
     * @param name            空间名称
     * @param ids             组织id
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<HomeConfig> findVirtualSpace(Integer page, Integer pageSize, String currentUserId, Optional<Long> createTimeStart, Optional<Long> createTimeEnd, Optional<String> name, List<String> ids, Optional<String> organizationId);


    /**
     * 添加虚拟空间
     *
     * @param logo
     * @param logoPath
     * @param logoMini
     * @param logoMiniPath
     * @param description
     * @param name
     * @param organizationId
     * @param version
     * @param homeModuleConfigs
     * @param pcCanvasPageConfigIds
     * @param appCanvasPageConfigIds
     * @param homePageJson
     * @param enableHomeBrowse
     * @param platform
     * @return
     */
    @Transactional
    HomeConfig insertVirtualSpace(Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini, Optional<String> logoMiniPath, Optional<String> description, String name, String organizationId, String version, List<HomeModuleConfig> homeModuleConfigs, Optional<String> pcCanvasPageConfigIds, Optional<String> appCanvasPageConfigIds, Optional<String> homePageJson, Integer enableHomeBrowse, Integer platform, Optional<Integer> jumpHomePage);


    void handHomePageRelation(Optional<String> homePageJson, String homeConfigId);

    //@Transactional
    void handHomePageRelation(Optional<String> pcCanvasPageConfigIds, Optional<String> appCanvasPageConfigIds, String homeConfigId);

    /**
     * 根据组织id查询虚拟空间
     *
     * @param organizationId
     * @param id
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    int getVirtualSpaceByOrganizationId(String organizationId, Optional<String> id);

    /**
     * 修改虚拟空间
     *
     * @param name             空间名称
     * @param description      空间描述
     * @param organizationId   组织id
     * @param id               空间id
     * @param logo
     * @param logoMini
     * @param logoPath
     * @param logoMiniPath
     * @param version
     * @param pcJson
     * @param enableHomeBrowse
     * @param platform
     */
    @Transactional
    HomeConfig updateVirtualSpace(Optional<String> name, Optional<String> description, Optional<String> organizationId, String id, Optional<String> logo, Optional<String> logoMini, Optional<String> logoPath, Optional<String> logoMiniPath, Optional<String> version, Optional<String> pcJson, Integer enableHomeBrowse, Integer platform, Optional<Integer> jumpHomePage);


    /**
     * 修改虚拟空间状态
     * @param id
     * @param state
     */
    @Transactional
    String updateVirtualSpaceState(String id, Integer state);


    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<String> hasConfigVirtualIds();

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String, HomeConfig> findByOrganizationId(List<String> list);


    /**
     * 根据组织id查询虚拟空间
     * @param list
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeConfig> findVirtualSpaceByOrganizationId(List<String> list);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeConfig> findVirtualSpaceByOrganizationNotInId(List<String> list);
    @Transactional
    int clearMongoCache(String currentUserId, String authorization, String homeConfigId);

    @Transactional
    void disableByOrganizationId(String organizationId, Integer type);

    /***
     * 修改首页的浏览权限
     * @param id
     * @param enableBrowse
     * @param logoId
     * @param logoPath
     * @param rootOrganizationId
     * @return
     */
    @Transactional
    HomeConfig update(String id, Integer enableBrowse, Optional<Integer> state, Optional<String> logoId, Optional<String> logoPath, String rootOrganizationId);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeConfig> findAll(List<String> list);
    /**
     * 查询所有分院，画布修复数据使用
     * @param page 页数
     * @param pageSize 每页数量
     * @param clientType 适用终端，1 PC 2APP
     * @param paasFlag
     */
    @Transactional(readOnly = true)
    List<HomeConfig> findAll(int page, int pageSize, Integer clientType, Optional<Integer> paasFlag);


    /**
     * 给指定分院增加页面，画布修复数据使用
     * @param map key是分院id，value是页面id
     * @param type 页面类型 1-pc  2-app
     */
    @Transactional
    Boolean addPageRelations(Map<String, String> map, Integer type);

    @Transactional(readOnly = true)
    PagedResult<HomeConfig> findAddList(Integer page, Integer pageSize, Optional<String> optionalName);

    @Transactional(readOnly = true)
    Optional<HomeConfig> getOptional(String id);

}
