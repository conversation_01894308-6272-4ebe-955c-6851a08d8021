/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.IntegralResult;
import com.zxy.product.system.jooq.tables.interfaces.IIntegralResult;

import java.math.BigDecimal;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户积分结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IntegralResultRecord extends UpdatableRecordImpl<IntegralResultRecord> implements Record6<String, String, BigDecimal, String, BigDecimal, BigDecimal>, IIntegralResult {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_integral_result.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_integral_result.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_integral_result.f_total_score</code>. 总分
     */
    @Override
    public void setTotalScore(BigDecimal value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_total_score</code>. 总分
     */
    @Override
    public BigDecimal getTotalScore() {
        return (BigDecimal) get(2);
    }

    /**
     * Setter for <code>system.t_integral_result.f_organization_id</code>. 组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_organization_id</code>. 组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>system.t_integral_result.f_max_score</code>. 最高分
     */
    @Override
    public void setMaxScore(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_max_score</code>. 最高分
     */
    @Override
    public BigDecimal getMaxScore() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>system.t_integral_result.f_history_score</code>. 去年之前积分值
     */
    @Override
    public void setHistoryScore(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>system.t_integral_result.f_history_score</code>. 去年之前积分值
     */
    @Override
    public BigDecimal getHistoryScore() {
        return (BigDecimal) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, BigDecimal, String, BigDecimal, BigDecimal> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, BigDecimal, String, BigDecimal, BigDecimal> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return IntegralResult.INTEGRAL_RESULT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return IntegralResult.INTEGRAL_RESULT.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<BigDecimal> field3() {
        return IntegralResult.INTEGRAL_RESULT.TOTAL_SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return IntegralResult.INTEGRAL_RESULT.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<BigDecimal> field5() {
        return IntegralResult.INTEGRAL_RESULT.MAX_SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<BigDecimal> field6() {
        return IntegralResult.INTEGRAL_RESULT.HISTORY_SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal value3() {
        return getTotalScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal value5() {
        return getMaxScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal value6() {
        return getHistoryScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value3(BigDecimal value) {
        setTotalScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value4(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value5(BigDecimal value) {
        setMaxScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord value6(BigDecimal value) {
        setHistoryScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntegralResultRecord values(String value1, String value2, BigDecimal value3, String value4, BigDecimal value5, BigDecimal value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IIntegralResult from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setTotalScore(from.getTotalScore());
        setOrganizationId(from.getOrganizationId());
        setMaxScore(from.getMaxScore());
        setHistoryScore(from.getHistoryScore());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IIntegralResult> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IntegralResultRecord
     */
    public IntegralResultRecord() {
        super(IntegralResult.INTEGRAL_RESULT);
    }

    /**
     * Create a detached, initialised IntegralResultRecord
     */
    public IntegralResultRecord(String id, String memberId, BigDecimal totalScore, String organizationId, BigDecimal maxScore, BigDecimal historyScore) {
        super(IntegralResult.INTEGRAL_RESULT);

        set(0, id);
        set(1, memberId);
        set(2, totalScore);
        set(3, organizationId);
        set(4, maxScore);
        set(5, historyScore);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.IntegralResultEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.IntegralResultEntity pojo = (com.zxy.product.system.jooq.tables.pojos.IntegralResultEntity)source;
        pojo.into(this);
        return true;
    }
}
