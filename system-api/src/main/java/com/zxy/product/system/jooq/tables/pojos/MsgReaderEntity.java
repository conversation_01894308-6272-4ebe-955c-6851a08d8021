/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IMsgReader;

import javax.annotation.Generated;


/**
 * 消息阅读表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MsgReaderEntity extends BaseEntity implements IMsgReader {

    private static final long serialVersionUID = 1L;

    private String msgId;
    private String memberId;

    public MsgReaderEntity() {}

    public MsgReaderEntity(MsgReaderEntity value) {
        this.msgId = value.msgId;
        this.memberId = value.memberId;
    }

    public MsgReaderEntity(
        String id,
        String msgId,
        String memberId,
        Long   createTime
    ) {
        super.setId(id);
        this.msgId = msgId;
        this.memberId = memberId;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMsgId() {
        return this.msgId;
    }

    @Override
    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MsgReaderEntity (");

        sb.append(getId());
        sb.append(", ").append(msgId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMsgReader from) {
        setId(from.getId());
        setMsgId(from.getMsgId());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMsgReader> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends MsgReaderEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.MsgReaderRecord r = new com.zxy.product.system.jooq.tables.records.MsgReaderRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.ID, record.getValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MSG_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MSG_ID, record.getValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MSG_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MEMBER_ID, record.getValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.MsgReader.MSG_READER.CREATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
