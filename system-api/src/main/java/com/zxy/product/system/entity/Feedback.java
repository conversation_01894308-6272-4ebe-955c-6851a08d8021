package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.FeedbackEntity;

import java.util.List;

/**
 * Created by myh on 2017-06-13
 */
public class Feedback extends FeedbackEntity {
	private static final long serialVersionUID = 1385228185436622049L;

	public static final String URI = "system/feedback";
	public static final Integer FEEDBACK_TYPE_FUNCTIONALITY = 1;       //功能性建议：1 内容性建议:2 网络环境问题：3：其他问题：99
	public static final Integer FEEDBACK_TYPE_CONTENT = 2;             //内容性建议
	public static final Integer FEEDBACK_TYPE_NETWORK_ENVIRONMENT  = 3;//网络环境问题
	public static final Integer FEEDBACK_TYPE_OTHER = 99;              //其他问题


	private Member member;
	private List<FeedbackAttachment> feedbackAttachmentEntityList;
	private String replyMemberName;

	public String getReplyMemberName() {
		return replyMemberName;
	}

	public void setReplyMemberName(String replyMemberName) {
		this.replyMemberName = replyMemberName;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public List<FeedbackAttachment> getFeedbackAttachmentEntityList() {
		return feedbackAttachmentEntityList;
	}

	public void setFeedbackAttachmentEntityList(List<FeedbackAttachment> feedbackAttachmentEntityList) {
		this.feedbackAttachmentEntityList = feedbackAttachmentEntityList;
	}
}
