package com.zxy.product.system.api.operation;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.Announcement;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 公告管理
 */
@RemoteService
public interface AnnouncementService {
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Announcement get(String id);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Announcement> find(int page, int pageSize, String memberId, List<String> grantOrganizationIds, Optional<String> titleContent,
                                   Optional<String> queryPublishMemberId, Optional<Integer> status, Optional<Long> startTime,
                                   Optional<Long> endTime);

    @Transactional
    Announcement insert(String organizationId,String title, String content, String textContent, Long startTime, Long endTime, Integer status, String memberId);

    @Transactional
    Announcement update(String id, Optional<String> organizationId, Optional<String> title, Optional<String> content, Optional<String> textContent, Optional<Long> startTime, Optional<Long> endTime, Optional<Integer> status, String memberId);

    @Transactional
    int delete(String id);

    @Transactional
    Announcement updateStatus(String id, Integer status);

    /**
     * 批量更新状态为已读
     */
    @Transactional
    int insertReadStatus(String currentUserId);

    /**
     * 个人中心公告
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Announcement> personList(Integer page, Integer pageSize, String currentUserId, String organizationId);

    /**
     * 个人中心公告查看更多
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Announcement> personListAll(String currentUserId, String organizationId);
}
