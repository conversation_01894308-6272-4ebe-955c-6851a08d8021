package com.zxy.product.system.api.backgroundoperationmanage;


import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.BackgroundOperationManage;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import java.util.Optional;

/**
 * 后台运营管理
 */
@RemoteService
public interface BackgroundOperationManageService {
    /**
     * 根据类型组装九天返回的所需数据
     * @param type 0：热词，1：热议 2：推荐
     * @param host 页面请求的域名
     * @param token
     * @param params
     * @param sqlParams
     * @return
     */
    PagedResult<BackgroundOperationManage> findDataListByType(String type, String host, String token, Map<String, String> params, Map<String, String> sqlParams);

    /**
     * 根据类型查找数据，包含黑名单数据
     * @param type
     * @return
     */
    List<BackgroundOperationManage> findArtificialDataListByType(Integer... type);


    /**
     * 添加内容
     * @param name          热词名称
     * @param businessId    业务id
     * @param businessType  业务类型 0=热词,1=问题,2=文章,3=课程,4=专题,5=直播
     * @param type          类型  0=热门搜索,1=热门内容,2=推荐内容
     * @param isBlacklist   是否是黑名单,0=不是,1=是
     * @param currentUserId
     * @return
     */
    @Transactional
    boolean addContent(Optional<String> name, Integer businessType, Integer type, Integer isBlacklist, String currentUserId, Integer count, Optional<String> businessId);

    /**
     * 查询人工数据有几个
     * @param type
     * @return
     */
    @Transactional(readOnly = true)
    Integer findNumber(Integer type);


    /**
     * 根据name查询热词
     *
     * @param name
     * @param isBlacklist
     * @param integer
     * @return
     */
    @Transactional(readOnly = true)
    Optional<Object> findHotWord(String name, Integer isBlacklist, Integer integer);


    /**
     * 根据业务id查询内容是否存在
     *
     * @param businessId
     * @param isBlacklist
     * @return
     */
    @Transactional(readOnly = true)
    Optional<Object> findContent(String businessId, Integer isBlacklist, Integer businessType);

    /**
     * 删除非黑名单内容
     * @param id  id
     * @return
     */
    @Transactional
    boolean deleteContent(String id);

    /**
     * 移除黑名单
     * @param s
     * @return
     */
    @Transactional
    boolean removeBlacklist(String s);

    /**
     * 上下移动内容
     * @param id 当前id
     * @param targetId  目标id
     * @return
     */
    @Transactional
    boolean moveContent(String id, String targetId);


    /**
     * 查询热词黑名单
     * @return
     */
    List<String> findBlacklistHotWord();

    /**
     *
     * @param name 热词名称
     * @param businessId  业务id
     * @param heat   热度
     * @param type   类型 0=热门搜索,1=热门内容,2=推荐内容
     * @param businessType 数据类型:0=热词,1=问题,2=文章,3=课程,4=专题,5=直播
     * @return
     */
    @Transactional
    boolean setBlacklist(Optional<String> name, Optional<String> businessId, Optional<Integer> heat, Optional<Integer> type, Optional<Integer> businessType);


    @Transactional(readOnly = true)
    List<BackgroundOperationManage> findHotWord(Integer businessType, Integer isBlacklist, Integer source);
}
