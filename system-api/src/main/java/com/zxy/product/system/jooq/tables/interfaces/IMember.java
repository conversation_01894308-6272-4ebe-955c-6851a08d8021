/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMember extends Serializable {

    /**
     * Setter for <code>system.t_member.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_member.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>system.t_member.f_name</code>. 用户名
     */
    public void setName(String value);

    /**
     * Getter for <code>system.t_member.f_name</code>. 用户名
     */
    public String getName();

    /**
     * Setter for <code>system.t_member.f_full_name</code>. 姓名
     */
    public void setFullName(String value);

    /**
     * Getter for <code>system.t_member.f_full_name</code>. 姓名
     */
    public String getFullName();

    /**
     * Setter for <code>system.t_member.f_ihr_code</code>. ihr新员工编码
     */
    public void setIhrCode(String value);

    /**
     * Getter for <code>system.t_member.f_ihr_code</code>. ihr新员工编码
     */
    public String getIhrCode();

    /**
     * Setter for <code>system.t_member.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>system.t_member.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>system.t_member.f_organization_name</code>. 组织名称
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>system.t_member.f_organization_name</code>. 组织名称
     */
    public String getOrganizationName();

    /**
     * Setter for <code>system.t_member.f_position_id</code>. 岗位id
     */
    public void setPositionId(String value);

    /**
     * Getter for <code>system.t_member.f_position_id</code>. 岗位id
     */
    public String getPositionId();

    /**
     * Setter for <code>system.t_member.f_job_id</code>. ihr新职务id
     */
    public void setJobId(String value);

    /**
     * Getter for <code>system.t_member.f_job_id</code>. ihr新职务id
     */
    public String getJobId();

    /**
     * Setter for <code>system.t_member.f_position_name</code>. 岗位名称
     */
    public void setPositionName(String value);

    /**
     * Getter for <code>system.t_member.f_position_name</code>. 岗位名称
     */
    public String getPositionName();

    /**
     * Setter for <code>system.t_member.f_head_portrait</code>. 头像id
     */
    public void setHeadPortrait(String value);

    /**
     * Getter for <code>system.t_member.f_head_portrait</code>. 头像id
     */
    public String getHeadPortrait();

    /**
     * Setter for <code>system.t_member.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_member.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    public void setInit(Integer value);

    /**
     * Getter for <code>system.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    public Integer getInit();

    /**
     * Setter for <code>system.t_member.f_email</code>.
     */
    public void setEmail(String value);

    /**
     * Getter for <code>system.t_member.f_email</code>.
     */
    public String getEmail();

    /**
     * Setter for <code>system.t_member.f_phone_number</code>. 手机号
     */
    public void setPhoneNumber(String value);

    /**
     * Getter for <code>system.t_member.f_phone_number</code>. 手机号
     */
    public String getPhoneNumber();

    /**
     * Setter for <code>system.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    public void setCompanyId(String value);

    /**
     * Getter for <code>system.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    public String getCompanyId();

    /**
     * Setter for <code>system.t_member.f_head_portrait_path</code>. 头像路径
     */
    public void setHeadPortraitPath(String value);

    /**
     * Getter for <code>system.t_member.f_head_portrait_path</code>. 头像路径
     */
    public String getHeadPortraitPath();

    /**
     * Setter for <code>system.t_member.f_status</code>. 人员状态 0=禁用,1=启用
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>system.t_member.f_status</code>. 人员状态 0=禁用,1=启用
     */
    public Integer getStatus();

    /**
     * Setter for <code>system.t_member.f_account_type</code>. 账号类型 0表示系统注册 1表示 新增注册 2表示自主注册
     */
    public void setAccountType(Integer value);

    /**
     * Getter for <code>system.t_member.f_account_type</code>. 账号类型 0表示系统注册 1表示 新增注册 2表示自主注册
     */
    public Integer getAccountType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMember
     */
    public void from(com.zxy.product.system.jooq.tables.interfaces.IMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMember
     */
    public <E extends com.zxy.product.system.jooq.tables.interfaces.IMember> E into(E into);
}
