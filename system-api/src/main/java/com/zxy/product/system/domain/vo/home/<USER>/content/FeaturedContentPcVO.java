package com.zxy.product.system.domain.vo.home.featured.content;

import java.io.Serializable;

/**
 * 首页精选内容：PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:45
 */
public class FeaturedContentPcVO extends FeaturedContentParentVO implements Serializable {
    private static final long serialVersionUID = -5486063649271501220L;

    /**精选内容：精选理由*/
    private String dataExt;

    /**精选内容：数据Id*/
    private String dataId;

    /**精选内容：数据名称*/
    private String dataName;

    /**精选内容：图片地址*/
    private String coverPath;

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getCoverPath() { return coverPath; }

    public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

    @Override
    public String toString() {
        return "FeaturedContentPcVO{" +
                "dataExt='" + dataExt + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataName='" + dataName + '\'' +
                ", coverPath='" + coverPath + '\'' +
                '}';
    }
}
