/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.PointRuleRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 积分规则配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PointRule extends TableImpl<PointRuleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_point_rule</code>
     */
    public static final PointRule POINT_RULE = new PointRule();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PointRuleRecord> getRecordType() {
        return PointRuleRecord.class;
    }

    /**
     * The column <code>system.t_point_rule.f_id</code>. 主键
     */
    public final TableField<PointRuleRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>system.t_point_rule.f_rule_key</code>. 业务类型键值
     */
    public final TableField<PointRuleRecord, String> RULE_KEY = createField("f_rule_key", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务类型键值");

    /**
     * The column <code>system.t_point_rule.f_aspect_type</code>. 积分维度类型
     */
    public final TableField<PointRuleRecord, String> ASPECT_TYPE = createField("f_aspect_type", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "积分维度类型");

    /**
     * The column <code>system.t_point_rule.f_rule_source</code>. 积分来源(用于积分明细字段的展示)
     */
    public final TableField<PointRuleRecord, String> RULE_SOURCE = createField("f_rule_source", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "积分来源(用于积分明细字段的展示)");

    /**
     * The column <code>system.t_point_rule.f_rule_count</code>. 规则次数/分钟
     */
    public final TableField<PointRuleRecord, Integer> RULE_COUNT = createField("f_rule_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "规则次数/分钟");

    /**
     * The column <code>system.t_point_rule.f_rule_max_count</code>. 最大次数(知识下载/短视频点赞为最高分)
     */
    public final TableField<PointRuleRecord, Integer> RULE_MAX_COUNT = createField("f_rule_max_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "最大次数(知识下载/短视频点赞为最高分)");

    /**
     * The column <code>system.t_point_rule.f_rule_point</code>. 积分
     */
    public final TableField<PointRuleRecord, Integer> RULE_POINT = createField("f_rule_point", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "积分");

    /**
     * The column <code>system.t_point_rule.f_rule_desc</code>. 积分规则描述
     */
    public final TableField<PointRuleRecord, String> RULE_DESC = createField("f_rule_desc", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "积分规则描述");

    /**
     * The column <code>system.t_point_rule.f_rule_type</code>. 参数个数 (1-1个参数,2-2个参数,3-三个参数)
     */
    public final TableField<PointRuleRecord, Integer> RULE_TYPE = createField("f_rule_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "参数个数 (1-1个参数,2-2个参数,3-三个参数)");

    /**
     * The column <code>system.t_point_rule.f_seq</code>. 管理端显示顺序
     */
    public final TableField<PointRuleRecord, Integer> SEQ = createField("f_seq", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "管理端显示顺序");

    /**
     * The column <code>system.t_point_rule.f_daily_quest_seq</code>. 每日任务排序
     */
    public final TableField<PointRuleRecord, Integer> DAILY_QUEST_SEQ = createField("f_daily_quest_seq", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "每日任务排序");

    /**
     * The column <code>system.t_point_rule.f_organization_id</code>. 组织ID
     */
    public final TableField<PointRuleRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织ID");

    /**
     * The column <code>system.t_point_rule.f_configurable</code>. 是否在管理后台可配置(1-可配置,2-不可配置)
     */
    public final TableField<PointRuleRecord, Integer> CONFIGURABLE = createField("f_configurable", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否在管理后台可配置(1-可配置,2-不可配置)");

    /**
     * The column <code>system.t_point_rule.f_create_time</code>. 创建时间
     */
    public final TableField<PointRuleRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>system.t_point_rule.f_modify_date</code>. 修改时间
     */
    public final TableField<PointRuleRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>system.t_point_rule</code> table reference
     */
    public PointRule() {
        this("t_point_rule", null);
    }

    /**
     * Create an aliased <code>system.t_point_rule</code> table reference
     */
    public PointRule(String alias) {
        this(alias, POINT_RULE);
    }

    private PointRule(String alias, Table<PointRuleRecord> aliased) {
        this(alias, aliased, null);
    }

    private PointRule(String alias, Table<PointRuleRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "积分规则配置表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PointRuleRecord> getPrimaryKey() {
        return Keys.KEY_T_POINT_RULE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PointRuleRecord>> getKeys() {
        return Arrays.<UniqueKey<PointRuleRecord>>asList(Keys.KEY_T_POINT_RULE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PointRule as(String alias) {
        return new PointRule(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PointRule rename(String name) {
        return new PointRule(name, null);
    }
}
