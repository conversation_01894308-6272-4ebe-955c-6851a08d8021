package com.zxy.product.system.async.listener;

import static com.zxy.product.system.jooq.Tables.DASHBOARD_CONFIG;

import com.google.common.collect.ImmutableList;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.system.api.operation.DashboardConfigService;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.entity.DashboardConfig;
import com.zxy.product.system.entity.Organization;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 3级组织新增时也新增驾驶舱配置
 */
@Component
public class DashboardConfigListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(DashboardConfigListener.class);

    private CommonDao<DashboardConfig> dashboardConfigDao;
    private DashboardConfigService dashboardConfigService;
    private OrganizationService organizationService;
    private Cache cache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("system", "dashboard-config");
    }

    @Autowired
    public void setDashboardConfigService(DashboardConfigService dashboardConfigService) {
        this.dashboardConfigService = dashboardConfigService;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setDashboardConfigDao(CommonDao<DashboardConfig> dashboardConfigDao) {
        this.dashboardConfigDao = dashboardConfigDao;
    }

    @Override
    public void onMessage(Message message) {
        LOGGER.info("system/DashboardConfigListener, {}", message.toString());

        String id = message.getHeader(MessageHeaderContent.ID);
        String ids = message.getHeader(MessageHeaderContent.IDS);
        int type = message.getType();

        if (type == MessageTypeContent.SYSTEM_ORGANIZATION_INSERT) {
            handleOrgInsert(id);
        }
        if (type == MessageTypeContent.SYSTEM_ORGANIZATION_BATCH_INSERT) {
            handleOrgBatchInsert(ids);
        }
    }

    private void handleOrgBatchInsert(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }
        organizationService.getOrganizationByIds(Arrays.asList(ids.split(SystemConstant.COMMA)))
                           .stream().map(Organization::getId).forEach(this::handleOrgInsert);
        cache.clear(DashboardConfig.CACHE_KEY);
    }

    private void handleOrgInsert(String id) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        Optional<Organization> organization = organizationService.get(id);
        ImmutableList<String> excludes = ImmutableList.of("内部组织", "网格单位", "中移网大");
        String existedId = dashboardConfigDao.execute(d -> d.select(DASHBOARD_CONFIG.ID)
                                                            .from(DASHBOARD_CONFIG).where(DASHBOARD_CONFIG.ORGANIZATION_ID.eq(id))
                                                            .fetchOne(DASHBOARD_CONFIG.ID));
        if (existedId != null) {
            return;
        }
        Integer maxOrder = dashboardConfigDao.execute(d -> d.select(DSL.max(DASHBOARD_CONFIG.ORDER))
                                                           .from(DASHBOARD_CONFIG)
                                                           .fetchOne(DSL.max(DASHBOARD_CONFIG.ORDER)));
        DashboardConfig dashboardConfig = new DashboardConfig();
        dashboardConfig.forInsert();
        dashboardConfig.setOrganizationId(id);
        dashboardConfig.setStatus(DashboardConfig.STATUS_ENABLE);
        dashboardConfig.setOrder(maxOrder);
        dashboardConfigDao.insert(dashboardConfig);
        dashboardConfigService.updateOrder(dashboardConfig.getId(), organization.get().getOrder());

        cache.clear(DashboardConfig.CACHE_KEY);

    }

    @Override
    public int[] getTypes() {
        return new int[]{
            MessageTypeContent.SYSTEM_ORGANIZATION_BATCH_INSERT,
            MessageTypeContent.SYSTEM_ORGANIZATION_INSERT,
            MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE,
        };
    }

}
