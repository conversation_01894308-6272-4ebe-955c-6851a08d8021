package com.zxy.product.system.async.listener;

import static com.zxy.product.system.jooq.Tables.DATA_PERMISSION;
import static com.zxy.product.system.jooq.Tables.HOME_CONFIG;
import static com.zxy.product.system.jooq.Tables.HOME_FOOTER;
import static com.zxy.product.system.jooq.Tables.HOME_MODULE;
import static com.zxy.product.system.jooq.Tables.HOME_NAV;
import static com.zxy.product.system.jooq.Tables.INTEGRAL_RULE;
import static com.zxy.product.system.jooq.Tables.MENU;
import static com.zxy.product.system.jooq.Tables.MESSAGE_TEMPLATE;
import static com.zxy.product.system.jooq.Tables.MODULE_GROUP;
import static com.zxy.product.system.jooq.Tables.ROLE;
import static com.zxy.product.system.jooq.Tables.RULE_CONFIG;
import static com.zxy.product.system.jooq.Tables.SETTING;
import static com.zxy.product.system.jooq.Tables.SHARE;
import static com.zxy.product.system.jooq.Tables.SPEECH_SET;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Lists;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.entity.DataPermission;
import com.zxy.product.system.entity.Grant;
import com.zxy.product.system.entity.HomeConfig;
import com.zxy.product.system.entity.HomeFooter;
import com.zxy.product.system.entity.HomeModule;
import com.zxy.product.system.entity.HomeNav;
import com.zxy.product.system.entity.IntegralRule;
import com.zxy.product.system.entity.Menu;
import com.zxy.product.system.entity.MessageTemplate;
import com.zxy.product.system.entity.ModuleGroup;
import com.zxy.product.system.entity.Role;
import com.zxy.product.system.entity.RoleMenu;
import com.zxy.product.system.entity.RuleConfig;
import com.zxy.product.system.entity.Setting;
import com.zxy.product.system.entity.Share;
import com.zxy.product.system.entity.SpeechSet;

@Component
public class CompanySyncListener extends AbstractMessageListener {

	private static final Logger LOGGER = LoggerFactory.getLogger(CompanySyncListener.class);
	private static final String DEFAULT_ID = "init";
	private static final String OPERATOR_TYPES = "0,1,2,3,4";
	private static final String SUPER_ADMIN_ROLE_NAME = "超级管理员";

	private MessageSender sender;
	private CommonDao<Role> roleDao;
	private CommonDao<Menu> menuDao;
	private CommonDao<Share> shareDao;
	private CommonDao<Grant> grantDao;
	private CommonDao<Setting> settingDao;
	private CommonDao<RoleMenu> roleMenuDao;
	private CommonDao<HomeConfig> homeConfigDao;
	private CommonDao<HomeModule> homeModuleDao;
	private CommonDao<HomeNav> homeNavDao;
	private CommonDao<HomeFooter> homeFooterDao;
	private CommonDao<ModuleGroup> moduleGroupDao;
	private CommonDao<RuleConfig> ruleConfigDao;
	private CommonDao<SpeechSet> speechSetDao;
	private CommonDao<IntegralRule> integralRuleDao;
	private CommonDao<DataPermission> dataPermissionDao;
	private CommonDao<MessageTemplate> messageTemplateDao;
	private TransactionTemplate transactionTemplate;

	@Autowired
	public void setMenuDao(CommonDao<Menu> menuDao) {
		this.menuDao = menuDao;
	}
	@Autowired
	public void setGrantDao(CommonDao<Grant> grantDao) {
		this.grantDao = grantDao;
	}
	@Autowired
	public void setRoleMenuDao(CommonDao<RoleMenu> roleMenuDao) {
		this.roleMenuDao = roleMenuDao;
	}
	@Autowired
	public void setRoleDao(CommonDao<Role> roleDao) {
		this.roleDao = roleDao;
	}
	@Autowired
	public void setSender(MessageSender sender) {
		this.sender = sender;
	}
	@Autowired
	public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}

	@Override
	protected void onMessage(Message message) {
		LOGGER.info("system/CloudSyncListener, {}", message.toString());

		String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
		String orgId = message.getHeader(MessageHeaderContent.ID);
		String companyName = message.getHeader(MessageHeaderContent.ID);

		int status = SystemConstant.SYNC_SUCCESS;
		try {
			// 授权
			initGrant(orgId, memberId);

			// 首页可配置
			initHomeConfig(orgId, companyName);

			// 系统管理
			initRuleConfig(orgId, companyName);
			initDataPermissionConfig(orgId);

			// 运营管理
			initIntegralRule(orgId);
			initShare(orgId, memberId);
			initSpeechSet(orgId);
			initMessageTemplate(orgId, memberId);

			// 国际化
			initSetting(orgId);

			LOGGER.info("system同步成功,公司id:{}", orgId);
		} catch(Exception e) {
			status = SystemConstant.SYNC_FAILED;
			LOGGER.error("system同步失败,公司id:" + orgId, e);
		} finally{
			// 发送消息给human,修改企业同步状态(module_sync_sign),以及通知云中心
			sender.send(MessageTypeContent.HR_CLOUD_CENTER_SYNC_STATUS,
					MessageHeaderContent.ID, orgId,
					MessageHeaderContent.SYNC_MODULE, SystemConstant.SYNC_MODULE_SYSTEM + "",
					MessageHeaderContent.SYNC_STATUS, status + "");
		}

	}

	/** 言论控制 */
	private void initSpeechSet(String orgId) {
		int count = speechSetDao.count(SPEECH_SET.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			List<SpeechSet> speechSets = speechSetDao.fetch(SPEECH_SET.ORGANIZATION_ID.eq(DEFAULT_ID));
			speechSets.forEach(s -> {
				s.forInsert();
				s.setUpdateTime(s.getCreateTime());
				s.setOrganizationId(orgId);
			});
			speechSetDao.insert(speechSets);
		}
	}
	/** 分享模板 */
	private void initShare(String orgId, String memberId) {
		int count = shareDao.count(SHARE.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			List<Share> shares = shareDao.fetch(SHARE.ORGANIZATION_ID.eq(DEFAULT_ID));
			shares.forEach(s -> {
				s.forInsert();
				s.setOrganizationId(orgId);
				s.setCreateMemberId(memberId);
			});
			shareDao.insert(shares);
		}
	}
	/** 消息模板 */
	private void initMessageTemplate(String orgId, String memberId) {
		int count = messageTemplateDao.count(MESSAGE_TEMPLATE.ORGANIZATION_ID.eq(orgId));
		int defaultCount = messageTemplateDao.count(MESSAGE_TEMPLATE.ORGANIZATION_ID.eq(DEFAULT_ID));

		if (count < defaultCount) {
			messageTemplateDao.delete(MESSAGE_TEMPLATE.ORGANIZATION_ID.eq(orgId));

			List<MessageTemplate> templates = messageTemplateDao.fetch(MESSAGE_TEMPLATE.ORGANIZATION_ID.eq(DEFAULT_ID));

			List<MessageTemplate> segment = new ArrayList<MessageTemplate>(20);
			IntStream.range(0, templates.size()).forEach(i -> {
				MessageTemplate t = templates.get(i);
				t.forInsert();
				t.setOrganizationId(orgId);
				t.setCreateMemberId(memberId);
				segment.add(t);

				if ((i + 1) % 20 == 0 || i == templates.size()) {
					messageTemplateDao.insert(segment);
					segment.clear();
				}
			});
		}
	}
	/** 积分规则 */
	private void initIntegralRule(String orgId) {
		int count = integralRuleDao.count(INTEGRAL_RULE.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			List<IntegralRule> integralRules = integralRuleDao.fetch(INTEGRAL_RULE.ORGANIZATION_ID.eq(DEFAULT_ID));
			integralRules.forEach(ir -> {
				ir.forInsert();
				ir.setOrganizationId(orgId);
			});
			integralRuleDao.insert(integralRules);
		}
	}
	/** 国际化 */
	private void initSetting(String orgId) {
		int count = settingDao.count(SETTING.ORGANIZATION_ID.eq(orgId));
		int defaultCount = settingDao.count(SETTING.ORGANIZATION_ID.eq(DEFAULT_ID));

		if (count < defaultCount) {
			settingDao.delete(SETTING.ORGANIZATION_ID.eq(orgId));

			List<Setting> settings = settingDao.fetch(SETTING.ORGANIZATION_ID.eq(DEFAULT_ID));

			List<Setting> segment = new ArrayList<Setting>(20);
			IntStream.range(0, settings.size()).forEach(i -> {
				Setting s = settings.get(i);
				s.forInsert();
				s.setOrganizationId(orgId);
				segment.add(s);

				if ((i + 1) % 20 == 0 || i == settings.size()) {
					settingDao.insert(segment);
					segment.clear();
				}
			});
		}
	}
	/** 数据权限 */
	private void initDataPermissionConfig(String orgId) {
		int count = dataPermissionDao.count(DATA_PERMISSION.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			List<DataPermission> dataPermissions = dataPermissionDao.fetch(DATA_PERMISSION.ORGANIZATION_ID.eq(DEFAULT_ID));
			dataPermissions.forEach(dp -> {
				dp.forInsert();
				dp.setOrganizationId(orgId);
			});
			dataPermissionDao.insert(dataPermissions);
		}
	}
	/** 规则配置 */
	private void initRuleConfig(String orgId, String companyName) {
		int count = dataPermissionDao.count(RULE_CONFIG.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			List<RuleConfig> ruleConfigs = ruleConfigDao.fetch(RULE_CONFIG.ORGANIZATION_ID.eq(DEFAULT_ID));
			ruleConfigs.forEach(rc -> {
				rc.forInsert();
				rc.setOrganizationId(orgId);
			});
			ruleConfigDao.insert(ruleConfigs);
		}

	}
	/** 首页配置 */
	private void initHomeConfig(String orgId, String companyName) {
		int count = homeConfigDao.count(HOME_CONFIG.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			Optional<HomeConfig> homeConfig = homeConfigDao.fetchOne(HOME_CONFIG.ORGANIZATION_ID.eq(DEFAULT_ID));
			homeConfig.ifPresent(hc -> {
				hc.forInsert();
				hc.setName(companyName);
				hc.setDescription(companyName);
				hc.setOrganizationId(orgId);

				List<ModuleGroup> moduleGroups = moduleGroupDao.fetch(MODULE_GROUP.ORGANIZATION_ID.eq(DEFAULT_ID));
				moduleGroups.forEach(mg -> {
					mg.forInsert();
					mg.setOrganizationId(orgId);
				});

				List<HomeModule> homeModules = homeModuleDao.fetch(HOME_MODULE.ORGANIZATION_ID.eq(DEFAULT_ID));
				homeModules.forEach(hm -> {
					hm.forInsert();
					hm.setOrganizationId(orgId);
				});

				List<HomeNav> homeNavs = homeNavDao.fetch(HOME_NAV.CONFIG_ID.eq(DEFAULT_ID));
				homeNavs.forEach(hn -> {
					hn.forInsert();
					hn.setConfigId(hc.getId());
				});

				Optional<HomeFooter> homeFooter = homeFooterDao.fetchOne(HOME_FOOTER.CONFIG_ID.eq(DEFAULT_ID));
				homeFooter.ifPresent(hf -> {
					hf.forInsert();
					hf.setConfigId(hc.getId());
				});

				transactionTemplate.execute(new TransactionCallbackWithoutResult() {
					@Override
					protected void doInTransactionWithoutResult(TransactionStatus status) {
						homeConfigDao.insert(hc);
						moduleGroupDao.insert(moduleGroups);
						homeModuleDao.insert(homeModules);
						homeNavDao.insert(homeNavs);
						homeFooter.ifPresent(hf -> {
							homeFooterDao.insert(hf);
						});
					}
				});
			});

		}

	}
	/** 超级管理员授权 */
	private void initGrant(String orgId, String memberId) {
		int count = roleDao.count(ROLE.ORGANIZATION_ID.eq(orgId));

		if (count == 0) {
			transactionTemplate.execute(new TransactionCallbackWithoutResult() {
				@Override
				protected void doInTransactionWithoutResult(TransactionStatus status) {
					// 新增公司菜单
					createCompanyMenu(orgId);
					// 新增角色-超级管理员
					Role r = createCompanySuperAdminRole(SUPER_ADMIN_ROLE_NAME, orgId);
					// 新增授权-为超级管理员授权
					createCompanySuperAdminGrant(r.getId(), memberId, orgId);
				}
			});
		}
	}

	private void createCompanyMenu(String orgId) {
		List<Menu> menus = menuDao.fetch(MENU.ORGANIZATION_ID.eq(DEFAULT_ID));

		assembleCompanyMenu(menus, null, orgId);

		menuDao.insert(menus);
	}


	private List<Menu> assembleCompanyMenu(List<Menu> menus, String parentId, String orgId) {
		List<Menu> menuList = Lists.newArrayList();
		for (Menu m : menus) {
			if (parentId == null || parentId.equals(m.getParentId())) {
				menuList.add(m);

				m.forInsert();
				m.setOrganizationId(orgId);
				m.setParentId(parentId);
				m.setPath(getParentPath(menus, parentId) + m.getId() + ",");
				m.setChildren(assembleCompanyMenu(menus, m.getId(), orgId));
			}
		}
		return menuList;
	}

	private String getParentPath(List<Menu> menus, String parentId) {
		String path = "";
		for (Menu m : menus) {
			if (parentId == null) {
				break;
			} else if (parentId.equals(m.getId())){
				path = m.getPath();
				break;
			}
		}
		return path;
	}

	/** 创建超级管理员角色 */
	private Role createCompanySuperAdminRole(String name, String orgId) {
		Role r = new Role();
		r.forInsert();
		r.setName(name);
		r.setOrganizationId(orgId);
        r.setParentId(null);
        r.setInit(Role.INIT_YES);
		roleDao.insert(r);

		roleMenuDao.insert(menuDao.fetch(MENU.ORGANIZATION_ID.eq(orgId)).stream().map(m -> {
			RoleMenu rm = new RoleMenu();
			rm.forInsert();
			rm.setMenuId(m.getId());
			rm.setRoleId(r.getId());

			return rm;
		}).collect(Collectors.toList()));

		sender.send(MessageTypeContent.SYSTEM_ROLE_INSERT, MessageHeaderContent.ID, r.getId());
		return r;
	}

	/** 为超级管理员授权 */
	private void createCompanySuperAdminGrant(String roleId, String memberId, String orgId) {
        // 添加授权基本表
        Grant g = new Grant();
        g.forInsert();
        g.setOrganizationId(orgId);
        g.setRoleId(roleId);
        g.setOperatorTypes(OPERATOR_TYPES);
        g = grantDao.insert(g);
	}

	@Override
	public int[] getTypes() {
		return new int[]{MessageTypeContent.HR_CLOUD_CENTER_SYNC_FIRST, MessageTypeContent.HR_CLOUD_CENTER_SYNC_FIRST_FIX};
	}

}
