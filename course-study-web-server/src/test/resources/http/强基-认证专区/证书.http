### 子认证 证书导入
POST {{domain}}{{course}}sub-authenticated/import-certificate
Accept: */*
Authorization: Bearer__2b42f85b88250d131132ca1a643a4832
Content-Type: application/x-www-form-urlencoded

subAuthenticatedId=10802bdc-bbac-4f39-8faa-03cc9b4cd7d4&fileId=413ab5a2-d7ca-432c-9e6e-4713f78d3ba9



### 子认证 证书删除
POST {{domain}}{{course}}sub-authenticated/delete-certificate?id=27bf1872-208c-4776-9049-a4f9f03bc82c&reasonForDeletion=测试删除数据的备注




### 子认证-证书列表查询
GET {{domain}}{{course}}sub-authenticated/query-certificate?page=1&pageSize=10&subAuthenticatedId=10802bdc-bbac-4f39-8faa-03cc9b4cd7d4&contain=1
Accept: */*
Authorization: Bearer__cae31c6bfcb3246475b3b0314c739479

