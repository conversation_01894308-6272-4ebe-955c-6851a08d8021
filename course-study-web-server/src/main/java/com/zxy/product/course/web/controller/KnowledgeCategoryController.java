package com.zxy.product.course.web.controller;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zxy.product.course.web.util.ImportExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Reader.Result;
import com.zxy.common.office.excel.Reader.Row;
import com.zxy.common.office.excel.ValidateContext;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.office.excel.export.Sheet;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.DefaultRow;
import com.zxy.common.office.excel.support.validator.LengthValidator;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.office.excel.support.validator.TrueValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.KnowledgeCategoryService;
import com.zxy.product.course.api.KnowledgeService;
import com.zxy.product.course.api.OrganizationService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.KnowledgeCategory;
import com.zxy.product.course.entity.KnowledgeInfo;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.Organization;
import com.zxy.product.course.web.controller.xlsx.BaseImportController;
import com.zxy.product.course.web.util.DateUtil;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.system.api.setting.DataPermissionService;
import com.zxy.product.system.entity.DataPermission;

/**
 * Created by Eric on 2017/3/6.
 */
@Controller
@RequestMapping("/knowledge-category")
public class KnowledgeCategoryController extends BaseImportController {
    private KnowledgeCategoryService knowledgeCategoryService;
    private OrganizationService organizationService;
    private DataPermissionService permissionService;
    private MemberService memberService;
    private KnowledgeService knowledgeService;
    private FileService fileService;
    private AttachmentResolver attachmentResolver;

    private Reader reader = new DefaultReader();

    @Override
    @Autowired
    public void setFileService(FileService fileService) {
		this.fileService = fileService;
	}

    @Override
    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
		this.attachmentResolver = attachmentResolver;
	}

    @Autowired
    public void setKnowledgeCategoryService(KnowledgeCategoryService knowledgeCategoryService) {
        this.knowledgeCategoryService = knowledgeCategoryService;
    }
    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setKnowledgeService(KnowledgeService knowledgeService) {
        this.knowledgeService = knowledgeService;
    }

    @Autowired
    public void setPermissionService(DataPermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    /**
     * [管理员端]根据机构id查询目录列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Permitted
    @Param(name = "organizationId",type = String.class, required = true)
    @Param(name = "state", type = Integer.class)
    @Param(name = "notIncludeCatelogId",type = String.class) // 不包含该目录和其子目录
    @JSON("id,parentId,name,path")
    public List<KnowledgeCategory> findByOrgId(RequestContext context, Subject<Member> subject){
        return knowledgeCategoryService.findByOrgId(
                subject.getCurrentUserId(),
                context.getString("organizationId"),
                context.getOptionalInteger("state"),
                context.getOptionalString("notIncludeCatelogId"));
    }

    /**
     * 目录选择器
     * @param context
     * @return
     */
    @RequestMapping(value= "/chooser", method = RequestMethod.GET)
    @Permitted
    @Param(name = "companyId",type = String.class)
    @Param(name = "rootOrganizationId",type = String.class, required = true)
    @Param(name = "currentCatalogId",type = String.class)
    @JSON("id,parentId,name,path")
    public List<KnowledgeCategory> findChooserByOrgId(RequestContext context, Subject<Member> subject){
        List<KnowledgeCategory> knowledgeCategoryList = null;
        // 公司ID
        Optional<String> companyId = context.getOptionalString("companyId");
        //  所属集团
        String rootOrganizationId = context.getString("rootOrganizationId");
        Optional<String> currentCatalogId = context.getOptionalString("currentCatalogId");
        DataPermission dataPermission = permissionService.getOptionalByKey(rootOrganizationId, DataPermission.CODE_KNOWLEDGE).get();
        if(companyId.isPresent() && dataPermission.getOrgLevel()!=null && dataPermission.getOrgLevel().equals(DataPermission.LEVEL_CHECKED)) {
        	if(currentCatalogId.isPresent()){//剔除该目录下的子目录
            	return knowledgeCategoryService.findByNoclude(
                        subject.getCurrentUserId(),
                        companyId.get(),
                        currentCatalogId);
            }else{
                knowledgeCategoryList = knowledgeCategoryService.findChooserByOrgId(companyId.get());
            }
        }
        if(knowledgeCategoryList == null || knowledgeCategoryList.size() == 0) {
        	if(currentCatalogId.isPresent()){//剔除该目录下的子目录
            	return knowledgeCategoryService.findByNoclude(
                        subject.getCurrentUserId(),
                        rootOrganizationId,
                        currentCatalogId);
            }else{
                knowledgeCategoryList = knowledgeCategoryService.findChooserByOrgId(rootOrganizationId);
            }
        }
        return knowledgeCategoryList;
    }
    /**
     * [学员端]查询目录列表
     * @return
     */
    @RequestMapping(value = "/front", method = RequestMethod.GET)
    @Permitted
    @JSON("id,parentId,name,path")
    public List<KnowledgeCategory> findForFront(){
        return knowledgeCategoryService.findForFront();
    }

    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "organizationId",type = String.class, required = true)
    @Param(name = "parentId",type = String.class)
    @Param(name = "name",type = String.class,required = true)
    @Param(name = "code",type = String.class,required = true)
    @Param(name = "sequence",type = Integer.class)
    @Param(name = "state",type = Integer.class)
    @JSON("id")
    @Audit(module = "知识管理", subModule = "知识管理-知识目录", action = Audit.Action.INSERT, fisrtAction = "新增", desc = "新增知识目录{0}", params = {"name"})
    public KnowledgeCategory insert(RequestContext context){
        return knowledgeCategoryService.insert(
                context.getString("organizationId"),
                context.getOptionalString("parentId"),
                context.getString("name"),
                context.getString("code"),
                context.getOptionalInteger("sequence"),
                context.getOptionalInteger("state")
        );
    }

    @RequestMapping(value = "/{id}",method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "organizationId",type = String.class,required = true)
    @Param(name = "name",type = String.class,required = true)
    @Param(name = "code",type = String.class,required = true)
    @Param(name = "parentId",type = String.class)
    @Param(name = "sequence",type = Integer.class)
    @Param(name = "state",type = Integer.class)
    @JSON("id")
    @Audit(module = "知识管理", subModule = "知识管理-知识目录", action = Audit.Action.UPDATE, fisrtAction = "修改", desc = "修改知识目录{0}", params = {"name"})
    public KnowledgeCategory update(RequestContext context){
        return knowledgeCategoryService.update(
                context.getString("id"),
                context.getString("name"),
                context.getString("code"),
                context.getString("organizationId"),
                context.getOptionalInteger("sequence"),
                context.getOptionalString("parentId"),
                context.getOptionalInteger("state")
        );
    }

    @RequestMapping(value = "/update-state/{id}",method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "state",type = Integer.class,required = true)
    @JSON("id")
    public KnowledgeCategory updateState(RequestContext context){
        return knowledgeCategoryService.updateState(
                context.getString("id"),
                context.getInteger("state")
        );
    }

    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name") // 知识目录名称 审计
    @JSON("*")
    @Audit(module = "知识管理", subModule = "知识管理-知识目录", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "删除知识目录{0}", params = {"name"})
    public int delete(RequestContext context){
    	//判断该目录是否有知识关联
        List<KnowledgeInfo> list = knowledgeService.findByCategoryId(context.getString("id"));
        if(list.size()>0){
        	return 0;
        }else{
            return knowledgeCategoryService.delete(context.getString("id"));
        }
    }

    @RequestMapping(value = "/{id}",method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("id,name,code,sequence,parentId,state")
    @JSON("parent.(id,name)")
    public KnowledgeCategory get(RequestContext context){
        return knowledgeCategoryService.get(context.getString("id"));
    }

    @RequestMapping(value = "/export-template", method = RequestMethod.GET)
	@Param(name="organizationId", type=String.class,required=true)
    public void exportImportTemplate(RequestContext context, Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        String  fileName = this.parseFileName( "知识目录导入信息", context.getRequest().getHeader("User-Agent"));
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        Writer writer = new ExcelWriter();
        writer.sheet("知识目录导入信息", new ArrayList<>())
               // .field("序号(必填)", x -> "")
                .field("目录名称(必填)", x -> "")
                .field("目录编码(必填)", x -> "")
                .field("上级目录编码", x -> "");
                //.field("排序", x -> "");
                //.field("所属机构(必填)", x -> "");

        writer.sheet("目录编码", getKnowledgeCodeMapList(context.getString("organizationId")))
		.field("名称", KnowledgeCategory::getName)
		.field("编码", KnowledgeCategory::getCode);

        //机构编码
        /*writer.sheet("组织编码", getOrganizationCodeMapList(subject.getCurrentUserId()))
		.field("部门", m -> m.get(KnowledgeCategory.KNOWLEDGE_CATEGORY_ORGANIZATION_DEC))
		.field("部门编码", m -> m.get(KnowledgeCategory.KNOWLEDGE_CATEGORY_CODE));*/

        writer.write(response.getOutputStream());
    }

    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @Param(name="fileId", type=String.class, required=true)
	@Param(name="organizationId", type=String.class,required=true)
    @JSON("*")
	@Permitted()
    @Audit(module = "知识管理", subModule = "知识管理-知识目录", action = Audit.Action.IMPORT, fisrtAction = "导入", desc = "导入知识目录")
    public Object importKnowledge(RequestContext requestContext, Subject<Member> subject) {
    	Optional<Attachment> attachment = fileService.get(requestContext.getString("fileId"));
    	String organizationId = requestContext.getString("organizationId");
    	Map<String, Object> m = attachment.map(t -> {
    		reader
				.skipRows(1)
				//序号
				/*.setColumn(0, String.class, new RequiredValidator<String>().compose((v, context, prev) -> {
					return indexValidator(v, context, prev);
				}))*/
				//目录名称
				.setColumn(0, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 10)))
				//目录编码
				.setColumn(1, String.class, new RequiredValidator<String>().compose((v, context, prev) -> {
					return codeValidator(v, context, prev);
				}))
				// 上级目录
				.setColumn(2, String.class, (v, context,prev) -> {
					return codeValidator(v, context, prev);
				});
				//排序
				/*.setColumn(3, String.class, (v, context, prev) -> {
					return sequenceValidator(v, context, prev);
				});*/
				//.setColumn(5, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 40))); // 归属部门

	    	com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
	        fastDFSAttachment.setPath(t.getPath());
	        InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
	        Reader.Result result = null;
	        Map<String, Object> map = null;

	        try {
	        	result = reader.read(inputStream);
	        	if(result.getErrorRows().size()==0&&result.getCorrectRows().size()==0){
            	 	throw new UnprocessableException(ErrorCode.ImportNullFile);
	        	}
	        	 List<KnowledgeCategory> list = result.map(row -> {
	 	            KnowledgeCategory category = new KnowledgeCategory();
	 	            //category.forInsert();
	 	            category.setId(java.util.UUID.randomUUID().toString());
	 	            category.setCreateTime(System.currentTimeMillis()+row.getIndex());//为了解决批量打时间戳的时候时间相同无法排序问题
	 	            category.setIndex(row.getIndex());
	 	            //category.setIndex(Integer.valueOf(trim(row.get(0, String.class))));
	 	            category.setName(trim(row.get(0, String.class)));
	 	            category.setCode(trim(row.get(1, String.class)));
	 	            category.setParentId(trim(row.get(2, String.class)));//暂时存code，后面根据code转化
	 	            //category.setSequence(row.get(3, String.class)==null?null:Integer.parseInt(trim(row.get(3, String.class))));
	 	            category.setOrganizationId(organizationId);
	 	            category.setState(KnowledgeCategory.STATE_ENABLE);
	 	            return category;
	 	        });

	        	List<KnowledgeCategory> inserts = new ArrayList<>();
	        	List<Reader.Row> errorRows = validate(inserts, list, result.getErrorRows(), subject.getCurrentUserId());
	        	/*if (inserts.size() > 0) {
		 	        knowledgeCategoryService.save(inserts);
	        	}*/
				map = new HashMap<>();

		        map.put("successCount", inserts.size());
		        map.put("errorCount", errorRows.size());
		        if (errorRows !=null && !errorRows.isEmpty()) {
		        	map.put("errorFileId", createErrorTempFile(errorRows, subject.getCurrentUserId(), organizationId));
		        }
				map.put("errorList", createErrorList(result));
				map.put("data", com.alibaba.fastjson.JSON.toJSONString(list));
	        } catch (IOException e) {
	        	e.printStackTrace();
	        }
	        return map;
    	}).get();
        return m;
    }

    private String trim(String s){
    	if(s!=null){
    		s = s.trim();
    	}
    	return s;
    }

	private Reader.Row createErrorRow(KnowledgeCategory k, Map<ErrorCode,Integer> errorRowMap) {
		Reader.Row row = new DefaultRow(k.getIndex());
		//row.addData(0, (k.getIndex()+1));
		row.addData(0, k.getName());
		row.addData(1, k.getCode());
		row.addData(2, k.getParent().getCode());
		//row.addData(3, k.getSequence()==null?null:k.getSequence().toString());
		//row.addData(5, k.getOrganization().getCode());
		errorRowMap.forEach((key,value) -> {
			row.addError(new Validator.DataError(row.getIndex(), value, key));
		});
		return row;
	}

	/**
	 * 导入的错误临时文件
	 * @param result
	 * @return
	 * @throws IOException
	 */
	private String createErrorTempFile(List<Reader.Row> errorRows, String memebrId,String organizationId) throws IOException {
		Writer writer = new ExcelWriter();
		if (errorRows !=null && !errorRows.isEmpty()) {
			Sheet<Reader.Row> s = writer.sheet("导入异常目录", errorRows)
				//.field("序号", e -> e.get(0, String.class))
				.field("目录名称", e -> e.get(0, String.class))
				.field("目录编码", e -> e.get(1, String.class))
				.field("上级目录编码", e -> e.get(2, String.class));
				//.field("排序", e -> e.get(3, String.class));


			writer.sheet("上级目录编码", getKnowledgeCodeMapList(organizationId))
			.field("名称", KnowledgeCategory::getName)
			.field("编码", KnowledgeCategory::getCode);

			/*writer.sheet("部门编码", getQuestionOrganizationCodeMapList(memebrId))
			.field(Question.QUESTION_ORGANIZATION_DEC, m -> m.get(Question.QUESTION_ORGANIZATION_DEC))
			.field(Question.QUESTION_CODE, m -> m.get(Question.QUESTION_CODE));*/
		}
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		writer.write(os);
		return uploadTempFile(new ByteArrayInputStream(os.toByteArray()));
	}

	/**
	 * 输出流转文件，再转输入流上传到FASTDFS
	 * @param file
	 * @return
	 * @throws IOException
	 */
	private String uploadTempFile(InputStream is) throws IOException {
    	MultipartFile mfile = transferTo(is, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "导入失败记录.xlsx", is.available());
    	com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(null, mfile, Optional.empty());
    	String[] filename = new String[]{ restfulAttachment.getFilename() };
    	String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, new String[]{"xlsx"}, path, size);
        return result.get(0).getId();
	}

	/**
     * 重写MultipartFile实现， 将文件流转换成MultipartFile
     * @param inputStream
     * @param contentType
     * @param filename
     * @param size
     * @return
     */
    private MultipartFile transferTo(final InputStream inputStream, String contentType, String filename, int size) {
        return new MultipartFile() {

            @Override
            public String getName() {
                return filename;
            }

            @Override
            public String getOriginalFilename() {
                if (filename == null) {
                    // Should never happen.
                    return "";
                }

                // Check for Unix-style path
                int unixSep = filename.lastIndexOf("/");
                // Check for Windows-style path
                int winSep = filename.lastIndexOf("\\");
                // Cut off at latest possible point
                int pos = (winSep > unixSep ? winSep : unixSep);
                if (pos != -1)  {
                    // Any sort of path separator found...
                    return filename.substring(pos + 1);
                }
                else {
                    // A plain name
                    return filename;
                }
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return size == 0;
            }

            @Override
            public long getSize() {
                return size;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return  inputStream;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
    }

    private List<Row> validate(List<KnowledgeCategory> inserts, List<KnowledgeCategory> categorys, List<Row> errorRows, String currentUserId) {
		List<String> errorCategoryIds = new ArrayList<>();
		Map<ErrorCode,Integer> errorRowMap = new HashMap<ErrorCode,Integer>();

		//判断父目录是否存在
		categorys.forEach(c -> {
	        boolean ifOk = true;
			if(c.getParentId()!=null&&c.getParentId().length()>0){
				KnowledgeCategory parent = getCategoryByCode(c.getOrganizationId(),c.getParentId());
				if (parent == null) {
					parent = new KnowledgeCategory();
					parent.setCode(c.getParentId());
					c.setParent(parent);
					errorRowMap.put(ErrorCode.ParentNotExists,2);
					ifOk = false;
					errorCategoryIds.add(c.getId());
				}else{
					//校验目录最高为4级
					String path = parent.getPath();
					String[] paths = path.split(",");
	                if(paths.length>=4){
	                	//Reader.Row row = createErrorRow(c, ErrorCode.MaxnumError,2);
	                	//errorRows.add(row);
	                	errorRowMap.put(ErrorCode.MaxnumError,2);
						ifOk = false;
	                	//throw new UnprocessableException(ErrorCode.MaxnumError);
	                }
					c.setParentId(parent.getId());
					c.setParent(parent);
				}
			}else{
				c.setParentId(null);
				c.setParent(new KnowledgeCategory());
			}

			//判断名称是否已存在
			List<KnowledgeCategory> list = knowledgeCategoryService.findByName(c.getOrganizationId(), c.getName());
			if(list!=null&&list.size()>0){
				//Reader.Row row = createErrorRow(c, ErrorCode.NameAlreadyExists,0);
				//errorRows.add(row);
				errorRowMap.put(ErrorCode.NameAlreadyExists,0);
				ifOk = false;
				errorCategoryIds.add(c.getId());
			}
			//判断code是否已存在
			List<KnowledgeCategory> codelist = knowledgeCategoryService.findByCode(c.getOrganizationId(), c.getCode());
			if(codelist!=null&&codelist.size()>0){
				//Reader.Row row = createErrorRow(c, ErrorCode.CodeAlreadyExists,1);
				//errorRows.add(row);
				errorRowMap.put(ErrorCode.CodeAlreadyExists,1);
				ifOk = false;
				errorCategoryIds.add(c.getId());
			}

			if(ifOk){
				inserts.add(c);
				//一条一条插入，为了适应导入里包含父子目录关系
				knowledgeCategoryService.insert(c);
			}else{
				Reader.Row row = createErrorRow(c,errorRowMap);
				errorRows.add(row);
			}
		});

		categorys = categorys.stream().filter(q -> errorCategoryIds.stream().collect(Collectors.joining(",")).indexOf(q.getId()) < 0).collect(Collectors.toList());
		/*categorys.forEach(q -> {
			inserts.add(q);
			//一条一条插入，为了适应导入里包含父子目录关系
			knowledgeCategoryService.insert(q);
		});*/

		/*int i = 0;
		for (Row row : errorRows) {
			row.addData(0, String.valueOf((++i)));
		}*/
		return errorRows;
	}

//	private Organization getOrganizationByCode(String code) {
//		return organizationService.getByCode("1",code).map(o -> {
//			Organization organization =  new Organization();
//			organization.setId(o.getId());
//			organization.setName(o.getName());
//			return organization;
//		}).orElse(new Organization());
//	}

//	private List<Map<String, String>> getOrganizationCodeMapList(String currentUserId) {
//		 List<Organization> organizations = organizationService.getByMember(currentUserId);
//		return organizations.stream().map(t -> {
//			Map<String, String> map = new HashMap<>();
//			map.put(KnowledgeCategory.KNOWLEDGE_CATEGORY_ORGANIZATION_DEC, t.getName());
//			map.put(KnowledgeCategory.KNOWLEDGE_CATEGORY_CODE, t.getCode());
//			return map;
//		}).collect(Collectors.toList());
//	}

    private KnowledgeCategory getCategoryByCode(String organizationId, String code){
    	List<KnowledgeCategory> list = knowledgeCategoryService.findByCode(organizationId, code);
    	if(list==null||list.size()==0){
    		return null;
    	}else{
    		return list.get(0);
    	}
    }


	// 目录名称校验
	private boolean nameValidator(String v, ValidateContext context, Object[] prev) {
		return true;
	}

	private boolean sequenceValidator(String v, ValidateContext context, Object[] prev) {
		Pattern p = Pattern.compile("^[1-9]\\d*$");
		if(v!=null&&v.length()>0&&!p.matcher(v).find()){
			context.error(ErrorCode.ImportSequenceError);
			return false;
		}
		if(v!=null&&v.length()>3){
			context.error(ErrorCode.ImportSequenceError);
			return false;
		}
		return true;
	}

	// 目录code校验
	private boolean codeValidator(String v, ValidateContext context, Object[] prev) {
		if(v!=null&&v.length()>15){
			context.error(ErrorCode.KnowledgeCategoryCodeError);
			return false;
		}
		if(v!=null&&v.length()>0&&!v.matches("[A-Za-z0-9_]+")){
			context.error(ErrorCode.KnowledgeCategoryCodeError);
			return false;
		}
		return true;
	}

    /**
	 * 返回错误信息提示
	 * 第几行，错误代码
	 * @param result
	 * @return
	 */
	private String createErrorList(Result result) {
		List<Map<String, Object>> errors = new ArrayList<>();
        for(Validator.DataError e : result.getErrors() ){
            Map<String, Object> map = new HashMap<>();
            map.put("row", e.getRow());
            map.put("column", e.getColumn());
            map.put("code", e.getCode() == null ? "" : e.getCode().getCode());
            errors.add(map);
        }
		return com.alibaba.fastjson.JSON.toJSONString(errors);
	}


    @RequestMapping("/category-import")
    @Params
    @JSON("*")
    public Object exportImport(@RequestParam("file") MultipartFile file) throws IOException {
        Reader r = new DefaultReader().skipRows(1)
                .setColumn(0, String.class, new TrueValidator<String>()) // 序号
                .setColumn(1, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 30))) // 目录名称
                .setColumn(2, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 100))) // 目录编码
                .setColumn(3, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 40))) // 上级目录
                .setColumn(4, String.class, new TrueValidator<String>()) // 排序
                .setColumn(5, String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 40))); // 归属部门
        Reader.Result result = r.read(file.getInputStream());

        List<KnowledgeCategory> list = result.map(row -> {
            KnowledgeCategory category = new KnowledgeCategory();
            category.forInsert();
            category.setName(row.get(1, String.class));
            category.setCode(row.get(2, String.class));
            category.setParentId(row.get(3, String.class));
            category.setSequence(Integer.parseInt(row.get(4, String.class)));
            category.setOrganizationId(row.get(5, String.class));
            category.setState(KnowledgeCategory.STATE_ENABLE);
            return category;
        });
        knowledgeCategoryService.save(list);
        return result.getErrors();
    }

    @RequestMapping(value = "/export", method = RequestMethod.GET)
	@Param(name="organizationId", type=String.class,required=true)
    @Param(name="id", type=String.class)
    @Audit(module = "知识管理", subModule = "知识管理-知识目录", action = Audit.Action.EXPORT, fisrtAction = "导出", desc = "导出知识目录")
    public void export(RequestContext context, Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        String filename = "知识目录信息";
        HttpServletRequest request = context.getRequest();
    	if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
    	    filename = URLEncoder.encode(filename, "UTF-8");
    	} else {
    	    filename = new String(filename.getBytes("gb2312"), "ISO8859-1");
    	}

        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="+filename+".xlsx");
        List<KnowledgeCategory> list = knowledgeCategoryService.findForExport(context.getOptionalString("organizationId"),context.getOptionalString("id"));

        Writer writer = new ExcelWriter();
        writer.sheet("目录列表", list)
                .field("目录名称", KnowledgeCategory::getName)
                .field("目录编码", KnowledgeCategory::getCode)
                .field("上级目录", category -> category.getParent().getName())
                .field("排序", KnowledgeCategory::getSequence)
                .field("所属机构", category -> category.getOrganization().getName())
                .field("创建时间", KnowledgeCategory::getCreateTime, x -> DateUtil.dateLongToString(x, DateUtil.YYYY_MM_DD_HH_MM));
        String date = com.zxy.product.system.util.DateUtil.dateLongToString(System.currentTimeMillis(), com.zxy.product.system.util.DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        writer.write(response.getOutputStream(), workBook -> {
            try {
                ImportExcelUtil.putWaterRemarkToExcel(workBook, workBook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

    }

    private List<KnowledgeCategory> getKnowledgeCodeMapList(String organizationId) {
    	List<KnowledgeCategory> knows = knowledgeCategoryService.findChooserByOrgId(organizationId);
    	return knows;
    }
}


