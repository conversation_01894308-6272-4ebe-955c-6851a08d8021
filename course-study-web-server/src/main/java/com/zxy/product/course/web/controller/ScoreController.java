package com.zxy.product.course.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.other.ScoreService;
import com.zxy.product.course.entity.KnowledgeInfo;
import com.zxy.product.course.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;
import java.util.Optional;

/**
 * Created by keeley on 2017/3/22.
 */
@Controller
@RequestMapping("/score")
public class ScoreController {

    private ScoreService scoreService;

    @Autowired
    public void setScoreService(ScoreService scoreService) {
        this.scoreService = scoreService;
    }

    // 是否评分了
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true) // 课程id
    @JSON("score")
    public Map<String,Integer> isScore(RequestContext context, Subject<Member> subject) {
        Optional<Integer> score = scoreService.userScore(context.getString("id"), subject.getCurrentUserId());
        return score.map( x-> ImmutableMap.of("score", x)).orElse(null);
    }
}
