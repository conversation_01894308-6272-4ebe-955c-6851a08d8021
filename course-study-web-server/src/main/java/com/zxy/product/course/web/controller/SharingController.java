package com.zxy.product.course.web.controller;


import com.google.common.collect.ImmutableMap;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.PointRule;
import com.zxy.product.system.entity.UserBehavior;
import com.zxy.product.course.web.aspectj.Behavior;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@Controller
@RequestMapping("/sharing")
public class SharingController {

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @Param(name="contentType")
    @JSON("*")
    @Behavior(contentID = "id",type= UserBehavior.SHARE,pageSource="/api/vi/course/sharing",contentType = "contentType")
    public Map<String, Object> get(RequestContext context, Subject<Member> subject) {
        messageSender.send(com.zxy.product.system.content.MessageTypeContent.SYSTEM_POINT_CHANGE,
                com.zxy.product.system.content.MessageHeaderContent.MEMBER_ID,subject.getCurrentUserId()
                , com.zxy.product.system.content.MessageHeaderContent.RULE_KEY, PointRule.SHARE);
        return ImmutableMap.of("success", "ok");
    }
}
