package com.zxy.product.course.web.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/26
 * @description ：jwt加密
 */
public class JwtUtil {
    private static final String ORGID = "1468838820714729472";//orgid
    private static final String ECID = "ecid";
    private static final String SECRET = "CAFB13362661E048956D1D1A1005DB41";//API SECRECT
    private static final String EXP = "exp";//私密key
    private static final String ISS = "55A4BDF0765AD7A2CAE5C86472933DAF";//API KEY

    private static final String ORGIDTEST = "1357221564709408768";//orgid
    private static final String ECIDTEST = "ecid";
    private static final String SECRETTEST = "8393309787D7E5552E5014AE9C25ACDC";//API SECRECT
    private static final String EXPTEST = "exp";//私密key
    private static final String ISSTEST = "CD6311985A6504C98B0358B678F50970";//API KEY

    public static String createJavaJWT(){
        return JWT.create().withIssuer(ISS)
                .withClaim(ECID, ORGID)
                .withClaim(EXP, System.currentTimeMillis() / 1000 + 86400)
                .sign(Algorithm.HMAC256(SECRET));
    }

    public static String createTestJavaJWT(){
        return JWT.create().withIssuer(ISSTEST)
                .withClaim(ECIDTEST, ORGIDTEST)
                .withClaim(EXPTEST, System.currentTimeMillis() / 1000 + 86400)
                .sign(Algorithm.HMAC256(SECRETTEST));
    }
}
