package com.zxy.product.course.web.controller.ftp;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/jiutian-sync")
public class JiuTianSyncDataController {
    private final static String COURSE_LOG_TABLE = "courselog";
    private final static String COURSE_STUDY_TABLE = "coursestudy";
    private final static String COURSE_PROGRESS_TABLE = "courseprogress";
    private final static String COURSE_SCORE_TABLE = "coursescore";
    private final static String COURSE_INFO_TABLE = "courseinfo";
    private final static String SUBJECT_LOG_TABLE = "subjectlog";
    private final static String USER_TABLE = "user";

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @RequestMapping(value = "/sync-data", method = RequestMethod.GET)
    @Permitted
    @Param(name = "businessTable",type = String.class)// 需要同步的表名
    @Param(name = "minDay", type = String.class)// 需要同步年月日类型时间day表用
    @Param(name = "maxDay", type = String.class)// 需要同步年截止月日类型时间day表用
    @Param(name = "startTime", type = String.class)
    @Param(name = "endTime", type = String.class)
    @Param(name = "businessType", type = Integer.class)// 同步数据类型1.全量，2.增量
    @JSON("*")
    public Map<String,Object> courseProgress(RequestContext context) {
        String businessTable = context.getString("businessTable");
        Optional<String> startTime = context.getOptionalString("startTime");
        Optional<String> endTime = context.getOptionalString("endTime");
        if (businessTable.equals(USER_TABLE)) {
            messageSender.send(MessageTypeContent.FTP_UPLOAD_USER_DATA_ALL);
            return ImmutableMap.of("success", "user");
        }
        if (businessTable.equals(COURSE_INFO_TABLE)) {
            // 同步全量的课程信息数据
            messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_INFO_DATA_ALL);
            return ImmutableMap.of("success", "courseinfo");
        }
        // 全量同步
        if (context.getInteger("businessType") == 1) {
            if (businessTable.equals(COURSE_LOG_TABLE)) {
                // 同步7天的day数据
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_ALL,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""),
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            } else if (businessTable.equals(COURSE_PROGRESS_TABLE)) {
                // 同步全量的progress
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_PROGRESS_DATA_ALL,
                        MessageHeaderContent.ENDTIME, endTime.orElse("")
                        );
            } else if (businessTable.equals(COURSE_SCORE_TABLE)) {
                // 同步全量的score
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_SCORE_DATA_ALL,
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            } else if (businessTable.equals(COURSE_STUDY_TABLE)) {
                // 同步log全量
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_STUDY_DATA_ALL,
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            } else if (businessTable.equals(SUBJECT_LOG_TABLE)) {
                // 同步7天的day数据
                messageSender.send(MessageTypeContent.FTP_UPLOAD_SUBJECT_LOG_DATA_ALL,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""),
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            }

        } else if (context.getInteger("businessType") == 2) {
            if (businessTable.equals(COURSE_LOG_TABLE)) {
                // 同步1天的day数据增量
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_LOG_DATA_INCREMENT,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""));
            } else if (businessTable.equals(COURSE_PROGRESS_TABLE)) {
                // 同步增量的progress
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_PROGRESS_DATA_INCREMENT,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""),
                        MessageHeaderContent.ENDTIME, endTime.orElse("")
                        );
            } else if (businessTable.equals(COURSE_SCORE_TABLE)) {
                // 同步增量的score
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_SCORE_DATA_INCREMENT,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""),
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            } else if (businessTable.equals(COURSE_STUDY_TABLE)) {
                // 同步log全量
                messageSender.send(MessageTypeContent.FTP_UPLOAD_COURSE_STUDY_DATA_INCREMENT,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""),
                        MessageHeaderContent.ENDTIME, endTime.orElse(""));
            } else if (businessTable.equals(SUBJECT_LOG_TABLE)) {
                // 同步7天的day数据
                messageSender.send(MessageTypeContent.FTP_UPLOAD_SUBJECT_LOG_DATA_INCREMENT,
                        MessageHeaderContent.STARTTIME, startTime.orElse(""));
            }
        }

        return ImmutableMap.of("success", true);

    }
    public static final int STUDY_TEAM_CONFIRMED_CREDITS_HOURS = 61401;

    @RequestMapping(value = "/sync-data1", method = RequestMethod.GET)
    @Param(name = "activityId",type = String.class)// 活动id
    @Param(name = "memberIds", type = String.class)// 人员id
    @JSON("*")
    public Map<String,Object> courseProgress1(RequestContext context) {
        String activityId = context.getString("activityId");
        String memberIds = context.getString("memberIds");
        messageSender.send(STUDY_TEAM_CONFIRMED_CREDITS_HOURS, MessageHeaderContent.ID, activityId, "memberId", memberIds);
        return ImmutableMap.of("success", "user");
    }
}
