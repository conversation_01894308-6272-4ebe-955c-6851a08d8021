package com.zxy.product.exam.web;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import com.zxy.common.restful.websocket.WebSocketConfig;
import com.zxy.product.exam.web.config.RPCClientConfig;
import com.zxy.product.exam.web.config.CacheConfig;
import com.zxy.product.exam.web.config.ExamWebSocketConfig;
import com.zxy.product.exam.web.config.MessageConfig;
import com.zxy.product.exam.web.config.RestfulConfig;
import com.zxy.product.exam.web.config.WebConfig;

@SpringBootApplication
@Import({
    RestfulConfig.class, RPCClientConfig.class,WebConfig.class,
    MessageConfig.class,CacheConfig.class,
    WebSocketConfig.class,ExamWebSocketConfig.class
})
public class ExamWebServerMain {

    public static void main(String[] args) {
        SpringApplication.run(ExamWebServerMain.class, args);
    }

}
