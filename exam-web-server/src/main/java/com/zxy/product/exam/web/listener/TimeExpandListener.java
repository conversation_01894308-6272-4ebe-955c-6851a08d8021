package com.zxy.product.exam.web.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.web.websocket.ExamWebSocketHandler;

@Component
public class TimeExpandListener extends AbstractMessageListener {

	private static final Logger LOGGER = LoggerFactory.getLogger(TimeExpandListener.class);

	private ExamWebSocketHandler examWebSocketHandler;

	@Autowired
	public void setExamWebSocketHandler(ExamWebSocketHandler examWebSocketHandler) {
		this.examWebSocketHandler = examWebSocketHandler;
	}

	@Override
	protected void onMessage(Message message) {
		LOGGER.info("exam/TimeExpandListener:{}", message);
		String userId = message.getHeader(MessageHeaderContent.ID);
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String time = message.getHeader(MessageHeaderContent.TIME);

		//延时，这边只需要判断下当前的web-server有没有session，有就发送消息
		//产品 bug 62644,延时发消息的同时，发送服务器当前时间
		if (examWebSocketHandler.isSessionOpen(createExamUserKey(examId, userId))) {
			examWebSocketHandler.sendMessageToUser(createExamUserKey(examId, userId),
			        ExamWebSocketHandler.TIME_EXPAND + "_" + time + "#" + System.currentTimeMillis());
		}
	}

	private String createExamUserKey(String examId, String userId) {
		return examId + "#" + userId;
	}


	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_TIME_EXPAND
		};
	}

}
