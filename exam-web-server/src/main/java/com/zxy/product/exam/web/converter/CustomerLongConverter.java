package com.zxy.product.exam.web.converter;

import java.beans.PropertyEditorSupport;

import org.springframework.core.convert.converter.Converter;

import com.zxy.product.exam.web.util.DateUtil;

/**
 * 用于实现 时间字符串转换为Long类型
 * <AUTHOR>
 *
 */
public class CustomerLongConverter extends PropertyEditorSupport implements Converter<String, Long>{
    public static final String YYYY_DD_MM_REGEX = "[\\d]{4}-[\\d]{2}-[\\d]{2}";
    public static final String YYYY_DD_MM_HH_MM_SS_REGEX = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9]:[0-5][0-9])$";
    public static final String YYYY_DD_MM_HH_MM_REGEX = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9])$";

    @Override
    public void setAsText(String text) throws IllegalArgumentException {
        this.setValue(this.convert(text));
    }

    @Override
    public Long convert(String text) {
        if (text.matches(YYYY_DD_MM_REGEX)){
            return DateUtil.dateStringYYYYMMDD2Long(text);
        } else if (text.matches(YYYY_DD_MM_HH_MM_SS_REGEX)) {
        	return DateUtil.dateStringYYYYMMDDHHMMSS2Long(text);
        } else if (text.matches(YYYY_DD_MM_HH_MM_REGEX)) {
        	return DateUtil.dateStringYYYYMMDDHHMM2Long(text);
        } else if ("".equals(text)){
            return null;
        } else {
            return Long.parseLong(text);
        }
    }
}
