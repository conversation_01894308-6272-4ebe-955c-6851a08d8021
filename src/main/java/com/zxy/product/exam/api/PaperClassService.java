package com.zxy.product.exam.api;

import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.PaperClass;
import com.zxy.product.exam.entity.PaperClassQuestion;
import com.zxy.product.exam.entity.PaperClassTactic;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout = 300000)
public interface PaperClassService {

	@Transactional
	PaperClass insert(
		String organizationId,
		String name,
		Integer totalScore,
		Integer questionNum,
		Integer type,
		List<PaperClassQuestion> paperClassQuestions
	);

	@Transactional
	String delete(String id);

	@Transactional
	PaperClass update(
		String id,
		String organizationId,
		String name,
		Integer totalScore,
		Integer questionNum,
		List<PaperClassQuestion> paperClassQuestions
	);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PaperClass get(String id,String memberId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PaperClass get(String id);

	@Transactional
	PaperClass insertPaperClassTactics(
		String name,
		String organizationId,
		Integer totalScore,
		Integer questionNum,
		List<PaperClassTactic> tacitces
	);

	@Transactional
	String deletePaperTactic(String id);
	
	@Transactional
	PaperClass updatePapaerClassTacitces(
		String id,
		String organizationId,
		Integer totalScore,
		Integer questionNum,
		List<PaperClassTactic> tacitces
	);
	
	/**
	 * 随机组卷 策略列表
	 * 
	 * @param paperClassId
	 * @return
	 */
	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<PaperClassTactic> findPaperClassTactic(String paperClassId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PaperClass getSimplePaperClassInfo(String paperClassId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PaperClass getPaperByExamId(String examId);
}
