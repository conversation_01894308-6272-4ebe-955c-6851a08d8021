package com.zxy.product.exam.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.GridCourseExam;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 */
@RemoteService
public interface AuthExamService {

    /**
     * 网格长认证考试-查询学员需要跳转的考试和学习专区
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    GridCourseExam findGridCourseExam(Integer level, String currentUserId);

}
