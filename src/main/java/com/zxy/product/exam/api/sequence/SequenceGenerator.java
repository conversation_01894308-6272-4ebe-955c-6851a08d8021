package com.zxy.product.exam.api.sequence;

import com.zxy.common.base.annotation.RemoteService;

/**
 * 序列号生成接口
 * <AUTHOR>
 * @date 2017年11月10日
 */
@RemoteService(timeout=90000)
public interface SequenceGenerator {

    /**
     * redis获取并更新序列
     * @param type 存储类型，即为key
     * @return
     */
    Long getSequence(String type);

    /**
     * redis更新序列：第一次的时候才会调用
     * @param type 存储类型，即为key
     * @param sequence 起始值
     */
    void setSequence(String type, Long sequence);
}
