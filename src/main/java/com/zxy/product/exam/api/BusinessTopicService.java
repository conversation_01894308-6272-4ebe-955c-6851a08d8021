package com.zxy.product.exam.api;

import java.util.List;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.BusinessTopic;

/**
 * <AUTHOR>
 *
 */
@RemoteService
public interface BusinessTopicService {
	
	@Transactional
	List<BusinessTopic> insert(String businessId, Integer businessType, List<BusinessTopic> topics);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<BusinessTopic> findByBusiness(String businessId, Integer type);
	
	@Transactional
	String deleteByBusinessId(String businessId, Integer type);
	/**
	 * 根据业务ids查询标签id等
	 */
	@Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	List<BusinessTopic> getTopicInfoByBusinessIds(List<String> ids);
}
