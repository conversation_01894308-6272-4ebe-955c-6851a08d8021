package com.zxy.product.exam.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.GridAudit;
import com.zxy.product.exam.entity.GridFile;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * <AUTHOR>
 */
@RemoteService
public interface GridExamService {

    @Transactional
    GridFile saveGridFile(GridFile gridFile);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<GridFile> getGridFiles(Integer page, Integer pageSize, String memberId, String levelId, Integer type);

    /**
     * 网格长认证专区-后台-材料管理列表
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<GridFile> getGridFileList(Integer page, Integer pageSize, Optional<String> memberFullName, Optional<String> memberName, Optional<Integer> type, Optional<String> levelId, Optional<Long> uploadTimeStart, Optional<Long> uploadTimeEnd, Map<String, Set<String>> grantOrganizationPathMap);

    @Transactional
    GridAudit saveGridAudit(GridAudit gridAudit);

    @Transactional
    GridAudit updateGridAudit(String id, Integer professionalKnowledge, Integer keyCapabilities, Integer organizationalFeedback, String levelId, Integer pass, Long authTime);

    /**
     * 网格长认证专区-后台-审核管理列表
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<GridAudit> getPageGridAuditList(Integer page, Integer pageSize, Optional<String> memberFullName, Optional<String> memberName, Optional<Integer> pass, Optional<String> levelId, Map<String, Set<String>> grantOrganizationPathMap);

    /**
     * 查询审核管理列表-导出
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GridAudit> getGridAuditList(Optional<String> memberFullName, Optional<String> memberName, Optional<Integer> pass, Optional<String> levelId, Map<String, Set<String>> grantOrganizationPathMap);

    /**
     * 通过memberId查询GridAudit
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GridAudit> findGridAuditByMemberIds(List<String> memberIds);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void batchInsertGridAudit(List<GridAudit> correctInsertList);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void batchUpdateGridAudit(List<GridAudit> correctUpdateList);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GridAudit> getGridAuditInfo(String currentUserId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GridFile> getGridFilesByIds(List<String> ids);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> getCurrentYearGridExamIds();

    /**
     * 网格长初级、中级、高级认证证书发放日期在2022年的学员，在获得2024年补学专区证书后，将对应网格长认证证书发证日期更新为2024年补学专区证书发证日期。
     * 网格长中级、高级认证证书发放日期在2021年的学员，在获得2024年补学专区证书后，将对应网格长认证证书发证日期更新为2024年补学专区证书发证日期。*
     */
    @Transactional
    void updateAuthTime(String memberId, Long finishTime);
}
