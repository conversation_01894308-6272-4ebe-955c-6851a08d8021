package com.zxy.product.exam.entity.szfn;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户考试档案实体
 */
public class UserExamProfile implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 用户id */
    private String userId;
    
    /** 专题id */
    private String subjectId;
    
    /** 答题记录列表 */
    private List<AnswerItem> answerItems;
    
    /**
     * 答题记录项
     */
    public static class AnswerItem implements Serializable {
        /** 题目序号 */
        private Integer sequence;
        /** 得分 */
        private Integer score;
        /** 题目类型 */
        private Integer type;
        /** 答案 */
        private String answer;
        
        // Getters and Setters
        public Integer getSequence() { return sequence; }
        public void setSequence(Integer sequence) { this.sequence = sequence; }
        
        public Integer getScore() { return score; }
        public void setScore(Integer score) { this.score = score; }
        
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        
        public String getAnswer() { return answer; }
        public void setAnswer(String answer) { this.answer = answer; }
    }
    
    public UserExamProfile() {}
    
    // Getters and Setters
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getSubjectId() {
        return subjectId;
    }
    
    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }
    
    public List<AnswerItem> getAnswerItems() {
        if (answerItems == null) {
            answerItems = new ArrayList<>();
        }
        return answerItems;
    }
    
    public void setAnswerItems(List<AnswerItem> answerItems) {
        this.answerItems = answerItems;
    }
}
