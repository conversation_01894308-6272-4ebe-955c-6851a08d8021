/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 网格长认证考试等级表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IGridLevel extends Serializable {

    /**
     * Setter for <code>exam.t_grid_level.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_grid_level.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>exam.t_grid_level.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_grid_level.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_grid_level.f_level_code</code>. 等级编码
     */
    public void setLevelCode(String value);

    /**
     * Getter for <code>exam.t_grid_level.f_level_code</code>. 等级编码
     */
    public String getLevelCode();

    /**
     * Setter for <code>exam.t_grid_level.f_level_name</code>. 等级名称
     */
    public void setLevelName(String value);

    /**
     * Getter for <code>exam.t_grid_level.f_level_name</code>. 等级名称
     */
    public String getLevelName();

    /**
     * Setter for <code>exam.t_grid_level.f_level</code>. 等级 1,2,3,4,5
     */
    public void setLevel(Integer value);

    /**
     * Getter for <code>exam.t_grid_level.f_level</code>. 等级 1,2,3,4,5
     */
    public Integer getLevel();

    /**
     * Setter for <code>exam.t_grid_level.f_valid_date</code>. 有效期（年）， 0代表长期有效
     */
    public void setValidDate(Integer value);

    /**
     * Getter for <code>exam.t_grid_level.f_valid_date</code>. 有效期（年）， 0代表长期有效
     */
    public Integer getValidDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IGridLevel
     */
    public void from(IGridLevel from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IGridLevel
     */
    public <E extends IGridLevel> E into(E into);
}
