/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 考试注册表_2024
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IExamRegist_2024 extends Serializable {

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_exam_id</code>.
     */
    public void setExamId(String value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_exam_id</code>.
     */
    public String getExamId();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_status</code>. 状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_status</code>. 状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_top_score</code>. 最高分数
     */
    public void setTopScore(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_top_score</code>. 最高分数
     */
    public Integer getTopScore();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_type</code>. 注册类型
     */
    public void setType(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_type</code>. 注册类型
     */
    public Integer getType();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_exam_times</code>. 考试次数
     */
    public void setExamTimes(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_exam_times</code>. 考试次数
     */
    public Integer getExamTimes();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_top_score_record_id</code>. 最高分的exam_record_id
     */
    public void setTopScoreRecordId(String value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_top_score_record_id</code>. 最高分的exam_record_id
     */
    public String getTopScoreRecordId();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_certificate_issue</code>. 是否已发放证书：0否 1是
     */
    public void setCertificateIssue(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_certificate_issue</code>. 是否已发放证书：0否 1是
     */
    public Integer getCertificateIssue();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_ticket</code>. 准考证
     */
    public void setTicket(String value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_ticket</code>. 准考证
     */
    public String getTicket();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_pass_status</code>. 及格状态，1：不及格，0：及格，2：已完成
     */
    public void setPassStatus(Integer value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_pass_status</code>. 及格状态，1：不及格，0：及格，2：已完成
     */
    public Integer getPassStatus();

    /**
     * Setter for <code>exam-stu.t_exam_regist_2024.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>exam-stu.t_exam_regist_2024.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IExamRegist_2024
     */
    public void from(IExamRegist_2024 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IExamRegist_2024
     */
    public <E extends IExamRegist_2024> E into(E into);
}
