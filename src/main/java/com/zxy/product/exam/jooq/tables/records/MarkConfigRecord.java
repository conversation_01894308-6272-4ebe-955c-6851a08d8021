/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.records;


import com.zxy.product.exam.jooq.tables.MarkConfig;
import com.zxy.product.exam.jooq.tables.interfaces.IMarkConfig;

import javax.annotation.Generated;

import com.zxy.product.exam.jooq.tables.pojos.MarkConfigEntity;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MarkConfigRecord extends UpdatableRecordImpl<MarkConfigRecord> implements Record6<String, Long, String, String, Integer, String>, IMarkConfig {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>exam.t_mark_config.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>exam.t_mark_config.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>exam.t_mark_config.f_exam_id</code>. 考试
     */
    @Override
    public void setExamId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_exam_id</code>. 考试
     */
    @Override
    public String getExamId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>exam.t_mark_config.f_member_id</code>. 评卷老师
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_member_id</code>. 评卷老师
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>exam.t_mark_config.f_type</code>. 评卷方式
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_type</code>. 评卷方式
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>exam.t_mark_config.f_type_id</code>. 类型ID （type:1,paperClassId)(type:2,questionType)(type3:questionId)
     */
    @Override
    public void setTypeId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>exam.t_mark_config.f_type_id</code>. 类型ID （type:1,paperClassId)(type:2,questionType)(type3:questionId)
     */
    @Override
    public String getTypeId() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Long, String, String, Integer, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Long, String, String, Integer, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return MarkConfig.MARK_CONFIG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return MarkConfig.MARK_CONFIG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return MarkConfig.MARK_CONFIG.EXAM_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return MarkConfig.MARK_CONFIG.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return MarkConfig.MARK_CONFIG.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return MarkConfig.MARK_CONFIG.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getExamId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value3(String value) {
        setExamId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value5(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord value6(String value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfigRecord values(String value1, Long value2, String value3, String value4, Integer value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMarkConfig from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setExamId(from.getExamId());
        setMemberId(from.getMemberId());
        setType(from.getType());
        setTypeId(from.getTypeId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMarkConfig> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MarkConfigRecord
     */
    public MarkConfigRecord() {
        super(MarkConfig.MARK_CONFIG);
    }

    /**
     * Create a detached, initialised MarkConfigRecord
     */
    public MarkConfigRecord(String id, Long createTime, String examId, String memberId, Integer type, String typeId) {
        super(MarkConfig.MARK_CONFIG);

        set(0, id);
        set(1, createTime);
        set(2, examId);
        set(3, memberId);
        set(4, type);
        set(5, typeId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof MarkConfigEntity)) {
            return false;
        }
        MarkConfigEntity pojo = (MarkConfigEntity)source;
        pojo.into(this);
        return true;
    }
}
