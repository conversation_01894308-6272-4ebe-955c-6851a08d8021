/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CloudLevelRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 移动云考试等级表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CloudLevel extends TableImpl<CloudLevelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_cloud_level</code>
     */
    public static final CloudLevel CLOUD_LEVEL = new CloudLevel();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CloudLevelRecord> getRecordType() {
        return CloudLevelRecord.class;
    }

    /**
     * The column <code>exam.t_cloud_level.f_id</code>. 主键
     */
    public final TableField<CloudLevelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_cloud_level.f_create_time</code>.
     */
    public final TableField<CloudLevelRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_cloud_level.f_level_code</code>. 等级编码
     */
    public final TableField<CloudLevelRecord, String> LEVEL_CODE = createField("f_level_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级编码");

    /**
     * The column <code>exam.t_cloud_level.f_level_name</code>. 等级名称
     */
    public final TableField<CloudLevelRecord, String> LEVEL_NAME = createField("f_level_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级名称");

    /**
     * The column <code>exam.t_cloud_level.f_level</code>. 等级 1,2,3,4,5
     */
    public final TableField<CloudLevelRecord, Integer> LEVEL = createField("f_level", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("1", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "等级 1,2,3,4,5");

    /**
     * The column <code>exam.t_cloud_level.f_valid_date</code>. 有效期（年）， 0代表长期有效
     */
    public final TableField<CloudLevelRecord, Integer> VALID_DATE = createField("f_valid_date", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "有效期（年）， 0代表长期有效");

    /**
     * Create a <code>exam.t_cloud_level</code> table reference
     */
    public CloudLevel() {
        this("t_cloud_level", null);
    }

    /**
     * Create an aliased <code>exam.t_cloud_level</code> table reference
     */
    public CloudLevel(String alias) {
        this(alias, CLOUD_LEVEL);
    }

    private CloudLevel(String alias, Table<CloudLevelRecord> aliased) {
        this(alias, aliased, null);
    }

    private CloudLevel(String alias, Table<CloudLevelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "移动云考试等级表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CloudLevelRecord> getPrimaryKey() {
        return Keys.KEY_T_CLOUD_LEVEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CloudLevelRecord>> getKeys() {
        return Arrays.<UniqueKey<CloudLevelRecord>>asList(Keys.KEY_T_CLOUD_LEVEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CloudLevel as(String alias) {
        return new CloudLevel(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CloudLevel rename(String name) {
        return new CloudLevel(name, null);
    }
}
