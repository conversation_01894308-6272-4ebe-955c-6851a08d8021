/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecord_2023Record;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 答案记录表_2023
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecord_2023 extends TableImpl<AnswerRecord_2023Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_answer_record_2023</code>
     */
    public static final AnswerRecord_2023 ANSWER_RECORD_2023 = new AnswerRecord_2023();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecord_2023Record> getRecordType() {
        return AnswerRecord_2023Record.class;
    }

    /**
     * The column <code>exam.t_answer_record_2023.f_id</code>.
     */
    public final TableField<AnswerRecord_2023Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_create_time</code>.
     */
    public final TableField<AnswerRecord_2023Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecord_2023Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_question_id</code>.
     */
    public final TableField<AnswerRecord_2023Record, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_answer</code>.
     */
    public final TableField<AnswerRecord_2023Record, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_is_right</code>.
     */
    public final TableField<AnswerRecord_2023Record, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_score</code>.
     */
    public final TableField<AnswerRecord_2023Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2023.f_modify_date</code>. 修改时间
     */
    public final TableField<AnswerRecord_2023Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_answer_record_2023</code> table reference
     */
    public AnswerRecord_2023() {
        this("t_answer_record_2023", null);
    }

    /**
     * Create an aliased <code>exam.t_answer_record_2023</code> table reference
     */
    public AnswerRecord_2023(String alias) {
        this(alias, ANSWER_RECORD_2023);
    }

    private AnswerRecord_2023(String alias, Table<AnswerRecord_2023Record> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecord_2023(String alias, Table<AnswerRecord_2023Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "答案记录表_2023");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecord_2023Record> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_2023_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecord_2023Record>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecord_2023Record>>asList(Keys.KEY_T_ANSWER_RECORD_2023_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecord_2023 as(String alias) {
        return new AnswerRecord_2023(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecord_2023 rename(String name) {
        return new AnswerRecord_2023(name, null);
    }
}
