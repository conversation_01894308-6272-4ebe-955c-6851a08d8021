/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.exam.jooq.tables.PaperClassQuestion;
import com.zxy.product.exam.jooq.tables.interfaces.IPaperClassQuestion;
import com.zxy.product.exam.jooq.tables.records.PaperClassQuestionRecord;

import javax.annotation.Generated;


/**
 * 试卷类与试题关联
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperClassQuestionEntity extends BaseEntity implements IPaperClassQuestion {

    private static final long serialVersionUID = 1L;

    private String  paperClassId;
    private String  questionId;
    private Integer score;
    private Integer sequence;
    private Integer isFromSelected;
    private String  parentId;

    public PaperClassQuestionEntity() {}

    public PaperClassQuestionEntity(PaperClassQuestionEntity value) {
        this.paperClassId = value.paperClassId;
        this.questionId = value.questionId;
        this.score = value.score;
        this.sequence = value.sequence;
        this.isFromSelected = value.isFromSelected;
        this.parentId = value.parentId;
    }

    public PaperClassQuestionEntity(
        String  id,
        String  paperClassId,
        String  questionId,
        Integer score,
        Integer sequence,
        Long    createTime,
        Integer isFromSelected,
        String  parentId
    ) {
        super.setId(id);
        this.paperClassId = paperClassId;
        this.questionId = questionId;
        this.score = score;
        this.sequence = sequence;
        super.setCreateTime(createTime);
        this.isFromSelected = isFromSelected;
        this.parentId = parentId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getPaperClassId() {
        return this.paperClassId;
    }

    @Override
    public void setPaperClassId(String paperClassId) {
        this.paperClassId = paperClassId;
    }

    @Override
    public String getQuestionId() {
        return this.questionId;
    }

    @Override
    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    @Override
    public Integer getScore() {
        return this.score;
    }

    @Override
    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public Integer getSequence() {
        return this.sequence;
    }

    @Override
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getIsFromSelected() {
        return this.isFromSelected;
    }

    @Override
    public void setIsFromSelected(Integer isFromSelected) {
        this.isFromSelected = isFromSelected;
    }

    @Override
    public String getParentId() {
        return this.parentId;
    }

    @Override
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PaperClassQuestionEntity (");

        sb.append(getId());
        sb.append(", ").append(paperClassId);
        sb.append(", ").append(questionId);
        sb.append(", ").append(score);
        sb.append(", ").append(sequence);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(isFromSelected);
        sb.append(", ").append(parentId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPaperClassQuestion from) {
        setId(from.getId());
        setPaperClassId(from.getPaperClassId());
        setQuestionId(from.getQuestionId());
        setScore(from.getScore());
        setSequence(from.getSequence());
        setCreateTime(from.getCreateTime());
        setIsFromSelected(from.getIsFromSelected());
        setParentId(from.getParentId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPaperClassQuestion> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PaperClassQuestionEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                PaperClassQuestionRecord r = new PaperClassQuestionRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.ID, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.PAPER_CLASS_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.PAPER_CLASS_ID, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.PAPER_CLASS_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.QUESTION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.QUESTION_ID, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.QUESTION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.SCORE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.SCORE, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.SCORE));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.SEQUENCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.SEQUENCE, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.SEQUENCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.CREATE_TIME, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.IS_FROM_SELECTED.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.IS_FROM_SELECTED, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.IS_FROM_SELECTED));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClassQuestion.PAPER_CLASS_QUESTION.PARENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClassQuestion.PAPER_CLASS_QUESTION.PARENT_ID, record.getValue(PaperClassQuestion.PAPER_CLASS_QUESTION.PARENT_ID));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
