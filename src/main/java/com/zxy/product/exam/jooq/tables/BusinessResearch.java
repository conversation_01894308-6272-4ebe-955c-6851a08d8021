/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.BusinessResearchRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 调研评估关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BusinessResearch extends TableImpl<BusinessResearchRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_business_research</code>
     */
    public static final BusinessResearch BUSINESS_RESEARCH = new BusinessResearch();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusinessResearchRecord> getRecordType() {
        return BusinessResearchRecord.class;
    }

    /**
     * The column <code>exam.t_business_research.f_id</code>.
     */
    public final TableField<BusinessResearchRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_business_research.f_business_id</code>.
     */
    public final TableField<BusinessResearchRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_business_research.f_business_type</code>. 资源类型  1:课程；2：专题，3：班级
     */
    public final TableField<BusinessResearchRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "资源类型  1:课程；2：专题，3：班级");

    /**
     * The column <code>exam.t_business_research.f_research_questionary_id</code>.
     */
    public final TableField<BusinessResearchRecord, String> RESEARCH_QUESTIONARY_ID = createField("f_research_questionary_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_business_research.f_create_time</code>.
     */
    public final TableField<BusinessResearchRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_business_research.f_source_id</code>. 源问卷id，对于选择的问卷，都会建立一个副本与业务数据关联
     */
    public final TableField<BusinessResearchRecord, String> SOURCE_ID = createField("f_source_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "源问卷id，对于选择的问卷，都会建立一个副本与业务数据关联");

    /**
     * Create a <code>exam.t_business_research</code> table reference
     */
    public BusinessResearch() {
        this("t_business_research", null);
    }

    /**
     * Create an aliased <code>exam.t_business_research</code> table reference
     */
    public BusinessResearch(String alias) {
        this(alias, BUSINESS_RESEARCH);
    }

    private BusinessResearch(String alias, Table<BusinessResearchRecord> aliased) {
        this(alias, aliased, null);
    }

    private BusinessResearch(String alias, Table<BusinessResearchRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "调研评估关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<BusinessResearchRecord> getPrimaryKey() {
        return Keys.KEY_T_BUSINESS_RESEARCH_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<BusinessResearchRecord>> getKeys() {
        return Arrays.<UniqueKey<BusinessResearchRecord>>asList(Keys.KEY_T_BUSINESS_RESEARCH_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusinessResearch as(String alias) {
        return new BusinessResearch(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public BusinessResearch rename(String name) {
        return new BusinessResearch(name, null);
    }
}
