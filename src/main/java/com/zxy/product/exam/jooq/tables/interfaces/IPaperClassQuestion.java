/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 试卷类与试题关联
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPaperClassQuestion extends Serializable {

    /**
     * Setter for <code>exam.t_paper_class_question.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>exam.t_paper_class_question.f_paper_class_id</code>. 试卷类ID
     */
    public void setPaperClassId(String value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_paper_class_id</code>. 试卷类ID
     */
    public String getPaperClassId();

    /**
     * Setter for <code>exam.t_paper_class_question.f_question_id</code>. 试题id
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_question_id</code>. 试题id
     */
    public String getQuestionId();

    /**
     * Setter for <code>exam.t_paper_class_question.f_score</code>. 状态(0：已发布 1：未发布)
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_score</code>. 状态(0：已发布 1：未发布)
     */
    public Integer getScore();

    /**
     * Setter for <code>exam.t_paper_class_question.f_sequence</code>.
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_sequence</code>.
     */
    public Integer getSequence();

    /**
     * Setter for <code>exam.t_paper_class_question.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_paper_class_question.f_is_from_selected</code>. 是否来自选择题目 0:否 1：是
     */
    public void setIsFromSelected(Integer value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_is_from_selected</code>. 是否来自选择题目 0:否 1：是
     */
    public Integer getIsFromSelected();

    /**
     * Setter for <code>exam.t_paper_class_question.f_parent_id</code>. 阅读题的父id
     */
    public void setParentId(String value);

    /**
     * Getter for <code>exam.t_paper_class_question.f_parent_id</code>. 阅读题的父id
     */
    public String getParentId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPaperClassQuestion
     */
    public void from(IPaperClassQuestion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPaperClassQuestion
     */
    public <E extends IPaperClassQuestion> E into(E into);
}
