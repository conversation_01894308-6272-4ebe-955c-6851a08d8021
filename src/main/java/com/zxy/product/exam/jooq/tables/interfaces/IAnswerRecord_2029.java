/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 答案记录表_2029
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAnswerRecord_2029 extends Serializable {

    /**
     * Setter for <code>exam.t_answer_record_2029.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_exam_record_id</code>.
     */
    public void setExamRecordId(String value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_exam_record_id</code>.
     */
    public String getExamRecordId();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_question_id</code>.
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_question_id</code>.
     */
    public String getQuestionId();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_answer</code>.
     */
    public void setAnswer(String value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_answer</code>.
     */
    public String getAnswer();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_is_right</code>.
     */
    public void setIsRight(Integer value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_is_right</code>.
     */
    public Integer getIsRight();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_score</code>.
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_score</code>.
     */
    public Integer getScore();

    /**
     * Setter for <code>exam.t_answer_record_2029.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>exam.t_answer_record_2029.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAnswerRecord_2029
     */
    public void from(IAnswerRecord_2029 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAnswerRecord_2029
     */
    public <E extends IAnswerRecord_2029> E into(E into);
}
