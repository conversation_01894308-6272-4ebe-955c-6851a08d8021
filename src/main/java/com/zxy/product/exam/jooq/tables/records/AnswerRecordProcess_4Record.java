/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.records;


import com.zxy.product.exam.jooq.tables.AnswerRecordProcess_4;
import com.zxy.product.exam.jooq.tables.interfaces.IAnswerRecordProcess_4;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 问题记录流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecordProcess_4Record extends UpdatableRecordImpl<AnswerRecordProcess_4Record> implements Record5<Long, String, String, String, Long>, IAnswerRecordProcess_4 {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>exam-stu.t_answer_record_process_4.id</code>.
     */
    @Override
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>exam-stu.t_answer_record_process_4.id</code>.
     */
    @Override
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>exam-stu.t_answer_record_process_4.f_exam_record_id</code>.
     */
    @Override
    public void setExamRecordId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>exam-stu.t_answer_record_process_4.f_exam_record_id</code>.
     */
    @Override
    public String getExamRecordId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>exam-stu.t_answer_record_process_4.f_question_id</code>.
     */
    @Override
    public void setQuestionId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>exam-stu.t_answer_record_process_4.f_question_id</code>.
     */
    @Override
    public String getQuestionId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>exam-stu.t_answer_record_process_4.f_answer</code>.
     */
    @Override
    public void setAnswer(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>exam-stu.t_answer_record_process_4.f_answer</code>.
     */
    @Override
    public String getAnswer() {
        return (String) get(3);
    }

    /**
     * Setter for <code>exam-stu.t_answer_record_process_4.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>exam-stu.t_answer_record_process_4.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<Long, String, String, String, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<Long, String, String, String, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field1() {
        return AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.EXAM_RECORD_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.QUESTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.ANSWER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getExamRecordId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getQuestionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getAnswer();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record value1(Long value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record value2(String value) {
        setExamRecordId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record value3(String value) {
        setQuestionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record value4(String value) {
        setAnswer(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4Record values(Long value1, String value2, String value3, String value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAnswerRecordProcess_4 from) {
        setId(from.getId());
        setExamRecordId(from.getExamRecordId());
        setQuestionId(from.getQuestionId());
        setAnswer(from.getAnswer());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAnswerRecordProcess_4> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AnswerRecordProcess_4Record
     */
    public AnswerRecordProcess_4Record() {
        super(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4);
    }

    /**
     * Create a detached, initialised AnswerRecordProcess_4Record
     */
    public AnswerRecordProcess_4Record(Long id, String examRecordId, String questionId, String answer, Long createTime) {
        super(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4);

        set(0, id);
        set(1, examRecordId);
        set(2, questionId);
        set(3, answer);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.exam.jooq.tables.pojos.AnswerRecordProcess_4Entity)) {
            return false;
        }
        com.zxy.product.exam.jooq.tables.pojos.AnswerRecordProcess_4Entity pojo = (com.zxy.product.exam.jooq.tables.pojos.AnswerRecordProcess_4Entity)source;
        pojo.into(this);
        return true;
    }
}
