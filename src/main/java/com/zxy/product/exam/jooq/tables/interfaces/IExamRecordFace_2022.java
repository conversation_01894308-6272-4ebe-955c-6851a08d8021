/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 人脸监考记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IExamRecordFace_2022 extends Serializable {

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_exam_record_id</code>.
     */
    public void setExamRecordId(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_exam_record_id</code>.
     */
    public String getExamRecordId();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_type</code>. 0：人脸进入 1：人脸监考 2：二机位认证进入 3：二机位认证中
     */
    public void setType(Integer value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_type</code>. 0：人脸进入 1：人脸监考 2：二机位认证进入 3：二机位认证中
     */
    public Integer getType();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_face_img_url</code>.
     */
    public void setFaceImgUrl(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_face_img_url</code>.
     */
    public String getFaceImgUrl();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_status</code>. 0：正常 1：异常 2：标记正常 3：标记异常 4：未检测
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_status</code>. 0：正常 1：异常 2：标记正常 3：标记异常 4：未检测
     */
    public Integer getStatus();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_resp_msg</code>.
     */
    public void setRespMsg(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_resp_msg</code>.
     */
    public String getRespMsg();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_exam_id</code>.
     */
    public void setExamId(String value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_exam_id</code>.
     */
    public String getExamId();

    /**
     * Setter for <code>exam.t_exam_record_face_2022.f_num</code>. 第几次采集（二机位认证用）
     */
    public void setNum(Integer value);

    /**
     * Getter for <code>exam.t_exam_record_face_2022.f_num</code>. 第几次采集（二机位认证用）
     */
    public Integer getNum();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IExamRecordFace_2022
     */
    public void from(IExamRecordFace_2022 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IExamRecordFace_2022
     */
    public <E extends IExamRecordFace_2022> E into(E into);
}
