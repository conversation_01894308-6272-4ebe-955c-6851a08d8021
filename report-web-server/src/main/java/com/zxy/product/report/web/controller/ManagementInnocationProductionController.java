package com.zxy.product.report.web.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.NativeWebRequest;

import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.report.api.ManagementInnocationProductionService;
import com.zxy.product.report.entity.ManagementInnocationProduction;
import com.zxy.product.report.entity.Member;
import com.zxy.product.report.web.util.BrowserUtil;

@Controller
@RequestMapping("/management-innocation-production")
public class ManagementInnocationProductionController {

	private ManagementInnocationProductionService managementInnocationProductionService;
	@Autowired
	public void setManagementInnocationProductionService(
			ManagementInnocationProductionService managementInnocationProductionService) {
		this.managementInnocationProductionService = managementInnocationProductionService;
	}
	public String get(NativeWebRequest request) {
		request.getNativeRequest(HttpServletRequest.class);
		return "";
	}
	@RequestMapping(value = "/list" , method = RequestMethod.GET)
    @Param(name = "activityTime", type = Integer.class,required = true)
    @Param(name = "type", type = Integer.class,required = true)// 参赛课程所属线条 1:十佳 2:最佳实践推广奖
    @JSON("options.*")
    @JSON("items.(id,title,coverPath,attachmentPath,briefContent,type,activityTime,isVote)")
	public PagedResult<ManagementInnocationProduction> findProductions(RequestContext context,Subject<Member> subject){
		return managementInnocationProductionService.find(context.getInteger("activityTime"),context.getInteger("type"),subject.getCurrentUserId());
	}

	@RequestMapping(value = "/vote" , method = RequestMethod.POST)
    @Param(name = "activityTime", type = Integer.class,required = true)
	@Param(name = "currentVoteNumber", type = Integer.class,required = true)
    @Param(name = "type", type = Integer.class,required = true)// 参赛课程所属线条 1:十佳 2:最佳实践推广奖
	@Param(name = "ids", type = String.class,required = true)
    @JSON("*")
	public int vote(RequestContext context,Subject<Member> subject) {
		return managementInnocationProductionService.vote(
				context.getInteger("currentVoteNumber"),
				context.getInteger("activityTime"),
				context.getInteger("type"),
				Arrays.asList(context.getString("ids").split(",")),
				subject.getCurrentUserId(),
				subject.getCurrentUser().getName());
	}
	@RequestMapping(value = "/vote-result" , method = RequestMethod.GET)
    @Param(name = "activityTime", type = Integer.class,required = true)
	@Param(name = "currentVoteNumber", type = Integer.class,required = true)
    @Param(name = "type", type = Integer.class,required = true)// 参赛课程所属线条 1:十佳 2:最佳实践推广奖
	@JSON("items.(id,title,votes,voteSum)")
	@JSON("raters.(*)")
	@JSON("realVoteNumber.(*)")
	public JSONObject findScoreResult(RequestContext context,Subject<Member> subject) {
		Map<List<String>, List<ManagementInnocationProduction>> result = managementInnocationProductionService
				.findVoteResult(context.getInteger("activityTime"),context.getInteger("currentVoteNumber"),context.getInteger("type"));
		JSONObject object = new JSONObject();
		List<String> raters = null;
		List<ManagementInnocationProduction> value = null;
		for(List<String> key : result.keySet()){
			raters = key;
			value = result.get(key);
			break;
        }
		object.put("raters",raters);
		object.put("items", value);
		int number = managementInnocationProductionService.getRealVoteNumber(context.getInteger("activityTime"),context.getInteger("type"));
		object.put("realVoteNumber", number);
		return object;
	}

	@RequestMapping(value = "/vote-export" , method = RequestMethod.GET)
    @Param(name = "activityTime", type = Integer.class,required = true)
	@Param(name = "currentVoteNumber", type = Integer.class,required = true)
    @Param(name = "type", type = Integer.class,required = true)// 参赛课程所属线条 1:十佳 2:最佳实践推广奖
	@JSON("items.(id,title,votes,voteSum)")
	@JSON("raters.(*)")
	public void export(RequestContext context,Subject<Member> subject) throws IOException {
	    String filename = "法律专家库课题投票数据.xls";
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
        }
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);

        HSSFWorkbook xlsx = new HSSFWorkbook();
        HSSFSheet sheet = xlsx.createSheet("法律专家库课题投票数据");
        CellStyle titleStyle = createCellStyle(xlsx);

		Map<List<String>, List<ManagementInnocationProduction>> result = managementInnocationProductionService
				.findVoteResult(context.getInteger("activityTime"),context.getInteger("currentVoteNumber"),context.getInteger("type"));

		// 头
		Row row=sheet.createRow(0);
		List<String> keys = null;
		for(List<String> key : result.keySet()){
			keys = key;
		}
		Cell cellName = row.createCell(0);
		cellName.setCellValue("序号");
		cellName.setCellStyle(titleStyle);

		Cell number = row.createCell(1);
		number.setCellValue("成果列表");
		number.setCellStyle(titleStyle);


        for(int i=0;i<keys.size();i++) {
        	String value = keys.get(i);
        	Cell cell = row.createCell((i+2));
        	cell.setCellValue(value);
        	cell.setCellStyle(titleStyle);
        }
        int size = keys.size() + 1,i=1;
        Cell cell1 = row.createCell(size + (i++));
        cell1.setCellValue("合计");
        cell1.setCellStyle(titleStyle);


    	// body
		List<ManagementInnocationProduction> res = result.get(keys);
		if(!res.isEmpty()) {
			for(int j =0;j<res.size();j++) {
				ManagementInnocationProduction re = res.get(j);
				String title = re.getTitle();
				List<Integer> votes = re.getVotes();
				int  voteSum = re.getVoteSum();
				Row rowTemp = sheet.createRow((j+1));
				rowTemp.createCell(0).setCellValue((j+1));
				// title
				rowTemp.createCell(1).setCellValue(title);
				// score
				for(int k = 0;k<votes.size() ;k++) {
					int value = votes.get(k);
					if(0 != value)
						rowTemp.createCell((k+2)).setCellValue(value+"");
					else
						rowTemp.createCell((k+2)).setCellValue("");
				}
				int bodySize = votes.size() + 1,h=1;
				// 总分 平均分
				rowTemp.createCell(bodySize + (h++)).setCellValue(voteSum+"");
			}
		}
		xlsx.write(response.getOutputStream());
	    response.getOutputStream().flush();
	    response.getOutputStream().close();
	}

	private CellStyle createCellStyle(Workbook workbook) {

		CellStyle style = workbook.createCellStyle();

		style.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());
    	style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    	style.setAlignment(HorizontalAlignment.CENTER);
    	style.setVerticalAlignment(VerticalAlignment.CENTER);

    	style.setBorderBottom(BorderStyle.THIN);
    	style.setBorderLeft(BorderStyle.THIN);
    	style.setBorderTop(BorderStyle.THIN);
    	style.setBorderRight(BorderStyle.THIN);

    	Font font = workbook.createFont();
    	font.setFontHeightInPoints((short) 11);
    	font.setBold(true);
    	style.setFont(font);
    	return style;
    }
}
