package com.zxy.product.report.web.util;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.TimeZone;

public class DateUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static final String YYYYMMDD = "yyyyMMdd";

    public static final long TIME = 86400000L;

    public static Long dateStringYYYYMMDD2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD);
    }

    public static Long dateStringYYYYMMDDHHMMSS2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD_HH_MM_SS);
    }

    public static Long dateStringYYYYMMDDHHMM2Long(String date) {
    	return dateString2Long(date, YYYY_MM_DD_HH_MM);
    }

    public static Long dateString2Long(String date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            LOGGER.error("string to date error", e);
        }

        return d.getTime();
    }

    public static Optional<Long> dateStringYYYYMMDDHHMMSS2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDDHHMMSS2Long(d);
        });
    }

    public static Optional<Long> dateStringYYYYMMDD2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDD2Long(d);
        });
    }

    /**
     * Long类型日期转String类型
     * @param dateLong
     * @param format
     * @return
     */
    public static String dateLongToString(Long dateLong,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(dateLong);
        return sdf.format(date);
    }

    /**
     * 秒数转成时间时长显示
     * @param seconds
     * @return
     */
    public static String secondsToString(Integer seconds){
        String time = "00:00:00";
        Integer minute = 0; // 分
        Integer hour = 0; // 小时
        if(seconds == null || seconds.equals(0)) return "-";
        if (seconds > 60) {
            minute = seconds / 60;
            seconds = seconds % 60;
            if (minute > 60) {
                hour = minute / 60;
                minute = minute % 60;
            }
        }
        time = seconds.toString();
        if (minute > 0) {
            time = String.format("%02d", minute) + ":" + String.format("%02d", seconds);
        } else {
            time = "00:" + String.format("%02d", seconds);
        }
        if (hour > 0) {
            time = String.format("%02d", hour) + ":" + time;
        } else {
            time = "00:" + time;
        }
        return time;
    }


    /**
     * 秒数转成时间时长显示 X时X分
     * @param seconds
     * @return
     */
    public static String secondsToHour(Integer seconds){
        String time = "0";
        Integer minute = 0; // 分
        Integer hour = 0; // 小时
        if (seconds > 60) {
            minute = seconds / 60;
            seconds = seconds % 60;
            if (minute > 60) {
                hour = minute / 60;
                minute = minute % 60;
            }
        }
        if (minute > 0) {
            time = minute + "分";
        }
        if (hour > 0) {
            time = hour + "小时" + time;
        }
        return time;
    }

    public static long getTimesmorning(){
        long current=System.currentTimeMillis();//当前时间毫秒数
        long zero=current/(1000*3600*24)*(1000*3600*24)-TimeZone.getDefault().getRawOffset();//今天零点零分零秒的毫秒数
        return zero;
    }
    //获得当天24点时间
    public static long getTimesnight(){
        long current=System.currentTimeMillis();//当前时间毫秒数
        long zero=current/(1000*3600*24)*(1000*3600*24)-TimeZone.getDefault().getRawOffset();//今天零点零分零秒的毫秒数
        long twelve=zero+24*60*60*1000-1;//今天23点59分59秒的毫秒数

        return twelve;
    }

    /**
     * 获取今天0点0时0秒
     */
    public static Optional<Long> today(Optional<Long> time) {
        return time.map(t -> {
            Calendar c1 = Calendar.getInstance();
            c1.setTimeInMillis(t);
            c1.set(Calendar.HOUR, 0);
            c1.set(Calendar.MINUTE, 0);
            c1.set(Calendar.SECOND, 0);
            c1.set(Calendar.MILLISECOND, 0);
            return c1.getTimeInMillis();
        });
    }

    /**
     * 获取明天0点0时0秒
     */
    public static Optional<Long> tomorrow(Optional<Long> time) {
        return time.map(t -> {
            Calendar c1 = Calendar.getInstance();
            c1.setTimeInMillis(t + (1000 * 60 * 60 * 24));
            c1.set(Calendar.HOUR, 0);
            c1.set(Calendar.MINUTE, 0);
            c1.set(Calendar.SECOND, 0);
            c1.set(Calendar.MILLISECOND, 0);
            return c1.getTimeInMillis();
        });
    }

    /**
     * 将 Optional<String> 转换为 Optional<java.sql.Date>
     * @param dateStr
     * @return
     */
    public static Optional<java.sql.Date> dateStringYYYYMMDD2OptionalDate(Optional<String> dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);

        if (!dateStr.isPresent()) {
            return Optional.empty();
        }

        Date utDate = null;
        try {
            utDate = sdf.parse(dateStr.get());
            java.sql.Date ret = new java.sql.Date(utDate.getTime());
            return Optional.of(ret);
        } catch (Exception e) {
            e.printStackTrace();
            return Optional.empty();
        }
    }
    
    public static int intToday(String pattern){
    	return Integer.parseInt(new DateTime().toString(pattern));
    }

    public static String strToday(String pattern){
        return new DateTime().toString(pattern);
    }


    /**
     * 毫秒时间戳转字符串
     * @param value
     * @return
     */
    public  static   String  dateTransition(Long value) {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        return format.format(value);
    }
    /**
     * 字符串转毫秒时间戳
     * @param value
     * @return
     */
    public  static   Long   dateTransition(String value){
        Calendar c = Calendar.getInstance();
        try {
            c.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(value));
        } catch (ParseException e) {
            LOGGER.error("字符串转毫秒异常,异常信息:{}",e.getMessage());
            throw new RuntimeException(e);
        }
        return c.getTimeInMillis();
    }


    /**
     * 1天前的dey
     * @return
     */
    public static String getMonthDaysAgoDayStr() {
        return longToDateString(System.currentTimeMillis() - TIME, YYYYMMDD);
    }

    /**
     * 8天前的day
     * @return
     */
    public static String getMonthEightDaysAgoDayStr() {
        return longToDateString(System.currentTimeMillis() - (TIME * 8), YYYYMMDD);
    }


    public static String longToDateString(Long date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date d = new Date(date);
        return sdf.format(d);
    }

    public static String getMonthDayStr() {
        return longToDateString(System.currentTimeMillis(), YYYYMMDD);
    }

    public static Integer getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (int) (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }
}
