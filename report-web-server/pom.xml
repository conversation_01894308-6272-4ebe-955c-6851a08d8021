<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zxy-mobile-report</artifactId>
        <groupId>com.zxy.product</groupId>
        <version>cmu-9.6.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>report-web-server</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>web-server-parent</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>report-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-stu-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>human-resource-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
        </dependency>
        <dependency>
	        <groupId>joda-time</groupId>
	        <artifactId>joda-time</artifactId>
	    </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>course-study-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>train-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>ask-bar-api</artifactId>
            <version>${version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.sun.jersey.contribs/jersey-multipart -->
        <dependency>
            <groupId>com.sun.jersey.contribs</groupId>
            <artifactId>jersey-multipart</artifactId>
            <version>1.19</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.sun.jersey/jersey-client -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>1.19</version>
        </dependency>

        <dependency>
            <groupId>io.springside</groupId>
            <artifactId>springside-utils</artifactId>
            <version>5.0.0-RC1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.25</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.3.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>


</project>
