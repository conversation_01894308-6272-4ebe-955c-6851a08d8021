
package com.zxy.product.human.web.controller;

import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.api.OrganizationService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.human.entity.Organization;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/organization")
public class OrganizationController {

    private OrganizationService organizationService;
    private MemberService memberService;

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    /** 判断组织关联 */
    @RequestMapping(value = "/judge-org-releated", method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @JSON("*")
    public Map<String,Object> judgeOrganizationReleated(RequestContext requestContext) {
    	return organizationService.judgeOrgReleated(requestContext.getString("organizationId"));
    }

    /**
     * 查询单个组织
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{organizationId}", method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @JSON("id, name, shortName, code, parentId, parentName, cmccLevel, level, order, cmccAttribute, cmccCategory, status")
    public Organization get(RequestContext requestContext){
        return organizationService.get(requestContext.get("organizationId", String.class)).orElse(null);
    }

    /**
     * 查询组织所属单位
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/company", method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @JSON("id, name, shortName, code, parentId, parentName, cmccLevel, level, order, cmccAttribute, cmccCategory, status")
    public Organization getCompany(RequestContext requestContext){
        return organizationService.getCompanyOrganization(requestContext.get("organizationId", String.class), 2, 3);
    }

    @RequestMapping(value = "/checkName", method = RequestMethod.GET)
    @Param(name = "name", type = String.class, required = true)
    @JSON("*")
    public int checkName(RequestContext requestContext, Subject<Member> subject){
        String name = requestContext.get("name", String.class);
        String organizationId = memberService.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId()).getId();
        return organizationService.count(organizationId, Optional.of(name), Optional.empty());
    }

    @RequestMapping(value = "/checkCode", method = RequestMethod.GET)
    @Param(name = "code", type = String.class, required = true)
    @JSON("*")
    public int checkCode(RequestContext requestContext, Subject<Member> subject){
        String code = requestContext.get("code", String.class);
        String organizationId = memberService.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId()).getId();
        return organizationService.count(organizationId, Optional.empty(), Optional.of(code));
    }

}
