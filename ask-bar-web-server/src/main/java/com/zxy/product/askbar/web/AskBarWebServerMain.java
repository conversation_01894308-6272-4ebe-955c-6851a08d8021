package com.zxy.product.askbar.web;

import com.zxy.product.askbar.web.config.*;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.web.*;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

import com.zxy.common.restful.websocket.WebSocketConfig;
import com.zxy.product.askbar.web.config.RPCClientConfig;

//@SpringBootApplication
@Configuration
@ComponentScan
@EnableAspectJAutoProxy
@Import({
        RestfulConfig.class,
        RPCClientConfig.class,
        WebConfig.class,
        MessageConfig.class,
        CacheConfig.class,
        WebSocketConfig.class,
        RabbitAutoConfiguration.class,
        EmbeddedServletContainerAutoConfiguration.class,
        DispatcherServletAutoConfiguration.class,
        HttpEncodingAutoConfiguration.class,
        HttpMessageConvertersAutoConfiguration.class,
        JacksonAutoConfiguration.class,
        MultipartAutoConfiguration.class,
        ServerPropertiesAutoConfiguration.class,
        PropertyPlaceholderAutoConfiguration.class,
        WebMvcAutoConfiguration.class,
        ErrorMvcAutoConfiguration.class,
        FastDFSConfig.class
})
public class AskBarWebServerMain {
    public static void main(String[] args) {
        SpringApplication.run(AskBarWebServerMain.class, args);
    }
}
