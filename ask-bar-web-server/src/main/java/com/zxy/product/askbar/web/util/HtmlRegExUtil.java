package com.zxy.product.askbar.web.util;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HtmlRegExUtil {
    public static final List<String> regExList;

    static {
        // String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式
        // String regEx_html = "<[^>]+>"; //定义HTML标签的正则表达式
        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
        // String regEx_img = "<img[^>]*?>[/]*?"; // 定义style的正则表达式
        regExList = Lists.newArrayList();
        regExList.add(regEx_script);
        // regExList.add(regEx_img);
    }


    public static String regExHtml(String content) {
        for (String regEx : regExList) {
            Pattern p_script = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
            Matcher m_script = p_script.matcher(content);
            content = m_script.replaceAll(""); // 过滤标签
        }
        return content;
    }

/*     public static void main(String[] args) {
        String content = "<a href = \"http://<script>alert(0)</script>www.baidu.com<img src=\"www.baidu.com\"><script>alert(0)</script><img src=\"www.baidu.com\">\"> </a>";
        System.out.println(regExHtml(content));
    } */
}
