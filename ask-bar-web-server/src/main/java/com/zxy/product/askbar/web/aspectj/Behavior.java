package com.zxy.product.askbar.web.aspectj;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/8/30
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Behavior {
    /**
     * 内容id
     */
    String contentID() default "";

    /**
     * 内容类型
     */
    String contentType() default "";

    /**
     * 页面来源
     */
    String pageSource() default "";

    /**
     * 类型
     */
    String type() default "";

    /**
     * 点击类型
     * @return
     */
    String clickType() default "";

    /*private String userId;
    private String contentId;
    private String contentType;
    private String contentName;
    private String clientType;
    private String type;
    private String value;
    private String pageSource;
    private String status;
    private Long   modifiTime;*/

}
