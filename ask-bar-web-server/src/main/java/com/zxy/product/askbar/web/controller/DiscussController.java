package com.zxy.product.askbar.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.askbar.api.DiscussService;
import com.zxy.product.askbar.content.ErrorCode;
import com.zxy.product.askbar.entity.Discuss;
import com.zxy.product.askbar.entity.Member;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.system.api.operation.SpeechSetService;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.entity.RuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @date 2017年7月31日 下午3:53:39
 *
 */
@Controller
@RequestMapping("/discuss")
public class DiscussController {

    private static final Logger log = LoggerFactory.getLogger(DiscussController.class);
    private DiscussService discussService;
    private SpeechSetService speechSetService;
    private MemberService memberService;
    private Cache cache;
    private final static Integer DISCUSS_CACHE_DEFAULT = 1;
    private final static int SENSITIVE=1;//是否过滤词0：否，1：是
    private RuleConfigService ruleConfigService;

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }
    @Autowired
    public void setDiscussService(DiscussService discussService) {
        this.discussService = discussService;
    }

    @Autowired
    public void setSpeechSetService(SpeechSetService speechSetService) {
        this.speechSetService = speechSetService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("ask-bar", "discuss");
    }

    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }

    /**
     * 讨论基本信息
     */
    @Permitted
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("id,type,content,contentText,auditStatus,img,praiseNum,replyNum,createTime,discussId,toDiscussId,isCreator,answerSourceName,answerSourceUrl,sourceName")
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath, isManager)") // who create
    @JSON("praise.(id)") // 点赞
    public Discuss get(RequestContext requestContext, Subject<Member> subject) {
        return discussService.get(requestContext.getString("id"), subject.getCurrentUserId());
    }

    /**
     * 参与讨论（问题、文章、分享）
     */
    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "questionId", type = String.class, required = true)
    @Param(name = "content", type = String.class, required = true)
    @Param(name = "contentText", type = String.class)
    @Param(name = "img", type = String.class)
    @Param(name = "sensitive", type = Integer.class)//是否敏感词
    @JSON("id,questionId,auditStatus,type,content,contentText,img,praiseNum,replyNum,createTime,isCreator,topStatus")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss insertDiscuss(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        String questionId = requestContext.getString("questionId");
        // 判断缓存中是否有值防止重复发表讨论
        if (null == cache.get(join(currentMemberId, questionId), Integer.class)) {
            // 问吧讨论规则优化，3分钟内不得重复发表讨论
            cache.set(join(currentMemberId,questionId), DISCUSS_CACHE_DEFAULT, 180);
        } else {
            throw new UnprocessableException(ErrorCode.ProhibitionOfRepeatedDiscussion);
        }
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Integer redStatus = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_RED_BOAT_AUDIT, rootOrganizationId);

        boolean flag = false;
        if (status == SpeechSetService.STATUS_ON){
            flag = true;
        }else if (redStatus == SpeechSetService.STATUS_ON){
            flag = true;
        }

        Integer sensitive = requestContext.get("sensitive",Integer.class);
        log.info("insertDiscuss sensitive:"+sensitive);
        status = sensitive.intValue() == SENSITIVE ? SENSITIVE:status;
        log.info("insertDiscuss status:"+status);
        Discuss discuss = discussService.insert(
                questionId,
                requestContext.getString("content"),
                requestContext.getOptionalString("contentText"),
                requestContext.getOptionalString("img"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                flag,
                requestContext.get("sensitive",Integer.class)
                                               );
        return discuss;
    }

    /**
     * 讨论修改（问题、文章、分享）
     */
    @RequestMapping(value = "/update-discuss/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @Param(name = "content", type = String.class, required = true)
    @Param(name = "contentTxt", type = String.class)
    @Param(name = "img", type = String.class)
    @JSON("id,questionId,auditStatus,type,content,contentText,img,praiseNum,replyNum,createTime,isCreator,topStatus")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss updateDiscuss(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Integer redStatus = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_RED_BOAT_AUDIT, rootOrganizationId);

        boolean flag = false;
        if (status == SpeechSetService.STATUS_ON){
            flag = true;
        }else if (redStatus == SpeechSetService.STATUS_ON){
            flag = true;
        }
        Discuss discuss = discussService.update(
                requestContext.getString("id"),
                requestContext.getString("questionId"),
                requestContext.getString("content"),
                requestContext.getOptionalString("contentTxt"),
                requestContext.getOptionalString("img"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                flag
        );
        return discuss;
    }

    /**
     * 讨论修改（问题、文章、分享）
     */
    @RequestMapping(value = "/update-discuss-app", method = RequestMethod.POST)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @Param(name = "content", type = String.class, required = true)
    @Param(name = "img", type = String.class)
    @JSON("id,questionId,auditStatus,type,content,contentText,img,praiseNum,replyNum,createTime,isCreator,topStatus")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss updateDiscussApp(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Discuss discuss = discussService.update(
                requestContext.getString("id"),
                requestContext.getString("questionId"),
                requestContext.getString("content"),
                requestContext.getOptionalString("contentTxt"),
                requestContext.getOptionalString("img"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                status == SpeechSetService.STATUS_ON
                                               );
        return discuss;
    }

    /**
     * 参与回复
     */
    @Permitted
    @RequestMapping(value = "/reply", method = RequestMethod.POST)
    @Param(name = "discussId", type = String.class, required = true)
    @Param(name = "contentText", type = String.class, required = true)
    @Param(name = "sensitive", type = Integer.class)//是否敏感词
    @JSON("id,questionId,auditStatus,type,content,contentText,discussId,toDiscussId,img,praiseNum,replyNum,createTime,isCreator")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    @JSON("toMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss insertReply(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        String discussId = requestContext.getString("discussId");
        // 判断缓存中是否有值防止重复发表讨论
        if (null == cache.get(join(currentMemberId, discussId), Integer.class)) {
            // 问吧讨论规则优化，3分钟内不得重复发表讨论
            cache.set(join(currentMemberId,discussId), DISCUSS_CACHE_DEFAULT, 180);
        } else {
            throw new UnprocessableException(ErrorCode.ProhibitionOfRepeatedDiscussion);
        }
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Integer redStatus = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_RED_BOAT_AUDIT, rootOrganizationId);

        boolean flag = false;
        if (status == SpeechSetService.STATUS_ON){
            flag = true;
        }else if (redStatus == SpeechSetService.STATUS_ON){
            flag = true;
        }

        Integer sensitive = requestContext.get("sensitive",Integer.class);
        log.info("insertReply sensitive:"+sensitive);
        status = sensitive.intValue() == SENSITIVE ? SENSITIVE:status;
        log.info("insertReply status:"+status);
        Discuss discuss = discussService.insertReply(
                discussId,
                requestContext.getString("contentText"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                flag,
                sensitive);
        return discuss;
    }

    /**
     * 修改回复
     */
    @Permitted
    @RequestMapping(value = "/update-reply/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class)
    @Param(name = "discussId", type = String.class, required = true)
    @Param(name = "contentText", type = String.class, required = true)
    @JSON("id,questionId,auditStatus,type,content,contentText,discussId,toDiscussId,img,praiseNum,replyNum,createTime,isCreator")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    @JSON("toMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss updateQuestion(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Discuss discuss = discussService.updateReply(
                requestContext.getString("id"),
                requestContext.getString("discussId"),
                requestContext.getString("contentText"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                status == SpeechSetService.STATUS_ON
                                                    );
        return discuss;
    }


    /**
     * 修改回复
     */
    @Permitted
    @RequestMapping(value = "/update-reply-app", method = RequestMethod.POST)
    @Param(name = "id", type = String.class)
    @Param(name = "discussId", type = String.class, required = true)
    @Param(name = "contentText", type = String.class, required = true)
    @JSON("id,questionId,auditStatus,type,content,contentText,discussId,toDiscussId,img,praiseNum,replyNum,createTime,isCreator")
    @JSON("praise.(id)") // 点赞
    @JSON("createMember.(id,fullName,headPortrait,headPortraitPath)")
    @JSON("toMember.(id,fullName,headPortrait,headPortraitPath)")
    public Discuss updateQuestionApp(RequestContext requestContext, Subject<Member> subject) {
        String currentMemberId = subject.getCurrentUserId();
        Member createMember = subject.getCurrentUser();
        String organizationId = createMember.getOrganizationId();
        String rootOrganizationId = createMember.getRootOrganization().getId();
        // 判断发表讨论是否需要审核
        Integer status = speechSetService.getStatusByCode(SpeechSetService.SET_CODE_BAR_DISCUSS, rootOrganizationId);
        Discuss discuss = discussService.updateReply(
                requestContext.getString("id"),
                requestContext.getString("discussId"),
                requestContext.getString("contentText"),
                currentMemberId,
                organizationId,
                rootOrganizationId,
                status == SpeechSetService.STATUS_ON
                                                    );
        return discuss;
    }

    /**
     * 分页查询内容下的讨论（问题、文章、分享）
     */
    @RequestMapping(method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("more")
    @JSON("items.(id,questionId,type,auditStatus,topStatus,essenceStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.organization.(id,name)") // 组织
    public Map<String,Object> findPageDiscuss(RequestContext requestContext, Subject<Member> subject){
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        Integer start = (page - 1) * pageSize;
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        return discussService.findPageDiscussSwitch(
                start,
                requestContext.getInteger("pageSize"),
                requestContext.getString("questionId"),
                subject.getCurrentUserId(),
                pageSwitch == 1);
    }

    /**
     * 分页查询内容下的讨论（问题、文章、分享）加密脱敏版
     */
    @RequestMapping(method = RequestMethod.GET, value = "/new")
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("more")
    @JSON("items.(id,questionId,type,auditStatus,topStatus,essenceStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.organization.(id,name)") // 组织
    public Map<String,Object> findPageDiscussNew(RequestContext requestContext, Subject<Member> subject){
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        Integer start = (page - 1) * pageSize;
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        return discussService.findPageDiscussSwitchNew(
                start,
                requestContext.getInteger("pageSize"),
                requestContext.getString("questionId"),
                subject.getCurrentUserId(),
                pageSwitch == 1);
    }


    /**
     * 分页查询内容下的讨论（问题、文章、分享）
     */
    @RequestMapping(value = "/common", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,questionId,type,auditStatus,topStatus,essenceStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.organization.(id,name)") // 组织
    public PagedResult<Discuss> findPageDiscussCommon(RequestContext requestContext){
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        Integer start = (page - 1) * pageSize;
        return discussService.findPageDiscussCommon(
                start,
                requestContext.getInteger("pageSize"),
                requestContext.getString("questionId"));

    }

    /**
     * 分页查询内容下的讨论（问题、文章、分享）
     */
    @RequestMapping(value = "/start-page", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "questionId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,questionId,type,auditStatus,topStatus,essenceStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.organization.(id,name)") // 组织
    public PagedResult<Discuss> findPageDiscussStart(RequestContext requestContext, Subject<Member> subject){

        return discussService.findPageDiscuss(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                requestContext.getString("questionId"),
                subject.getCurrentUserId());
    }

    /**
     * 分页查询回复（讨论的回复、回复的回复）
     */
    @RequestMapping(value = "/reply", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "discussId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,questionId,discussId,toDiscussId,type,auditStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath)")
    @JSON("items.toMember.(id,fullName,headPortrait,headPortraitPath)")
    public PagedResult<Discuss> findPageReply(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findPageReply(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                requestContext.getString("discussId"),
                subject.getCurrentUserId());
    }



    /**
     * 分页查询回复（讨论的回复、回复的回复）加密脱敏版
     */
    @RequestMapping(value = "/reply-new", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "discussId", type = String.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,questionId,discussId,toDiscussId,type,auditStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath)")
    @JSON("items.toMember.(id,fullName,headPortrait,headPortraitPath)")
    public PagedResult<Discuss> findPageReplyNew(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findPageReplyNew(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                requestContext.getString("discussId"),
                subject.getCurrentUserId());
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Map<String, Object> deleteFront(RequestContext requestContext, Subject<Member> subject) {
        return ImmutableMap.of("count", discussService.delete(requestContext.get("id", String.class), subject.getCurrentUserId()));
    }

    /**
     * 删除(管理端删除)
     */
    @RequestMapping(value = "/manage/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Map<String, Object> delete(RequestContext requestContext, Subject<Member> subject) {
        return ImmutableMap.of("count", discussService.deleteManage(requestContext.get("id", String.class), subject.getCurrentUserId()));
    }

    /**
     * 我是专家-我的回答
     */
    @RequestMapping(value = "/my-discuss", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,questionId,type,auditStatus,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // who create
    @JSON("items.toMember.(id,fullName,headPortrait,headPortraitPath)") // to who
    @JSON("items.question.(id,title,type)") // 内容
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.audit.(id,auditNote)") // 审核记录
    public PagedResult<Discuss> findMyDiscuss(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findMyDiscuss(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                subject.getCurrentUserId());
    }

    /**
     * 专家回答
     */
    @RequestMapping(value = "/expert-discuss", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "expertMemberId", type = String.class, required = true) // 专家用户id
    @JSON("recordCount")
    @JSON("items.(id,type,questionId,content,contentText,img,praiseNum,replyNum,createTime,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.question.(id,title,type,isManager)") // 内容
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)") // 关联话题
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.praise.(id)") // 点赞
    public PagedResult<Discuss> findExpertDiscuss(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findExpertDiscuss(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                requestContext.getString("expertMemberId"),
                subject.getCurrentUserId());
    }

    /**
     * 我的回复
     */
    @RequestMapping(value = "/my-reply", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @Param(name = "timeOrder", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,type,content,contentText,auditStatus,img,praiseNum,replyNum,createTime,discussId,toDiscussId,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath)") // who create
    @JSON("items.toMember.(id,fullName,headPortrait，headPortraitPath)") // to who
    @JSON("items.question.(id,title,type)") //内容
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.audit.(id,auditNote)") // 审核记录
    public PagedResult<Discuss> findMyReplyList(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findMyReply(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                requestContext.getOptionalInteger("auditStatus"),
                subject.getCurrentUserId(),
                requestContext.getOptionalString("timeOrder"));
    }

    /**
     * 个人中心-我的回复
     */
    @RequestMapping(value = "/my-reply-page", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @Param(name = "timeOrder", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,type,content,contentText,auditStatus,img,praiseNum,replyNum,createTime,discussId,toDiscussId,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath)") // who create
    @JSON("items.toMember.(id,fullName,headPortrait,headPortraitPath)") // to who
    @JSON("items.question.(id,title,type)") //内容
    @JSON("items.praise.(id)") // 点赞
    @JSON("items.audit.(id,auditNote)") // 审核记录
    public PagedResult<Discuss> findMyReplyListPage(RequestContext requestContext, Subject<Member> subject) {
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        return discussService.findMyReply((page - 1) * pageSize,
                                          pageSize,
                                          requestContext.getOptionalInteger("auditStatus"),
                                          subject.getCurrentUserId(),
                                          requestContext.getOptionalString("timeOrder"));
    }

    /**
     * 兼岗-个人中心-我的回复
     */
    @RequestMapping(value = "/my-reply-page-merge", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "auditStatus", type = Integer.class)
    @Param(name = "timeOrder", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,type,content,contentText,auditStatus,img,praiseNum,replyNum,createTime,discussId,toDiscussId,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath)") // who create
    @JSON("items.toMember.(id,fullName,headPortrait,headPortraitPath)") // to who
    @JSON("items.question.(id,title,type)") //内容
    @JSON("items.praise.(id,createMemberId)") // 点赞
    @JSON("items.audit.(id,auditNote)") // 审核记录
    public PagedResult<Discuss> findMyReplyListPageMerge(RequestContext requestContext, Subject<Member> subject) {
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        List<String> currentUserIds = memberService.getMembersByUserId(subject.getCurrentUserId());
        return discussService.findMyReplyMerge((page - 1) * pageSize,
                                          pageSize,
                                          requestContext.getOptionalInteger("auditStatus"),
                                          currentUserIds,
                                          requestContext.getOptionalString("timeOrder"));
    }

    /**
     * 回复我的
     */
    @RequestMapping(value = "/reply-me", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,type,content,contentText,auditStatus,img,praiseNum,replyNum,createTime,discussId,toDiscussId,isCreator,answerSourceName,answerSourceUrl,sourceName)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // who create
    @JSON("items.toMember.(id,fullName,headPortrait，headPortraitPath)") // to who
    @JSON("items.question.(id,title,type)") //内容
    @JSON("items.praise.(id)") // 点赞
    public PagedResult<Discuss> findMyArticleList(RequestContext requestContext, Subject<Member> subject) {
        return discussService.findReply2Me(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                subject.getCurrentUserId());
    }

    /** 加精 */
    @RequestMapping(value = "/essence/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Discuss essence(RequestContext requestContext, Subject<Member> subject) {
        return discussService.essence(requestContext.get("id", String.class), subject.getCurrentUserId());
    }

    /** 取消加精 */
    @RequestMapping(value = "/unessence/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Discuss unessence(RequestContext requestContext, Subject<Member> subject) {
        return discussService.unessence(requestContext.get("id", String.class), subject.getCurrentUserId());
    }

    /** 置顶  */
    @RequestMapping(value = "/top/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Discuss top(RequestContext requestContext, Subject<Member> subject) {
        return discussService.top(requestContext.get("id", String.class), subject.getCurrentUserId());
    }

    /** 取消置顶  */
    @RequestMapping(value = "/untop/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public Discuss untop(RequestContext requestContext, Subject<Member> subject) {
        return discussService.untop(requestContext.get("id", String.class), subject.getCurrentUserId());
    }

    /**
     * 查询所有问吧下的评论 -- 运营管理-网评管理-问吧
     */
    @RequestMapping(value = "/find-discuss", method = RequestMethod.GET)
    @Permitted(perms = {"operation/comment-manage","operation/direct-train-comment-manage"})
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "type", type = Integer.class, required = true)//子级父级
    @Param(name = "content")//评论关键词
    @Param(name = "businessName")//来源明细
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    @JSON("recordCount")
    @JSON("items.(id, contentText, createTime, source, redBoatQuestionAuditStatus, redBoatQuestionToReview, redBoatCreateTime)")
    @JSON("items.question.(id, title)")
    @JSON("items.createMember.(id, name, fullName)")
    @JSON("items.organization.(id, name)")
    public PagedResult<Discuss> findDiscuss(RequestContext context){
        return discussService.findDiscuss(
                    context.getInteger("page"),
                    context.getInteger("pageSize"),
                    context.getInteger("type"),
                    context.getOptionalString("content"),
                    context.getOptionalString("businessName"),
                    context.getOptionalLong("startTime"),
                    context.getOptionalLong("endTime")
                );
    }

    /**
     * 运营管理-网评管理-问吧 删除
     */
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    public Map<String, Object> deleteDiscuss(RequestContext requestContext) {
        return ImmutableMap.of("count", discussService.deleteDiscuss(requestContext.getString("id")));
    }

    /**
     * 根据问吧ID查询点赞数最多的评论
     */
    @RequestMapping(value = "/top-praised", method = RequestMethod.GET)
    @Permitted
    @Param(name = "questionIds", type = String.class, required = true)
    @JSON("id,questionId,contentText,praiseNum,createTime")
    public List<Discuss> findTopPraisedDiscussByQuestionIds(RequestContext requestContext) {
        String questionIds = requestContext.getString("questionIds");
        return discussService.findTopPraisedDiscussByQuestionIds(questionIds);
    }

    private static String join(String f, String s) {
        return f + "#" + s;
    }
}
